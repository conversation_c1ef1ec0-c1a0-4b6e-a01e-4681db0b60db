package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"share-live/common"
	"share-live/model"
	"share-live/request"
	"share-live/service"
	"share-live/setting"
	smallShopRequest "small-shop/request"
	smallShopService "small-shop/service"
	"small-shop/service/tools"
	"strconv"
	ufv1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

// createShareLiveCategory
// @Tags 共享直播
// @Summary 定时任务--定时获取直播中的 峰值和流量
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "定时任务--定时获取直播中的 峰值和流量"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/saveShareLiveRoom [get]
func SynShareLive(c *gin.Context) {
	//var shareLiveRoomIdRequest request.ShareLiveRoomIdRequest
	//_ = c.ShouldBindQuery(&shareLiveRoomIdRequest)

	err := service.SynShareLive()
	if err != nil {
		//log.Log().Error(err.Error(), zap.Any("err", err))
		//yzResponse.FailWithMessage(err.Error(), c)
	}
}

// @Tags 共享直播API
// @Summary 获取同步状态
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取同步状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/getSysShareLiveSetting [get]
func GetSysShareLiveSettingIsSyn(c *gin.Context) {
	userID := ufv1.GetUserID(c)
	err, data := service.GetSysShareLiveSettingIsSyn(userID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"is_syn": data.IsSyn}, c)

}

// @Tags 共享直播API
// @Summary 保存同步状态
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "保存同步状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/saveSysShareLiveSetting [get]
func SaveSysShareLiveSettingIsSyn(c *gin.Context) {
	var saveSaveSysShareLiveSetting request.SaveSaveSysShareLiveIsSyn
	_ = c.ShouldBindJSON(&saveSaveSysShareLiveSetting)
	userID := ufv1.GetUserID(c)
	err := service.SaveSysShareLiveSettingIsSyn(userID, saveSaveSysShareLiveSetting.IsSyn)
	if err != nil {
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithData("修改成功", c)

}

// @Tags 共享直播
// @Summary 共享直播直播间列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播直播间列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveRoomList [get]
func GetShareLiveRoomList(c *gin.Context) {
	var pageInfo request.ShareLiveRoomSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	pageInfo.SmallShopUserId = ufv1.GetUserID(c)
	var isOpen = 0
	pageInfo.IsOpen = &isOpen
	if err, list, total := service.GetShareLiveRoomListFront(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 共享直播API
// @Summary 保存修改直播间开启关闭状态 -- 单独
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "保存修改直播间开启关闭状态 -- 单独"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/saveShareLiveRoomIsSyn [POST]
func SaveShareLiveRoomSmallShop(c *gin.Context) {
	var saveShareLiveRoomSmallShop request.SaveShareLiveRoomSmallShop
	_ = c.ShouldBindJSON(&saveShareLiveRoomSmallShop)
	if saveShareLiveRoomSmallShop.ShareLiveRoomId == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}
	userID := ufv1.GetUserID(c)

	err := service.SaveShareLiveRoomSmallShop(userID, saveShareLiveRoomSmallShop)
	if err != nil {
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithData("修改成功", c)

}

// @Tags 共享直播API
// @Summary 保存修改直播间开启关闭状态 -- 单独
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "保存修改直播间开启关闭状态 -- 单独"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/saveShareLiveRoomIsSyn [get]
func SaveShareLiveRoomSmallShopAll(c *gin.Context) {
	var saveShareLiveRoomSmallShopAll request.SaveShareLiveRoomSmallShopAll
	_ = c.ShouldBindJSON(&saveShareLiveRoomSmallShopAll)

	userID := ufv1.GetUserID(c)

	err := service.SaveShareLiveRoomSmallShopAll(userID, saveShareLiveRoomSmallShopAll)
	if err != nil {
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithData("修改成功", c)

}

// @Tags 共享直播API
// @Summary 获取同步状态
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取同步状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/getShareLiveRoomProductListByShareLiveId [get]
func GetShareLiveRoomProductListByShareLiveId(c *gin.Context) {
	var pageInfo smallShopRequest.ProductStorageSearch
	_ = c.ShouldBindQuery(&pageInfo)
	if pageInfo.ShareLiveId == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}
	source.DB().Model(&model.ShareLiveRoomProduct{}).Where("share_live_room_id = ?", pageInfo.ShareLiveId).Pluck("product_id", &pageInfo.ProductIds)
	if len(pageInfo.ProductIds) == 0 {
		yzResponse.FailWithMessage("直播间没有商品", c)
		return
	}
	userID := ufv1.GetUserID(c)
	if err, list, total := smallShopService.GetProductList(pageInfo, userID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetShareLiveCategorysBySmallShopId @Tags 共享直播
// @Summary 通过店主id获取共享直播分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "通过店主id获取共享直播分类列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/getShareLiveCategorysBySmallShopId [get]
func GetShareLiveCategorysBySmallShopId(c *gin.Context) {
	var pageInfo request.ShareLiveCategorysBySmallShopId
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list := service.GetShareLiveCategorysBySmallShopId(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(list, c)
	}
}

// @Tags 共享直播
// @Summary 共享直播直播间列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播直播间列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/getShareLiveRoomListBySmallShopId [get]
func GetShareLiveRoomListBySmallShopId(c *gin.Context) {
	var pageInfo request.ShareLiveRoomSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.Sid == 0 {
		yzResponse.FailWithMessage("请提交小商店id", c)
		return
	}
	if err, list, total := service.GetShareLiveRoomList(pageInfo, 1); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetShareLiveRoomByIdAndSmallShopId
// @Tags 共享直播
// @Summary 创建共享直播间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "创建共享直播间"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/getShareLiveRoomByIdAndSmallShopId [get]
func GetShareLiveRoomByIdAndSmallShopId(c *gin.Context) {
	var shareLiveRoomIdRequest request.ShareLiveRoomIdRequest
	err := c.ShouldBindQuery(&shareLiveRoomIdRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if shareLiveRoomIdRequest.Sid == 0 {
		yzResponse.FailWithMessage("请提交小商店id", c)
		return
	}
	if shareLiveRoomIdRequest.Id == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}
	err = service.ShareLiveRoomBySmallShopId(shareLiveRoomIdRequest)
	if err != nil {
		log.Log().Error("验证失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, data := service.GetShareLiveRoomByIdSmallShop(shareLiveRoomIdRequest.Id, shareLiveRoomIdRequest.Sid)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}

// SaveLikeNum
// @Tags 共享直播
// @Summary 增加点赞人数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "增加点赞人数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/saveLikeNum [post]
func SaveLikeNum(c *gin.Context) {
	var saveTotalNumRequest request.ShareLiveRoomId
	err := c.ShouldBindJSON(&saveTotalNumRequest)

	if saveTotalNumRequest.ShareLiveRoomId == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交增加点赞人数的直播间", c)
		return
	}
	smallUserId := ufv1.GetUserID(c)
	//smallUserId := 1

	err = service.SaveLikeNum(smallUserId, saveTotalNumRequest)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
	return
}

// GetLike
// @Tags 共享直播
// @Summary 获取是否点赞
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "增加点赞人数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/GetLike [post]
func GetLike(c *gin.Context) {
	var saveTotalNumRequest request.ShareLiveRoomId
	err := c.ShouldBindJSON(&saveTotalNumRequest)

	if saveTotalNumRequest.ShareLiveRoomId == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交增加点赞人数的直播间", c)
		return
	}
	smallUserId := ufv1.GetUserID(c)
	//smallUserId := 1

	err, like := service.GetLike(smallUserId, saveTotalNumRequest)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(gin.H{"is_like": like.Status}, c)
	return
}

// SaveTotalNum
// @Tags 共享直播
// @Summary 保存累计观看人数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "保存累计观看人数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/saveTotalNum [post]
func SaveTotalNum(c *gin.Context) {
	var saveTotalNumRequest request.SaveTotalNumRequest
	err := c.ShouldBindJSON(&saveTotalNumRequest)
	if saveTotalNumRequest.Num == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交增加的观看人数", c)
		return
	}
	if saveTotalNumRequest.ShareLiveRoomId == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交增加人数的直播间", c)
		return
	}

	err = service.SaveTotalNum(0, saveTotalNumRequest)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
	return
}

// GetShaveLiveRoomPoster SaveTotalNum
// @Tags 共享直播
// @Summary 保存累计观看人数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "保存累计观看人数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/saveTotalNum [post]
func GetShaveLiveRoomPoster(c *gin.Context) {
	var saveTotalNumRequest request.GetShaveLiveRoomPoster
	err := c.ShouldBindJSON(&saveTotalNumRequest)
	//这个卖羽绒服的直播间又搞特价啦，快来看看买一送二，买1000减去100，这个卖羽绒服又高特价了
	smallUserId := ufv1.GetUserID(c)
	//var smallUserId uint = 1
	//saveTotalNumRequest.Sid = 1
	domain := c.Request.Header.Get("X-Forwarded-Proto") + "://" + c.Request.Host
	err, link := tools.GetShaveLiveRoomPoster(saveTotalNumRequest.ShareLiveRoomId, saveTotalNumRequest.Sid, smallUserId, saveTotalNumRequest.Text, domain)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//yzResponse.Ok(c)
	yzResponse.OkWithData(link, c)
	return
}

// GenSig Send
// @Tags 共享直播
// @Summary im用户登录密码UserSig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "im用户登录密码UserSig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/genSig [post]
func GenSig(c *gin.Context) {
	var genSigRequest request.GenSigRequestSmallShop
	err := c.ShouldBindJSON(&genSigRequest)

	if genSigRequest.ShareLiveRoomId == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}
	if genSigRequest.Sid == 0 {
		yzResponse.FailWithMessage("请提交小商店id", c)
		return
	}
	smallUserId := ufv1.GetUserID(c)

	err, ShareLiveRoom := service.VerifyShareLiveRoomSmallShop(genSigRequest.ShareLiveRoomId, genSigRequest.Sid)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, im := common.Initial()
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var uid string
	uid = im.Config.Value.SerialNumber + "_" + strconv.Itoa(int(ShareLiveRoom.ID)) + "_" + strconv.Itoa(int(genSigRequest.Sid)) + "_" + strconv.Itoa(int(smallUserId))
	err, result := im.GenSig(uid)

	if err != nil {
		log.Log().Error("im用户登录密码UserSig请求失败", zap.Any("err", err))
		yzResponse.FailWithMessage("im用户登录密码UserSig请求失败"+err.Error(), c)
		return
	}
	if result.Code != 200 {
		log.Log().Error("im用户登录密码UserSig请求失败", zap.Any("err", err))
		yzResponse.FailWithMessage("im用户登录密码UserSig请求失败"+result.Msg, c)
		return
	}

	//序号_直播间id_小商店id_小商店会员id
	yzResponse.OkWithData(gin.H{"data": result.Data, "user_id": uid}, c)
	return
}

// Send
// @Tags 共享直播
// @Summary 群组系统消息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "群组系统消息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/genSig [post]
func SendGroupMsg(c *gin.Context) {
	var sendGroupMsgRequest request.SendGroupMsgRequest
	err := c.ShouldBindJSON(&sendGroupMsgRequest)

	if sendGroupMsgRequest.ShareLiveRoomId == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}
	if sendGroupMsgRequest.Sid == 0 {
		yzResponse.FailWithMessage("请提交小商店id", c)
		return
	}

	err, shareLiveRoomApp := service.VerifyShareLiveRoomSmallShop(sendGroupMsgRequest.ShareLiveRoomId, sendGroupMsgRequest.Sid)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, im := common.Initial()
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, result := im.SendGroupMsg(sendGroupMsgRequest, shareLiveRoomApp.GroupId)

	if err != nil {
		log.Log().Error("群组系统消息", zap.Any("err", err))
		yzResponse.FailWithMessage("群组系统消息"+err.Error(), c)
		return
	}
	if result.Code != 200 {
		log.Log().Error("群组系统消息", zap.Any("err", err))
		yzResponse.FailWithMessage("群组系统消息"+result.Msg, c)
		return
	}
	yzResponse.OkWithData(result.Data, c)
	//{"code":200,"msg":"","data":{"ActionStatus":"OK","ErrorCode":0,"ErrorInfo":"","MsgSeq":1,"MsgTime":1690421984}}
	//{"code":200,"msg":"","data":{"ActionStatus":"FAIL","ErrorCode":10004,"ErrorInfo":"invalid msg"}}
	return
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 通过直播间id获取回放
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "通过直播间id获取回放"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/getShareLiveRoomRecordFileByRoomId [get]
func GetSmallShopShareLiveRoomRecordFileByRoomId(c *gin.Context) {
	var shareLiveRoomIdRequest model.ShareLiveRoomRecordFile
	err := c.ShouldBindQuery(&shareLiveRoomIdRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, room := service.GetShareLiveRoomById(shareLiveRoomIdRequest.ShareLiveRoomId)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if room.IsTranscribe == 0 {
		yzResponse.FailWithMessage("暂无录制", c)
		return
	}
	if room.IsPlayback != 1 {
		yzResponse.FailWithMessage("未开启回放", c)
		return
	}

	err, data := service.GetShareLiveRoomRecordFileByRoomId(shareLiveRoomIdRequest.ShareLiveRoomId)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}

// @Tags 共享直播API
// @Summary 获取共享直播配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取共享直播配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/getSysShareLiveSetting [get]
func GetSysShareLiveSetting(c *gin.Context) {
	var sys setting.SysSetting
	_ = c.ShouldBindJSON(&sys)
	err, shareLiveSetting := setting.GetSysShareLiveSetting()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(gin.H{"is_open": shareLiveSetting.Value.IsOpen, "sdkappid": shareLiveSetting.Value.AppId, "serial_number": shareLiveSetting.Value.SerialNumber, "im_app_secret": shareLiveSetting.Value.ImAppSecret}, c)

}
