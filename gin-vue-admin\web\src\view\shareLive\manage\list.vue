<template>
    <m-card>
        <el-button type="primary" @click="openCreateDialog">创建直播间</el-button>
        <el-form :model="searchInfo" label-width="130px" inline class="search-term mt25">
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchInfo.title" class="line-input" clearable>
                    <span slot="prepend">直播间名称</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchInfo.keyword" class="line-input" clearable>
                    <span slot="prepend">主播昵称/手机号</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >直播间分类</span>
                    </div>
                    <el-select v-model="searchInfo.share_live_category_id" clearable class="w100">
                        <el-option v-for="item in shareLiveCategoryOption" :key="item.id" :value="item.id"
                                    :label="item.title"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >直播间状态</span>
                    </div>
                    <el-select v-model="searchInfo.status" clearable class="w100">
                        <el-option :value="0" label="等待直播"></el-option>
                        <el-option :value="1" label="直播中"></el-option>
                        <el-option :value="2" label="已结束"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >采购端</span>
                    </div>
                    <el-select v-model="searchInfo.application_id" clearable class="w100">
                        <el-option v-for="item in applicationOption" :key="item.id" :value="item.id"
                                    :label="item.companyName"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >是否录制</span>
                    </div>
                    <el-select v-model="searchInfo.is_transcribe" clearable class="w100">
                        <el-option :value="1" label="是"></el-option>
                        <el-option :value="0" label="否"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >是否开启回放</span>
                    </div>
                    <el-select v-model="searchInfo.is_playback" clearable class="w100">
                        <el-option :value="1" label="是"></el-option>
                        <el-option :value="2" label="否"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchInfo.product_name" class="line-input" clearable>
                    <span slot="prepend">商品名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >商品ID</span>
                    </div>
                    <m-num-input v-model="searchInfo.product_id" placeholder="请输入"></m-num-input>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" style="width: 500px">
                    <div class="line-box ">
                        <span >直播时间</span>
                    </div>
                    <m-daterange v-model="date"></m-daterange>
                </div>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" class="mt25">
            <el-table-column label="ID" align="center" prop="id"></el-table-column>
            <el-table-column width="250">
                <template slot="header" slot-scope="scope">
                    <p>直播间</p>
                    <p>开播时间</p>
                </template>
                <template slot-scope="scope">
                    <div class="f">
                        <m-image :src="scope.row.image_url" class="list-img"></m-image>
                        <p class="hiddenText2 ml10" style="line-height: 30px;">
                            {{ scope.row.title }}
                        </p>
                    </div>
                    <p class="mt25">{{ scope.row.start_at | formatDate }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header" slot-scope="scope">
                    <p>主播昵称</p>
                    <p>手机号</p>
                </template>
                <template slot-scope="scope">
                    <p>
                        <span v-if="scope.row.user.nickname">{{ scope.row.user.nickname }}</span><span v-else
                                                                                                       class="color-red">未完善会员昵称</span>
                    </p>
                    <p>{{ scope.row.user.username }}</p>
                </template>
            </el-table-column>
            <el-table-column label="分类" align="center" prop="share_live_category.title"></el-table-column>
            <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                    {{ scope.row.status | formattedState }}
                </template>
            </el-table-column>
            <el-table-column align="center" width="150">
                <template slot="header" slot-scope="scope">
                    <p>累计时长</p>
                    <p>总观看人数</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.total_time | formatDuration }}</p>
                    <p>{{ scope.row.total_num }}人</p>
                </template>
            </el-table-column>
            <el-table-column label="商品数量" align="center" prop="product_num"></el-table-column>
            <el-table-column label="累计点赞" align="center" prop="like_num"></el-table-column>
            <el-table-column label="是否显示" align="center">
                <template slot-scope="scope">
                    <el-switch
                        v-model="scope.row.is_open"
                        :active-value="0"
                        :inactive-value="1"
                        @change="handleStatus(scope.row)"
                    ></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="是否横屏" align="center">
                <template slot-scope="scope">
                    <el-switch
                        v-model="scope.row.is_landscape"
                        :active-value="1"
                        :inactive-value="0"
                        @change="handleLandscapeStatus(scope.row)"
                    ></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="采购端同步数量" align="center" prop="app_num"></el-table-column>
            <el-table-column align="center" width="150">
                <template slot="header" slot-scope="scope">
                    <p>带货订单数量(单)</p>
                    <p>带货订单金额(元)</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.order_total }}</p>
                    <p>{{ scope.row.order_amount_total | formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header" slot-scope="scope">
                    <p>峰值带宽</p>
                    <p>总流量</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.peak_bandwidth }}Mbps</p>
                    <p>{{ scope.row.total_flow }}Mbps</p>
                </template>
            </el-table-column>
            <el-table-column label="开始直播" align="center"  width="150">
                <template slot-scope="scope">
                    <div v-if="scope.row.status === 0 || scope.row.status === 1">
                        <p>
                            <el-button v-if="scope.row.status === 0" type="primary" size="small"
                                       @click="liveBtnPerate('start',scope.row.id)">开始直播
                            </el-button>
                            <el-button v-if="scope.row.status === 1" type="danger" size="small"
                                       @click="liveBtnPerate('end',scope.row.id)">结束直播
                            </el-button>
                        </p>
                        <el-button type="text" @click="$fn.copy(scope.row.push_url)">复制推流地址</el-button>

                    </div>
                    <div v-if="scope.row.status === 2">
                        <p>
                            <el-button type="info" size="small" disabled>已结束</el-button>
                        </p>
                        <el-button type="text" @click="openPlayBackDialog(scope.row)">查看回放</el-button>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.status === 0" type="text" @click="edit(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
                    <el-button type="text" @click="jumpLink('purchasingSide',scope.row)" style="padding: 0 !important;margin-left: 10px !important;">采购端数据</el-button>
                    <el-button v-if="scope.row.status === 1 || scope.row.status === 2" type="text" @click="jumpOrder(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">带货订单</el-button>
                  <el-button type="text" v-if="scope.row.status === 1 || scope.row.status === 2" @click="openEditApplicationDialog(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">修改采购端</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100,200]"
                :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
        <CreateDialog ref="createDialog" @reLoad="getTableData"/>
        <PlaybackDialog ref="playbackDialog" @reLoad="getTableData"/>
        <EditApplicationDialog ref="editApplicationDialog" @reLoad="getTableData"/>

    </m-card>
</template>
<script>
import infoList from "@/mixins/infoList";
import MDaterange from "@/components/mDate/daterange.vue";
import EditApplicationDialog from "./components/editApplicationDialog.vue";

import {
    getShareLiveRoomList,
    getShareLiveCategorys,
    saveShareLiveRoomEnd,
    saveShareLiveRoomStart,
    saveShareLiveRoomIsOpen,
    saveShareLiveRoomIsLandscape
} from "@/api/shareLive";
import CreateDialog from "./components/createDialog.vue";

import {getApplicationList} from "@/api/application";
import PlaybackDialog from "./components/playbackDialog.vue";

export default {
    name: "shareLiveManageList",
    components: {MDaterange, CreateDialog, PlaybackDialog,EditApplicationDialog},
    mixins: [infoList],
    filters: {
        formattedState(v) {
            let s = ""
            switch (v) {
                case 0:
                    s = "等待直播"
                    break;
                case 1:
                    s = "直播中"
                    break;
                case 2:
                    s = "已结束"
                    break;
            }
            return s;
        },
        formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600); // 计算小时数
            const minutes = Math.floor((seconds % 3600) / 60); // 计算分钟数
            const remainingSeconds = seconds % 60; // 计算剩余的秒数

            // 格式化输出
            if (hours > 0) {
                if (remainingSeconds === 0) {
                    return `${hours}小时${minutes}分钟`;
                }
                return `${hours}小时${minutes}分钟${remainingSeconds}秒`;
            }
            if (remainingSeconds === 0) {
                return `${minutes}分钟`;
            }
            return `${minutes}分钟${remainingSeconds}秒`;
        }
    },
    data() {
        return {
            date: [],
            applicationOption: [],
            shareLiveCategoryOption: [],
            listApi: getShareLiveRoomList
        }
    },
    mounted() {
        this.getTableData()
        this.getApplicationOption()
        this.getShareLiveCategoryOption()
    },
    methods: {
      openEditApplicationDialog(row) {
        this.$refs.editApplicationDialog.init(row);
      },
        // 是否显示开关
        async handleStatus(row) {
            let params = {
                is_open: row.is_open,
                id: row.id,
            }
            let res = await saveShareLiveRoomIsOpen(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
            }
        },
        // 是否横屏开关
        async handleLandscapeStatus(row) {
            let params = {
                is_landscape: row.is_landscape,
                id: row.id,
            }
            let res = await saveShareLiveRoomIsLandscape(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
            }
        },
        jumpOrder(row){
            this.$_blank('/layout/orderIndex/allOrderIndex',{
                share_live_room_id: row.id
            })
        },
        jumpLink(type, row) {
            switch (type) {
                case "purchasingSide": // 采购端数据列表跳转
                    this.$router.push({
                        path: '/layout/shareLiveIndex/shareLivePurchasingDataList', query: {
                            share_live_room_id: row.id
                        }
                    })
                    break;
            }
        },
        openPlayBackDialog(row) {
            this.$refs.playbackDialog.init(row.id, row.is_playback)
        },
        // 操作 开始or结束直播
        liveBtnPerate(type, id) {
            let t = type === 'start' ? '开始' : '结束'
            this.$confirm(`确定${t}直播?`, '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                switch (type) {
                    case 'start':
                        const startRes = await saveShareLiveRoomStart({id})
                        if (startRes.code === 0) {
                            this.$message.success(startRes.msg)
                            this.getTableData()
                        }
                        break;
                    case 'end':
                        const endRes = await saveShareLiveRoomEnd({id})
                        if (endRes.code === 0) {
                            this.$message.success(endRes.msg)
                            this.getTableData()
                        }
                        break;
                }
            }).catch(() => {
            })
        },
        // 获取采购端option
        async getApplicationOption() {
            const {code, data} = await getApplicationList({page: 1, pageSize: 9999})
            if (code === 0) {
                this.applicationOption = data.list
            }
        },
        // 获取直播间分类option
        async getShareLiveCategoryOption() {
            const {code, data} = await getShareLiveCategorys()
            if (code === 0) {
                this.shareLiveCategoryOption = data;
            }
        },
        edit(id) {
            this.$refs.createDialog.init(id)
        },
        openCreateDialog() {
            this.$refs.createDialog.init()
        },
        search() {
            if (this.date.length) {
                this.searchInfo.start_at_search = this.date[0]
                this.searchInfo.end_at_search = this.date[1]
            }
            this.getTableData(1)
        },
        reSearch() {
            this.searchInfo = {}
            this.date = []
        }
    }
}
</script>
<style scoped lang="scss">
.el-date-editor .el-range-separator {
  line-height: none !important;
}

.list-img {
  min-width: 60px;
  min-height: 60px;
  max-width: 60px;
  max-height: 60px;
}

::v-deep .search-term .line-input .fac .el-input--medium .el-input__inner {
    text-align: left;
}
</style>