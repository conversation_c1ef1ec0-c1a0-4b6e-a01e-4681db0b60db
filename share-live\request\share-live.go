package request

import (
	productRequest "product/request"
	"share-live/model"
	yzRequest "yz-go/request"
)

type ShareLiveCategorySearch struct {
	model.ShareLiveCategory
	yzRequest.PageInfo
}
type ProductStorageSearch struct {
	yzRequest.PageInfo
	Filter          int                        `json:"filter" form:"filter" query:"filter"`
	Title           string                     `json:"title"`
	Category1ID     int                        `json:"category_1_id"`
	Category2ID     int                        `json:"category_2_id"`
	Category3ID     int                        `json:"category_3_id"`
	Source          *int                       `json:"source"`
	MarketPrice     productRequest.Between     `json:"market_price"`    //市场价
	AgreementPrice  productRequest.Between     `json:"agreement_price"` //协议价
	GuidePrice      productRequest.Between     `json:"guide_price"`     //市场价、指导价
	ActivityPrice   productRequest.Between     `json:"activity_price"`  //营销价
	ActivityRate    productRequest.Between     `json:"activity_rate"`   //营销价
	OriginRate      productRequest.Between     `json:"origin_rate"`     //常规利润率
	MarketRate      productRequest.Between     `json:"market_rate"`     //市场价利润率  (市场价-协议价)/协议价
	Profit          productRequest.Between     `json:"profit"`          //毛利润
	IsBill          *int                       `json:"is_bill"`
	IsRecommend     *int                       `json:"is_recommend"`
	IsNew           *int                       `json:"is_new"`
	IsHot           *int                       `json:"is_hot"`
	IsPromotion     *int                       `json:"is_promotion"`
	IsDisplay       *int                       `json:"is_display"`
	CreatedAt       productRequest.BetweenTime `json:"created_at"`
	SupplierID      uint                       `json:"supplier_id"`
	FreightType     int                        `json:"freight_type"`
	Type            string                     `json:"type"`
	Sort            bool                       `json:"sort"`
	SupplierName    string                     `json:"supplier_name"`
	GatherSupplyID  *uint                      `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
	TopicID         uint                       `json:"topic_id"`
	IsImport        *int                       `json:"is_import" form:"is_import"`
	AppID           uint                       `json:"app_id"`
	Cursor          int                        `json:"cursor"` //游标
	SupplyLineId    string                     `json:"supply_line_id"`
	ShareLiveRoomId uint                       `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id

}

// 共享直播间商品列表
type GetShareLiveRoomProductListRequest struct {
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id
	yzRequest.PageInfo
}

type ShareLiveRoomSearch struct {
	model.ShareLiveRoom
	yzRequest.PageInfo
	Keyword         string `json:"keyword" query:"keyword" form:"keyword"`                             //主播昵称/手机号
	Status          *int   `json:"status" form:"status" gorm:"column:status;comment:状态;type:int(11);"` //状态不传所有 0等待直播1直播中2已结束
	ApplicationId   uint   `json:"application_id" query:"application_id" form:"application_id"`
	IsTranscribe    *int   `json:"is_transcribe"  form:"is_transcribe" gorm:"column:is_transcribe;comment:是否有录制 1是0否;"` //不传则是获取所有
	StartATSearch   string `json:"start_at_search" query:"start_at_search" form:"start_at_search"`
	EndATSearch     string `json:"end_at_search" query:"end_at_search" form:"end_at_search"`
	ProductName     string `json:"product_name" query:"product_name" form:"product_name"`                               //商品名称
	ProductId       uint   `json:"product_id" query:"product_id" form:"product_id"`                                     //商品id
	IsImport        int    `json:"is_import" query:"is_import" form:"is_import"`                                        //是否已导入 1 已导入  2未导入 不传或者传0全部
	SmallShopUserId uint   `json:"small_shop_user_id" query:"small_shop_user_id" form:"small_shop_user_id"`             //店主会员id 不需要前端传 登录后 后端通过token获取
	Sid             uint   `json:"sid" form:"sid" gorm:"column:sid;comment:小商店id;"`                                     // 小商店id //用户端使用
	IsOpen          *int   `json:"is_open" form:"is_open" gorm:"column:is_open;default:0;comment:开关0开1关;type:int(11);"` //状态0等待直播1直播中2已结束

}

type ShareLiveRoomApplicationSearch struct {
	model.ShareLiveRoomApplication
	yzRequest.PageInfo
	Keyword string `json:"keyword" query:"keyword" form:"keyword"` //主播昵称/手机号

	Title               string `json:"title" form:"title" gorm:"column:title;comment:直播间标题;"`              // 直播间标题
	Status              *int   `json:"status" form:"status" gorm:"column:status;comment:状态;type:int(11);"` //状态不传所有 0等待直播1直播中2已结束
	ApplicationId       uint   `json:"application_id" query:"application_id" form:"application_id"`
	ShareLiveCategoryId uint   `json:"share_live_category_id" form:"share_live_category_id" gorm:"column:share_live_category_id;comment:分类id;"` // 分类id
	StartATSearch       string `json:"start_at_search" query:"start_at_search" form:"start_at_search"`
	EndATSearch         string `json:"end_at_search" query:"end_at_search" form:"end_at_search"`
	ProductName         string `json:"product_name" query:"product_name" form:"product_name"` //商品名称
	ProductId           uint   `json:"product_id" query:"product_id" form:"product_id"`       //商品id

}

// 修改直播间开关状态请求
type ShareLiveRoomIsOpenRequest struct {
	ID     uint `json:"id" `      // 直播间ID
	IsOpen int  `json:"is_open" ` // 开关状态(0开1关)
}

// 创建直播间请求参数
type CreateShareLiveRoomRequest struct {
	ShareLiveRoom model.ShareLiveRoom `json:"share_live_room"`
	ProductIds    []uint              `json:"product_ids"`
}

type ShareLiveRoomIdRequest struct {
	Id  uint `json:"id" query:"id" form:"id"`
	Sid uint `json:"sid" form:"sid" gorm:"column:sid;comment:小商店id;"` // 小商店id //用户端使用

}

type ShareLiveRoomIdsRequest struct {
	Ids []uint `json:"ids" query:"ids" form:"ids"`
}

type SaveSaveSysShareLiveSetting struct {
	Data string `json:"data" query:"data" form:"data"`
}

type SaveSaveSysShareLiveIsSyn struct {
	IsSyn int `json:"is_syn" query:"is_syn" form:"is_syn"` //0关闭1开启
}

type SaveShareLiveRoomSmallShop struct {
	Status          int  `json:"status" query:"status" form:"status"`                                                          //0关闭1开启
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id

}

type SaveShareLiveRoomSmallShopAll struct {
	ShareLiveRoomSearch
	Status int `json:"status" query:"status" form:"status"` //0关闭1开启

	//ShareLiveCategoryId uint   `json:"share_live_category_id" form:"share_live_category_id" gorm:"column:share_live_category_id;comment:分类id;"`    // 分类id
	//Keyword string `json:"keyword" query:"keyword" form:"keyword"` //主播昵称/手机号
	//ProductName                string        `json:"product_name" query:"product_name" form:"product_name"` //商品名称
	//Title          string    `json:"title" form:"title" gorm:"column:title;comment:直播间标题;"`          // 直播间标题

}

type SaveShareLiveRoomIsPlayBack struct {
	Id         uint `json:"id" query:"id" form:"id"`
	IsPlayback int  `json:"is_playback"  form:"is_playback" gorm:"column:is_playback;default:1;comment:是否开启回放 1是2否;"`
}

// 修改直播间横屏状态请求
type ShareLiveRoomIsLandscapeRequest struct {
	ID          uint `json:"id" `           // 直播间ID
	IsLandscape int  `json:"is_landscape" ` // 横屏状态(0否1是)
}

type BeginRequest struct { //推流 断流参数一致
	App         string `json:"app"`
	Appid       int    `json:"appid"`
	Appname     string `json:"appname"`
	ChannelID   string `json:"channel_id"` //直播间标识
	Errcode     int    `json:"errcode"`
	Errmsg      string `json:"errmsg"`
	EventTime   int    `json:"event_time"`
	EventType   int    `json:"event_type"`
	SetID       int    `json:"set_id"`
	Node        string `json:"node"`
	Sequence    string `json:"sequence"`
	StreamID    string `json:"stream_id"`
	StreamParam string `json:"stream_param"`
	UserIP      string `json:"user_ip"`
	Width       int    `json:"width"`
	Height      int    `json:"height"`
	Sign        string `json:"sign"` //签名
	T           int    `json:"t"`    //特殊字符串
}

type RecordRequest struct { //录制回调
	EventType      int    `json:"event_type"`
	Appid          int    `json:"appid"`
	App            string `json:"app"`
	CallbackExt    string `json:"callback_ext"`
	Appname        string `json:"appname"`
	StreamID       string `json:"stream_id"`  //直播流名称
	ChannelID      string `json:"channel_id"` //直播间标识
	FileID         string `json:"file_id"`    //点播 file ID，在 云点播平台 可以唯一定位一个点播视频文件
	RecordFileID   string `json:"record_file_id"`
	FileFormat     string `json:"file_format"` //FLV，HLS，MP4，AAC
	TaskID         string `json:"task_id"`
	StartTime      int    `json:"start_time"`
	EndTime        int    `json:"end_time"`
	StartTimeUsec  int    `json:"start_time_usec"`
	EndTimeUsec    int    `json:"end_time_usec"`
	Duration       int    `json:"duration"`  //录制文件时长，单位秒
	FileSize       int    `json:"file_size"` //录制文件大小，单位字节
	StreamParam    string `json:"stream_param"`
	VideoURL       string `json:"video_url"` //录制文件下载 URL
	MediaStartTime int    `json:"media_start_time"`
	RecordBps      int    `json:"record_bps"`
	Sign           string `json:"sign"`
	T              int    `json:"t"`
}

// 保存观看人数
type SaveTotalNumRequest struct {
	Num             uint `json:"num" form:"num"`                                                                               //增加人数
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id

}

// 用户登录密码UserSig -- im API
type GenSigRequest struct {
	Uid             string `json:"uid" form:"uid"`                                                                               //im用户唯一身份标识（商城端提供的）
	ShareLiveRoomId uint   `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id
}

// 用户登录密码UserSig -- im API
type GenSigRequestSmallShop struct {
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id
	Sid             uint `json:"sid" form:"sid" gorm:"column:sid;comment:小商店id;"`                                              // 小商店id

}

type LiveCallbackTemplateRequest struct {
	TemplateId *int64 `json:"TemplateId,omitempty" name:"TemplateId"`

	// 模板名称。
	// 长度上限：255字节。
	// 仅支持中文、英文、数字、_、-。
	TemplateName *string `json:"TemplateName,omitempty" name:"TemplateName"`

	// 描述信息。
	// 长度上限：1024字节。
	// 仅支持中文、英文、数字、_、-。
	Description *string `json:"Description,omitempty" name:"Description"`

	// 开播回调 URL，
	// 相关协议文档：[事件消息通知](/document/product/267/32744)。
	StreamBeginNotifyUrl *string `json:"StreamBeginNotifyUrl,omitempty" name:"StreamBeginNotifyUrl"`

	// 断流回调 URL，
	// 相关协议文档：[事件消息通知](/document/product/267/32744)。
	StreamEndNotifyUrl *string `json:"StreamEndNotifyUrl,omitempty" name:"StreamEndNotifyUrl"`

	// 录制回调 URL，
	// 相关协议文档：[事件消息通知](/document/product/267/32744)。
	RecordNotifyUrl *string `json:"RecordNotifyUrl,omitempty" name:"RecordNotifyUrl"`

	// 截图回调 URL，
	// 相关协议文档：[事件消息通知](/document/product/267/32744)。
	SnapshotNotifyUrl *string `json:"SnapshotNotifyUrl,omitempty" name:"SnapshotNotifyUrl"`

	// 鉴黄回调 URL，
	// 相关协议文档：[事件消息通知](/document/product/267/32741)。
	PornCensorshipNotifyUrl *string `json:"PornCensorshipNotifyUrl,omitempty" name:"PornCensorshipNotifyUrl"`

	// 回调 Key，回调 URL 公用，回调签名详见事件消息通知文档。
	// [事件消息通知](/document/product/267/32744)。
	CallbackKey *string `json:"CallbackKey,omitempty" name:"CallbackKey"`

	// 混流回调 URL，
	// 相关协议文档：[事件消息通知](/document/product/267/32744)。
	StreamMixNotifyUrl *string `json:"StreamMixNotifyUrl,omitempty" name:"StreamMixNotifyUrl"`
}
type MsgBody struct {
	MsgType        string `json:"MsgType" form:"MsgType" `               // MsgType'    //TIMCustomElem
	MsgContentData string `json:"MsgContentData" form:"MsgContentData" ` // MsgContentData
	MsgContentDesc string `json:"MsgContentDesc" form:"MsgContentDesc" ` // MsgContentDesc
	MsgContentExt  string `json:"MsgContentExt" form:"MsgContentExt" `   // MsgContentExt
}

// 修改直播间采购端设置请求
type SaveShareLiveRoomApplicationSetting struct {
	ShareLiveRoomId      uint   `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"`                   // 直播间id
	IsSpecifyApplication int    `json:"is_specify_application" form:"is_specify_application" gorm:"column:is_specify_application;comment:是否指定采购端0否1是;"` // 是否指定采购端0否1是
	ApplicationIds       []uint `json:"application_ids" form:"application_ids" gorm:"column:application_ids;comment:采购端id列表;"`                          // 采购端id列表
}

// 群组系统消息
type SendGroupMsgRequest struct {
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id
	//GroupId string `json:"GroupId" form:"GroupId"` //'GroupId
	Random      int       `json:"Random" form:"Random" `                           // 随机10位数字
	MsgPriority string    `json:"MsgPriority" form:"MsgPriority" `                 // MsgPriority
	MsgBody     []MsgBody `json:"MsgBody" form:"MsgBody" `                         // MsgType'
	Sid         uint      `json:"sid" form:"sid" gorm:"column:sid;comment:小商店id;"` // 小商店id //小商店请求时使用

}

type ShareLiveCategorysBySmallShopId struct {
	Sid uint `json:"sid" form:"sid" gorm:"column:sid;comment:小商店id;"` // 小商店id

}

type ShareLiveRoomId struct {
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id

}

type GetShaveLiveRoomPoster struct {
	Sid             uint   `json:"sid" form:"sid" gorm:"column:sid;comment:小商店id;"`                                              // 小商店id
	ShareLiveRoomId uint   `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id
	Text            string `json:"text"`
}
