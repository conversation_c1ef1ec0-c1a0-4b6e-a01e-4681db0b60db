package component

import "ai-assistant/model"

// AIService AI服务接口
type AIService interface {
	Chat(message string, chatType string) (map[string]interface{}, error)
}

// NewAIService AI服务的实例化方法
func NewAIService() AIService {
	err, setting := model.GetAiSetting()
	if err != nil {
		return nil
	}
	switch setting.AiType {
	case "silicon":
		return &SiliconFlow{}
	default:
		return &SiliconFlow{} // 默认使用 SiliconFlow
	}
}

func NewAIServiceByType(aiType string) AIService {
	switch aiType {
	case "silicon":
		return &SiliconFlow{}
	// 后续可以轻松添加其他AI服务
	// case "openai":
	//     return &OpenAI{}
	default:
		return &SiliconFlow{} // 默认使用 SiliconFlow
	}
}
