package product

import (
	"fmt"
	"testing"
)

func TestAction(t *testing.T) {

	accessToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6WyJhbGwiXSwiZXhwIjoxNzU1NTAzMjE2LCJqdGkiOiJjM2EwZjBlNi04MDRiLTQxNDEtYTJlZi05NjBhN2YxNTJlOTEiLCJ0ZW5hbnQiOiIwMGEwMWExOS04YWRkLTQ3N2YtYTg1Ni01MTUxN2Q1ZDQ4MTQiLCJjbGllbnRfaWQiOiJiZjc3OTk2NmU1Mzc0OTIzODljNTM0YWExYzk3ZDBmYzowMGEwMWExOS04YWRkLTQ3N2YtYTg1Ni01MTUxN2Q1ZDQ4MTQifQ.oC03iVJVT30BbTJwvZ4BJJOCaQpxj2LNJ970wRDjpv0"

	// 4.1 查询品牌列表
	//result, err := BrandPage(BrandPageParams{
	//	AccessToken: accessToken,
	//	PageNum:     1,
	//	PageSize:    100000, // 很有意思，页面个数没有限制
	//})

	// 4.2 查询分类列表
	//result, err := CategoryPage(CategoryPageParams{
	//	AccessToken: accessToken,
	//	PageNum:     1,
	//	PageSize:    200,
	//})

	// 4.3 查询商品列表
	//result, err := GoodsPage(GoodsPageParams{
	//	Domain:      "https://zhiwu.admin.fyuanai.com",
	//	AccessToken: accessToken,
	//	PageNum:     1,
	//	PageSize:    100, // 很有意思，页面个数没有限制
	//})

	// 获取全部商品
	//result, err := GoodsAll("https://zhiwu.admin.fyuanai.com", accessToken)

	// 4.4 查询商品详情
	// 单规格(goodsId:e938c77a-894b-481e-b0b6-9f3446026383 skuId:0ea02f2f-160b-41a6-9a96-75b7da8faca4)
	// 多规格(goodsId:a28f38cf-ab07-4594-acb2-0e5ec38ebf0c skuId:37652f50-2d2e-4a45-b4ae-3f7233fa42e0)
	//result, err := GoodsDetail(GoodsDetailParams{
	//	AccessToken: accessToken,
	//	Domain:      "https://zhiwu.admin.fyuanai.com",
	//	GoodsId:     "df78fdc8-0812-4164-abcb-43d875c984f7",
	//	SkuId:       "8597e7d5-ea86-42c9-a540-dd707a7f8b79",
	//})

	// 4.7 批量查询价格库存
	result, err := GoodsPriceStock(GoodsPriceStockParams{
		AccessToken: accessToken,
		Domain:      "https://zhiwu.admin.fyuanai.com",
		SkuIds:      []string{"405d6ad4-8b8f-4c4f-8c8e-4dcc75f3ef4f"},
	})

	// 4.8 查询全量分类信息
	//result, err := CategoryAll("https://zhiwu.admin.fyuanai.com", accessToken)

	if err != nil {
		fmt.Println(err)
	}

	fmt.Println(result)
}
