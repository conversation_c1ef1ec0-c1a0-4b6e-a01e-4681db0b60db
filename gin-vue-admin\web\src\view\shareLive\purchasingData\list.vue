<template>
    <m-card>
        <el-form :model="searchInfo" label-width="130px" inline class="search-term mt25">
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchInfo.title" class="line-input" clearable>
                    <span slot="prepend">直播间名称</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchInfo.tikeywordtle" class="line-input" clearable>
                    <span slot="prepend">主播昵称/手机号</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >直播间分类</span>
                    </div>
                    <el-select v-model="searchInfo.share_live_category_id" clearable class="w100">
                        <el-option v-for="item in shareLiveCategoryOption" :key="item.id" :value="item.id"
                                    :label="item.title"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >直播间状态</span>
                    </div>
                    <el-select v-model="searchInfo.status" clearable class="w100">
                        <el-option :value="0" label="等待直播"></el-option>
                        <el-option :value="1" label="直播中"></el-option>
                        <el-option :value="2" label="已结束"></el-option>
                    </el-select>
                </div>
            </el-form-item>
             <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >采购端</span>
                    </div>
                    <el-select v-model="searchInfo.application_id" clearable class="w100">
                        <el-option v-for="item in applicationOption" :key="item.id" :value="item.id"
                                    :label="item.companyName"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="searchInfo.product_name" class="line-input" clearable>
                    <span slot="prepend">商品名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >商品ID</span>
                    </div>
                    <m-num-input v-model="searchInfo.product_id" placeholder="请输入"></m-num-input>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" style="width: 500px">
                    <div class="line-box ">
                        <span >直播时间</span>
                    </div>
                    <m-daterange v-model="date"></m-daterange>
                </div>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" class="mt25">
            <el-table-column label="ID" align="center" prop="id"></el-table-column>
            <el-table-column width="250">
                <template slot="header" slot-scope="scope">
                    <p>直播间</p>
                    <p>开播时间</p>
                </template>
                <template slot-scope="scope">
                    <div class="f">
                        <m-image :src="scope.row.share_live_room.image_url" class="list-img"></m-image>
                        <p class="hiddenText2 ml10" style="line-height: 30px;">
                            {{ scope.row.share_live_room.title }}
                        </p>
                    </div>
                    <p class="mt25">{{ scope.row.share_live_room.start_at | formatDate }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header" slot-scope="scope">
                    <p>主播昵称</p>
                    <p>手机号</p>
                </template>
                <template slot-scope="scope">
                    <p>
                        <span v-if="scope.row.share_live_room.user.nickname">{{
                            scope.row.share_live_room.user.nickname
                            }}</span><span v-else
                                           class="color-red">未完善会员昵称</span>
                    </p>
                    <p>{{ scope.row.share_live_room.user.username }}</p>
                </template>
            </el-table-column>
            <el-table-column label="分类" align="center"
                             prop="share_live_room.share_live_category.title"></el-table-column>
            <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                    {{ scope.row.share_live_room.status | formattedState }}
                </template>
            </el-table-column>
            <el-table-column label="商品数量" align="center" prop="product_num"></el-table-column>
            <el-table-column label="采购端信息" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.application.user_data.username }}</p>
                    <p>{{ scope.row.application.companyName }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header" slot-scope="scope">
                    <p>采购端</p>
                    <p>观看人数(人)</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.total_num }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" width="200">
                <template slot="header" slot-scope="scope">
                    <p>采购端带货订单数量(单)</p>
                    <p>采购端带货订单金额(元)</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.order_total }}</p>
                    <p>{{ scope.row.order_amount_total | formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="jumpOrder(scope.row)">查看带货订单</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100,200]"
                :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
    </m-card>
</template>
<script>
import infoList from "@/mixins/infoList";
import {
    getShareLiveCategorys,
    getShareLiveRoomApplicationList
} from "@/api/shareLive";
import {getApplicationList} from "@/api/application";
import MDaterange from "@/components/mDate/daterange.vue";

export default {
    name: "shareLivePurchasingDataList",
    components: {MDaterange},
    mixins: [infoList],
    data() {
        return {
            shareLiveCategoryOption: [],
            applicationOption: [],
            date: [],
            listApi: getShareLiveRoomApplicationList
        }
    },
    filters: {
        formattedState(v) {
            let s = ""
            switch (v) {
                case 0:
                    s = "等待直播"
                    break;
                case 1:
                    s = "直播中"
                    break;
                case 2:
                    s = "已结束"
                    break;
            }
            return s;
        }
    },
    mounted() {
        if(this.$route.query.share_live_room_id){
            this.searchInfo.share_live_room_id = parseInt(this.$route.query.share_live_room_id)
        }
        this.getTableData()
        this.getShareLiveCategoryOption()
        this.getApplicationOption()
    },
    methods: {
        jumpOrder(row){
            this.$_blank('/layout/orderIndex/allOrderIndex',{
                share_live_room_id: row.share_live_room_id,
                application_id: row.application_id
            })
        },
        search() {
            if (this.date.length) {
                this.searchInfo.start_at_search = this.date[0]
                this.searchInfo.end_at_search = this.date[1]
            }
            this.getTableData(1)
        },
        reSearch() {
            this.searchInfo = {}
            this.date = []
        },
        // 获取直播间分类option
        async getShareLiveCategoryOption() {
            const {code, data} = await getShareLiveCategorys()
            if (code === 0) {
                this.shareLiveCategoryOption = data;
            }
        },
        // 获取采购端option
        async getApplicationOption() {
            const {code, data} = await getApplicationList({page: 1, pageSize: 9999})
            if (code === 0) {
                this.applicationOption = data.list
            }
        },
    }
}
</script>
<style scoped lang="scss">
.list-img {
  min-width: 60px;
  min-height: 60px;
  max-width: 60px;
  max-height: 60px;
}
::v-deep .search-term .line-input .fac .el-input--medium .el-input__inner {
    text-align: left;
}
</style>