package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"io"
	"io/ioutil"
	log2 "log"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"

	"github.com/chenhg5/collection"
	"github.com/gookit/color"
	"github.com/imroc/req/v3"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// 拉卡拉敏感信息掩码字段

func LaKaLaMaskFields() []string {
	return []string{"Cert", "PrivateKey"}
}

// 微信支付敏感信息掩码字段

func WechatPaymentMaskFields() []string {
	return []string{"AppSecret", "PayKey"}
}

// 汇聚支付敏感信息掩码字段

func HuiJuPaymentMaskFields() []string {
	return []string{"HmacVal"}
}

// 回显支付设置，敏感信息掩码处理

func ShowSystemConfig() (err error, conf config.Server) {
	conf = *config.Config()

	// 拉卡拉掩码
	utils.MaskFixedFields(&conf.Join.LakalaSetting, LaKaLaMaskFields())
	// 微信支付掩码
	utils.MaskMiddleFields(&conf.WechatPayment, WechatPaymentMaskFields())
	// 汇聚支付掩码
	utils.MaskMiddleFields(&conf.Join, HuiJuPaymentMaskFields())

	return nil, conf
}

type UpdateMask struct {
	Field string `json:"field"`
	Value string `json:"value"`
}

func UpdateHuiJuPaymentMask(params UpdateMask) (err error) {
	// 验证提交字段是否在掩码字段中
	if !utils.IsStringInSlice(params.Field, HuiJuPaymentMaskFields()) {
		err = errors.New("字段验证错误，请重试")
		return
	}

	conf := *config.Config()
	// 根据字段名设置结构体字段的值
	if err = utils.SetValueByFieldName(&conf.Join, params.Field, params.Value); err != nil {
		return
	}

	cs := utils.StructToMap(conf)
	vp := viper.New()
	vp.Set("Join", cs["Join"])
	if err = vp.WriteConfigAs(utils.GetRunPath() + "conf/join.yaml"); err != nil {
		return err
	}
	if err = vp.Unmarshal(&config.GVA_CONFIG); err != nil {
		return
	}

	return
}

func UpdateWechatPaymentMask(params UpdateMask) (err error) {
	// 验证提交字段是否在掩码字段中
	if !utils.IsStringInSlice(params.Field, WechatPaymentMaskFields()) {
		err = errors.New("字段验证错误，请重试")
		return
	}

	conf := *config.Config()
	// 根据字段名设置结构体字段的值
	if err = utils.SetValueByFieldName(&conf.WechatPayment, params.Field, params.Value); err != nil {
		return
	}

	cs := utils.StructToMap(conf)
	vp := viper.New()
	vp.Set("WechatPayment", cs["WechatPayment"])
	if err = vp.WriteConfigAs(utils.GetRunPath() + "conf/wechatpayment.yaml"); err != nil {
		return err
	}
	if err = vp.Unmarshal(&config.GVA_CONFIG); err != nil {
		return
	}

	return
}

func UpdateLaKaLaPaymentMask(params UpdateMask) (err error) {
	// 验证提交字段是否在掩码字段中
	if !utils.IsStringInSlice(params.Field, LaKaLaMaskFields()) {
		err = errors.New("字段验证错误，请重试")
		return
	}

	conf := *config.Config()
	// 根据字段名设置结构体字段的值
	if err = utils.SetValueByFieldName(&conf.Join.LakalaSetting, params.Field, params.Value); err != nil {
		return
	}

	cs := utils.StructToMap(conf)
	vp := viper.New()
	vp.Set("Join", cs["Join"])
	if err = vp.WriteConfigAs(utils.GetRunPath() + "conf/join.yaml"); err != nil {
		return err
	}
	if err = vp.Unmarshal(&config.GVA_CONFIG); err != nil {
		return
	}

	return
}

// @description   set system config,
//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetSystemConfig
//@description: 设置配置文件
//@param: system model.System
//@return: err error

func SetSystemConfig(system model.System) (err error) {
	conf := *config.Config()

	// 敏感字段不更新，单独接口修改
	system.Config.WechatPayment.AppSecret = conf.WechatPayment.AppSecret
	system.Config.WechatPayment.PayKey = conf.WechatPayment.PayKey
	system.Config.Join.HmacVal = conf.Join.HmacVal
	system.Config.Join.LakalaSetting.Cert = conf.Join.LakalaSetting.Cert
	system.Config.Join.LakalaSetting.PrivateKey = conf.Join.LakalaSetting.PrivateKey

	cs := utils.StructToMap(system.Config)

	//mysql start
	vp := viper.New()
	vp.Set("Mysql", cs["Mysql"])
	//mysql end

	//Rabbitmq start
	vp.Set("Rabbitmq", cs["Rabbitmq"])

	//Rabbitmq end

	//Redis start
	vp.Set("Redis", cs["Redis"])

	//es start
	vp.Set("ElasticSearch", cs["ElasticSearch"])

	//es end
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/mysql.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	//Redis end

	//wechat start
	vp = viper.New()
	vp.Set("Wechat", cs["Wechat"])
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/wechat.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	//wechat end

	//wechat start
	vp = viper.New()
	vp.Set("Join", cs["Join"])
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/join.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	//wechat end
	//
	//Payment start
	vp = viper.New()
	vp.Set("Payment", cs["Payment"])
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/payment.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	//Payment end

	//AttachmentType start
	vp = viper.New()
	vp.Set("AttachmentType", cs["AttachmentType"])

	//AttachmentType end
	//
	//Local start
	vp.Set("Local", cs["Local"])
	//Local end
	//
	//Qiniu start
	vp.Set("Qiniu", cs["Qiniu"])
	//Qiniu end
	//
	//Alioss start
	vp.Set("Alioss", cs["Alioss"])
	//Alioss end
	//Huaweiobs start
	vp.Set("Huaweiobs", cs["Huaweiobs"])
	//Huaweiobs end
	//
	//Cos start
	vp.Set("Cos", cs["Cos"])
	//Cos end
	//
	//Alisms start
	vp.Set("Alisms", cs["Alisms"])
	//Alisms end
	//
	//Tencentsms start
	vp.Set("Tencentsms", cs["Tencentsms"])
	//Tencentsms end
	//
	//Email start
	vp.Set("Email", cs["Email"])
	vp.Set("Express", cs["Express"])
	vp.Set("TencentMap", cs["TencentMap"])
	vp.Set("Amap", cs["Amap"])
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/attachment.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	//Email end
	//
	//JWT start
	vp = viper.New()
	vp.Set("JWT", cs["JWT"])
	//JWT end
	//
	//Zap start
	vp.Set("Zap", cs["Zap"])
	//Zap end
	//
	//Casbin start
	vp.Set("Casbin", cs["Casbin"])
	//Casbin end
	//
	//System start
	vp.Set("System", cs["System"])
	//System end
	//
	//Captcha start
	vp.Set("Captcha", cs["Captcha"])
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/config.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	//Captcha end
	//微信公众号配置WechatOfficial start
	vp = viper.New()
	vp.Set("WechatOfficial", cs["WechatOfficial"])
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/wechatofficial.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	//微信公众号配置WechatOfficial end

	//wechat start
	vp = viper.New()
	vp.Set("WechatPayment", cs["WechatPayment"])
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/wechatpayment.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	//wechat end

	//聚合支付-数据通 start
	vp = viper.New()
	vp.Set("AggregatedPayment", cs["AggregatedPayment"])
	err = vp.WriteConfigAs(utils.GetRunPath() + "conf/aggregatedpayment.yaml")
	_ = vp.Unmarshal(&config.GVA_CONFIG)
	if err != nil {
		return err
	}
	return err
}

var supplyPath = "/data/goSupply/"

// var supplyH5Path = "/data/wwwroot/yzshop"
var supplyH5Path = "/data/wwwroot/yzshop/"

func ExecCommand(strCommand string) string {
	cmd := exec.Command("/bin/bash", "-c", strCommand)

	stdout, _ := cmd.StdoutPipe()
	if err := cmd.Start(); err != nil {
		fmt.Println("Execute failed when Start:" + err.Error())
		return ""
	}

	out_bytes, _ := ioutil.ReadAll(stdout)
	stdout.Close()

	if err := cmd.Wait(); err != nil {
		fmt.Println("Execute failed when Wait:" + err.Error())
		return ""
	}
	return string(out_bytes)
}

func ExecCmd(cmdLine string) (err error) {
	cmd := exec.Command("bash", "-c", cmdLine)
	in := bytes.NewBuffer(nil)
	cmd.Stdin = in //绑定输入
	var out bytes.Buffer
	cmd.Stdout = &out //绑定输出
	cmd.Dir = supplyPath
	err = cmd.Start()
	if err != nil {
		log2.Fatal(err)
	}
	log2.Println(cmd.Args)
	err = cmd.Wait()
	if err != nil {
		log2.Printf("Command finished with error: %v", err)
	}

	return
}

func ExecCmdH5(cmdLine string) (err error) {
	cmd := exec.Command("bash", "-c", cmdLine)
	in := bytes.NewBuffer(nil)
	cmd.Stdin = in //绑定输入
	var out bytes.Buffer
	cmd.Stdout = &out //绑定输出
	cmd.Dir = supplyH5Path
	err = cmd.Start()
	if err != nil {
		log2.Fatal(err)
	}
	log2.Println(cmd.Args)
	err = cmd.Wait()
	if err != nil {
		log2.Printf("Command finished with error: %v", err)
	}

	return
}

func GetRemoteVersion() (err error, httpResData string, isUpdate string) {
	isUpdate = "0"
	err, httpResByte := utils.Get("https://downloads.yunzmall.com/supply_data/supply_version.txt", nil)
	if err != nil {
		return
	}
	if string(httpResByte) == "" {
		color.Info.Println("返回数据为空", string(httpResData))
		return
	}
	localVersion := strings.ReplaceAll(gva.BuildVersion, ".", "")
	localVersion = strings.Replace(localVersion, "\n", "", -1)
	localVersion = strings.Replace(localVersion, " ", "", -1)
	remoteVersion := strings.ReplaceAll(string(httpResByte), ".", "")
	remoteVersion = strings.Replace(remoteVersion, "\n", "", -1)
	remoteVersion = strings.Replace(remoteVersion, " ", "", -1)

	// 转换为数字进行比较
	localVersionNum, _ := strconv.Atoi(localVersion)
	remoteVersionNum, _ := strconv.Atoi(remoteVersion)
	if localVersionNum < remoteVersionNum {
		isUpdate = "1"
	}
	httpResData = string(httpResByte)
	fmt.Println(string(httpResData))
	return

}

// 返回带小数点的版本号 用于前端判断
func GetLocalH5VersionNoProcessing() (version string, err error) {
	localVersionTxt, err := os.ReadFile(supplyH5Path + "h5/h5_version.txt")
	if err != nil {
		return
	}
	version = string(localVersionTxt)
	return
}

// 返回带小数点的版本号 用于前端判断
func GetLocalSmallShopH5VersionNoProcessing() (version string, err error) {
	localVersionTxt, err := os.ReadFile(supplyH5Path + "smallShopH5/small-shop-front_version.txt")
	if err != nil {
		return
	}
	version = string(localVersionTxt)
	return
}

func GetLocalH5Version() (version string, err error) {

	localVersionTxt, _ := os.ReadFile(supplyH5Path + "h5/h5_version.txt")
	if err != nil {
		return
	}
	//fmt.Println(string(content))
	localVersion := strings.ReplaceAll(string(localVersionTxt), ".", "")
	localVersion = strings.Replace(localVersion, "\n", "", -1)
	localVersion = strings.Replace(localVersion, " ", "", -1)
	version = localVersion
	return
}

func GetLocalSmallShopH5Version() (version string, err error) {

	localVersionTxt, _ := os.ReadFile(supplyH5Path + "smallShopH5/small-shop-front_version.txt")
	if err != nil {
		return
	}
	//fmt.Println(string(content))
	localVersion := strings.ReplaceAll(string(localVersionTxt), ".", "")
	localVersion = strings.Replace(localVersion, "\n", "", -1)
	localVersion = strings.Replace(localVersion, " ", "", -1)
	version = localVersion
	return
}
func GetH5RemoteVersion() (err error, httpResData string, isUpdate string) {
	isUpdate = "0"
	err, httpResByte := utils.Get("https://downloads.yunzmall.com/supply_data/h5_version.txt", nil)
	if err != nil {
		return
	}
	if string(httpResByte) == "" {
		color.Info.Println("返回数据为空", string(httpResData))
		return
	}

	localVersion, _ := GetLocalH5Version()
	remoteVersion := strings.ReplaceAll(string(httpResByte), ".", "")
	remoteVersion = strings.Replace(remoteVersion, "\n", "", -1)
	remoteVersion = strings.Replace(remoteVersion, " ", "", -1)

	// 转换为数字进行比较
	localVersionNum, _ := strconv.Atoi(localVersion)
	remoteVersionNum, _ := strconv.Atoi(remoteVersion)
	if localVersionNum < remoteVersionNum {
		isUpdate = "1"
	}
	httpResData = string(httpResByte)
	fmt.Println(string(httpResData))
	return

}
func GetSmallShopH5RemoteVersion() (err error, httpResData string, isUpdate string) {
	isUpdate = "0"
	err, httpResByte := utils.Get("https://downloads.yunzmall.com/supply_data/small-shop-front_version.txt", nil)
	if err != nil {
		return
	}
	if string(httpResByte) == "" {
		color.Info.Println("GetSmallShopH5RemoteVersion 返回数据为空", string(httpResData))
		return
	}

	localVersion, _ := GetLocalSmallShopH5Version()
	remoteVersion := strings.ReplaceAll(string(httpResByte), ".", "")
	remoteVersion = strings.Replace(remoteVersion, "\n", "", -1)
	remoteVersion = strings.Replace(remoteVersion, " ", "", -1)
	log.Log().Debug("GetSmallShopH5RemoteVersion localVersion", zap.Any("localVersion", localVersion))

	log.Log().Debug("GetSmallShopH5RemoteVersion remoteVersion", zap.Any("remoteVersion", remoteVersion))

	log.Log().Debug("", zap.Any("MarketingPlugin", gva.GlobalAuth.MarketingPlugin), zap.Any("LocalEnv", utils.LocalEnv()))

	if localVersion == "" { //如果本地没有版本号，就更新

		if collection.Collect(gva.GlobalAuth.MarketingPlugin).Contains(17) == true || utils.LocalEnv() == false {
			isUpdate = "1"
		}

	}

	// 转换为数字进行比较
	localVersionNum, _ := strconv.Atoi(localVersion)
	remoteVersionNum, _ := strconv.Atoi(remoteVersion)
	if localVersionNum < remoteVersionNum {

		if collection.Collect(gva.GlobalAuth.MarketingPlugin).Contains(17) == true || utils.LocalEnv() == false {
			isUpdate = "1"
		}

	}
	httpResData = string(httpResByte)
	fmt.Println(string(httpResData))
	return

}

func GetUpdateLog(version string) (log string) {
	if version == "" {
		return
	}
	version = strings.Replace(version, "\n", "", -1)
	version = strings.Replace(version, " ", "", -1)

	err, httpResByte := utils.Get("https://downloads.yunzmall.com/supply_data/change_log_"+version+".txt", nil)
	if err != nil {
		return
	}

	//remoteTxt := strings.ReplaceAll(string(httpResByte), ".", "")
	//remoteTxt = strings.Replace(remoteTxt, "\n", "", -1)
	//remoteTxt = strings.Replace(remoteTxt, " ", "", -1)

	log = string(httpResByte)
	return

}
func restartMq() (err error) {
	command := "systemctl restart  mq"
	err = ExecCmd(command)
	if err != nil {
		log2.Println(err)
		return
	}

	return
}
func RestartSystem() {
	ok, err := source.Redis().Publish(context.Background(), "systemUpdate", "update").Result()
	if err != nil {
		log.Log().Info("source.Redis  Failed!", zap.String("err", err.Error()), zap.Any("ok", ok))

	}
}
func KillSupply() (err error) {

	go func() {
		time.Sleep(10 * time.Second)
		RestartSystem()

	}()

	//command := "pkill -9 supply"
	//err = ExecCmd(command)
	//if err != nil {
	//	log2.Println(err)
	//	return
	//}

	return
}

func HttpDownloadSupply(url, path string) {
	callback := func(info req.DownloadInfo) {
		if info.Response.Response != nil {
			log.Log().Info("HttpDownloadSupply 更新进度", zap.Any("百分比：", float64(info.DownloadedSize)/float64(info.Response.ContentLength)*100.0))
		}
	}
	client := req.C().SetOutputDirectory(path)
	resp, err := client.R().SetRetryCount(2). //下载失败 重试次数
							SetRetryBackoffInterval(1*time.Second, 5*time.Second). //下载失败重试间隔
							SetOutputFile("supply.zip").
							SetDownloadCallback(callback).
							Get(url)
	if err != nil || resp.Err != nil {
		log.Log().Error("下载更新错误", zap.Any("err", err), zap.Any("resp.Err", resp.Err))
	}
}
func HttpDownloadSmallShopH5Supply(url, path string) {
	callback := func(info req.DownloadInfo) {
		if info.Response.Response != nil {
			log.Log().Info("HttpDownloadSmallShopH5Supply 更新进度", zap.Any("百分比：", float64(info.DownloadedSize)/float64(info.Response.ContentLength)*100.0))
		}
	}
	client := req.C().SetOutputDirectory(path)
	resp, err := client.R().SetRetryCount(2). //下载失败 重试次数
							SetRetryBackoffInterval(1*time.Second, 5*time.Second). //下载失败重试间隔
							SetOutputFile("small_shop_h5.zip").
							SetDownloadCallback(callback).
							Get(url)
	if err != nil || resp.Err != nil {
		log.Log().Error("下载更新错误", zap.Any("err", err), zap.Any("resp.Err", resp.Err))
	}
}

var IsUpdate = false

func GODownLoadUpdateSupply(version string) {

	if IsUpdate {
		return
	}
	IsUpdate = true
	var err error

	var remoteVersion string

	domainPath := "yzshop"
	if version != "" {
		// 指定版本
		remoteVersion = version
	} else {
		// 最新版本
		err, remoteVersion, _ = GetRemoteVersion()
	}
	if err != nil {
		return
	}
	remoteVersion = strings.Replace(remoteVersion, "\n", "", -1)
	remoteVersion = strings.Replace(remoteVersion, " ", "", -1)
	if remoteVersion == "" {
		err = errors.New("版本号异常")
		return
	}
	if gva.BuildVersion == "" || (gva.BuildVersion < remoteVersion) {
		url := fmt.Sprintf("https://downloads.yunzmall.com/supply_data/supply_%s.zip", remoteVersion)

		HttpDownloadSupply(url, supplyPath)

		//command := "rm -rf  " + supplyPath + "supply"
		//err = ExecCmd(command)
		//if err != nil {
		//	log2.Println(err)
		//	return
		//}
		err = ExecCmd("unzip -o " + supplyPath + "supply.zip;chmod 777 supply")
		if err != nil {
			log.Log().Error("解压失败", zap.Any("err", err))
			return
		}

		err = ExecCmd("cp -rf " + supplyPath + "backend_dist/* " + "/data/wwwroot/" + domainPath + "/super/")
		if err != nil {
			log.Log().Error("cp backend_dist 失败", zap.Any("err", err))
			return
		}
		err = ExecCmd("cp -rf " + supplyPath + "pc_dist/* " + "/data/wwwroot/" + domainPath + "/")
		if err != nil {
			log.Log().Error("cp pc_dist 失败", zap.Any("err", err))
			return
		}

		time.Sleep(10 * time.Second)
		RedisChannelPush()

	}

}

func GODownLoadH5UpdateSupply(version string) {

	var err error

	var remoteVersion string
	var res *http.Response
	var f *os.File

	//domainPath := ExecCommand("ls /data/wwwroot/")
	//domainPath = strings.Replace(domainPath, "\n", "", -1)
	//domainPath = strings.Replace(domainPath, " ", "", -1)\
	//domainPath := "yzshop"
	if version != "" {
		// 指定版本
		remoteVersion = version
	} else {
		// 最新版本
		err, remoteVersion, _ = GetH5RemoteVersion()
	}
	if err != nil {
		return
	}
	remoteVersion = strings.Replace(remoteVersion, "\n", "", -1)
	remoteVersion = strings.Replace(remoteVersion, " ", "", -1)

	//remoteVersion = strings.ReplaceAll(remoteVersion, ".", "")
	remoteVersionA := strings.ReplaceAll(remoteVersion, ".", "")

	if remoteVersion == "" {
		err = errors.New("版本号异常")
		return
	}
	localVersion, _ := GetLocalH5Version()

	// 转换为数字进行比较
	localVersionNum, _ := strconv.Atoi(localVersion)
	remoteVersionNum, _ := strconv.Atoi(remoteVersionA)
	if localVersion == "" || (localVersionNum < remoteVersionNum) {
		//url := "https://downloads.yunzmall.com/supply_data/supply_1.0.2.zip"
		url := fmt.Sprintf("https://downloads.yunzmall.com/supply_data/%s.zip", remoteVersion)
		res, err = http.Get(url)
		if err != nil {
			log2.Println(err)
			err = errors.New("更新地址错误")
			return
		}
		f, err = os.Create(supplyH5Path + "h5.zip")
		if err != nil {
			log2.Println(err)
		}
		_, err = io.Copy(f, res.Body)

		command := "rm -rf  " + supplyH5Path + "h5/"
		err = ExecCmdH5(command)
		if err != nil {
			log2.Println(err)
			return
		}
		err = ExecCmdH5("unzip -o " + supplyH5Path + "h5.zip")
		if err != nil {
			log2.Println(err)
			return
		}
		//
		//err = ExecCmd("cp -rf " + supplyPath + "backend_dist/* " + "/data/wwwroot/" + domainPath + "/super/")
		//if err != nil {
		//	log2.Println(err)
		//	return
		//}
		//err = ExecCmd("cp -rf " + supplyPath + "pc_dist/* " + "/data/wwwroot/" + domainPath + "/")
		//if err != nil {
		//	log2.Println(err)
		//	return
		//}

		time.Sleep(10 * time.Second)
		//RedisChannelPush()

	}

}
func GODownLoadSmallShopH5UpdateSupply(version string) {
	var err error
	var remoteVersion string

	if version != "" {
		// 指定版本
		remoteVersion = version
		log.Log().Debug("更新为前端传过来的小商店版本 ", zap.Any("version", version))

	} else {
		// 最新版本
		err, remoteVersion, _ = GetSmallShopH5RemoteVersion()
		log.Log().Debug("获取最新小商店版本 ", zap.Any("err", err), zap.Any("remoteVersion", remoteVersion))
	}
	if err != nil {
		return
	}
	remoteVersion = strings.Replace(remoteVersion, "\n", "", -1)
	remoteVersion = strings.Replace(remoteVersion, " ", "", -1)

	//remoteVersion = strings.ReplaceAll(remoteVersion, ".", "")
	remoteVersionA := strings.ReplaceAll(remoteVersion, ".", "")

	if remoteVersion == "" {
		err = errors.New("版本号异常")
		return
	}
	localVersion, _ := GetLocalSmallShopH5Version()
	log.Log().Debug("获取本地小商店当前版本 ", zap.Any("localVersion", localVersion), zap.Any("remoteVersionA", remoteVersionA))

	// 转换为数字进行比较
	localVersionNum, _ := strconv.Atoi(localVersion)
	remoteVersionNum, _ := strconv.Atoi(remoteVersionA)
	if localVersion == "" || (localVersionNum < remoteVersionNum) {
		url := fmt.Sprintf("https://downloads.yunzmall.com//supply_data/small-shop-front_%s.zip", remoteVersion)
		HttpDownloadSmallShopH5Supply(url, supplyH5Path)
		command := "rm -rf  " + supplyH5Path + "smallShopH5/"
		err = ExecCmdH5(command)
		if err != nil {
			log.Log().Error("ExecCmdH5 err  ", zap.String("command", command), zap.Error(err))
			return
		}
		err = ExecCmdH5("unzip -o " + supplyH5Path + "small_shop_h5.zip")
		if err != nil {
			log.Log().Error("ExecCmdH5 unzip  err small_shop_h5.zip  ", zap.Error(err))
			return
		}

		// 新增：创建并写入small-shop-front版本文件
		versionFilePath := supplyH5Path + "smallShopH5/small-shop-front_version.txt"
		if err := os.WriteFile(versionFilePath, []byte(remoteVersion), 0644); err != nil {
			log.Log().Error("写入small-shop-front版本文件失败", zap.String("路径", versionFilePath), zap.Error(err))
			return
		}

		time.Sleep(10 * time.Second)
		//RedisChannelPush()

	}

}
func DownLoadUpdateSupply(version string) (err error) {
	go GODownLoadUpdateSupply(version)
	return
}

func DownLoadH5UpdateSupply(version string) (err error) {
	go GODownLoadH5UpdateSupply(version)
	return
}
func DownLoadSmallShopH5UpdateSupply(version string) (err error) {
	go GODownLoadSmallShopH5UpdateSupply(version)
	return
}

func RedisChannelPush() {

	ok, err := source.Redis().Publish(context.Background(), "systemUpdate", "update").Result()
	if err != nil {
		log.Log().Info("source.Redis  Failed!", zap.String("err", err.Error()), zap.Any("ok", ok))

	}

}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: GetServerInfo
//@description: 获取服务器信息
//@return: server *utils.Server, err error

func GetServerInfo() (server *utils.Server, err error) {
	var s utils.Server
	s.Os = utils.InitOS()

	if s.Cpu, err = utils.InitCPU(); err != nil {
		log.Log().Error("func utils.InitCPU() Failed!", zap.String("err", err.Error()))
		return &s, err
	}
	if s.Rrm, err = utils.InitRAM(); err != nil {
		log.Log().Error("func utils.InitRAM() Failed!", zap.String("err", err.Error()))
		return &s, err
	}
	if s.Disk, err = utils.InitDisk(); err != nil {
		log.Log().Error("func utils.InitDisk() Failed!", zap.String("err", err.Error()))
		return &s, err
	}
	if s.Load, err = utils.InitLoad(); err != nil {
		log.Log().Error("func utils.InitLoad() Failed!", zap.String("err", err.Error()))
		return &s, err
	}

	return &s, nil
}
