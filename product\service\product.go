package service

import (
	"archive/zip"
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"gin-vue-admin/cmd/gva"
	"io"
	"math"
	"os"
	"path/filepath"
	"product/model"
	"product/mq"
	"product/other"
	"product/request"
	"product/response"
	"product/stock"
	shippingModel "shipping/model"
	shopSetting "shop/setting"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"
	levelModel "user/level"
	model2 "user/model"
	"yz-go/cache"
	"yz-go/common_data"
	"yz-go/component/log"
	"yz-go/config"
	model3 "yz-go/model"
	yzRequest "yz-go/request"
	response2 "yz-go/response"
	"yz-go/service"
	"yz-go/setting"
	"yz-go/source"
	"yz-go/utils"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/chenhg5/collection"
	"github.com/emirpasic/gods/sets/hashset"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Sku struct {
	source.Model
	Title         string           `json:"title"`
	Price         uint             `json:"price"`
	CostPrice     uint             `json:"cost_price"`
	OriginPrice   uint             `json:"origin_price"`
	GuidePrice    uint             `json:"guide_price"`
	ActivityPrice uint             `json:"activity_price"`
	Stock         int              `json:"stock"`
	LockStock     int              `json:"lock_stock" gorm:"-"`
	Weight        int              `json:"weight"`
	IsDisplay     int              `json:"is_display"`
	OriginalSkuID int              `json:"original_sku_id"`
	SpecId        string           `json:"specId"`
	Sn            string           `json:"sn"`
	Barcode       string           `json:"barcode"`
	ImageUrl      string           `json:"image_url"`
	SupplierID    uint             `json:"supplier_id"`
	ProductID     uint             `json:"product_id"`
	Options       model.Options    `json:"options"`
	AliProduct    AliProduct       `json:"ali_product" form:"ali_product"`
	MinNumber     int              `json:"min_number" form:"min_number" gorm:"column:min_number;comment:起订量;type:int;size:11;"`
	SourceStrId   string           `json:"source_str_id" form:"source_str_id" gorm:"column:source_str_id;comment:上游规格id;index;"`
	SpecItems     []model.SpecItem `json:"spec_items,omitempty" gorm:"many2many:sku_spec_items;"`
	Describe      string           `json:"describe" form:"describe" gorm:"type:longtext;column:describe;comment:sku详情;"`
	Code          string           `json:"code"`
	Desc          string           `json:"desc" form:"desc" gorm:"column:desc;comment:规格描述;type:varchar(200);size:200;"`
	Gallery       model.Gallery    `json:"gallery" form:"gallery" gorm:"column:gallery;comment:组图;type:text;"`
	VideoUrl      string           `json:"video_url" gorm:"column:video_url;comment:首图视频;"`
	Attrs         model.Attrs      `json:"attrs" form:"attrs" gorm:"column:attrs;comment:属性json列表(冗余信息);type:text;"`
	model.ProductBill

	ProfitRate float64 `json:"profit_rate"`
}

func (Sku) TableName() string {
	return "skus"
}
func (s *Sku) AfterFind(tx *gorm.DB) (err error) {
	if s.Options == nil {
		s.Options = model.Options{{SpecName: "规格", SpecItemName: s.Title}}
	}
	return
}

type SkuForUpdate struct {
	source.SoftDel
	ID            uint              `json:"id" form:"id" gorm:"primarykey"`
	UpdatedAt     *source.LocalTime `json:"updated_at"`
	Title         string            `json:"title"`
	Price         uint              `json:"price"`
	CostPrice     uint              `json:"cost_price"`
	OriginPrice   uint              `json:"origin_price"`
	GuidePrice    uint              `json:"guide_price"`
	ActivityPrice uint              `json:"activity_price"`
	Stock         int               `json:"stock"`
	Weight        int               `json:"weight"`
	Code          string            `json:"code"`
	Sn            string            `json:"sn"`
	ImageUrl      string            `json:"image_url"`
	Describe      string            `json:"describe" form:"describe" gorm:"type:longtext;column:describe;comment:sku详情;"` //sku详情
}

func (SkuForUpdate) TableName() string {
	return "skus"
}

type AliProduct struct {
	source.Model
	//ProductID    int64  `json:"product_id"`
	SkuID        int64  `json:"sku_id"`
	AliSkuID     string `json:"ali_sku_id"`
	AliProductID int64  `json:"ali_product_id"`
	ShopID       string `json:"shop_id"`
	AutoPay      int64  `json:"auto_pay"`
}

type ProductForUpdate struct {
	source.Model
	Title                    string               `json:"title"`
	OriginPrice              uint                 `json:"origin_price"`
	GuidePrice               uint                 `json:"guide_price"`
	Price                    uint                 `json:"price"`
	Stock                    uint                 `json:"stock"`
	CostPrice                uint                 `json:"cost_price"`
	ActivityPrice            uint                 `json:"activity_price"`
	Sales                    uint                 `json:"sales"`
	Sn                       string               `json:"sn"`
	SingleOption             int                  `json:"single_option"`
	Desc                     string               `json:"desc"`
	ImageUrl                 string               `json:"image_url"`
	VideoUrl                 string               `json:"video_url"`
	Unit                     string               `json:"unit"`
	Barcode                  string               `json:"barcode"`
	Freight                  int                  `json:"freight"`
	FreightType              int                  `json:"freight_type"`
	DetailImages             string               `json:"detail_images"`
	BrandID                  uint                 `json:"brand_id"`
	SupplierID               uint                 `json:"supplier_id"`
	GatherSupplyID           uint                 `json:"gather_supply_id"`
	Category1ID              uint                 `json:"category1_id"`
	Category2ID              uint                 `json:"category2_id"`
	Category3ID              uint                 `json:"category3_id"`
	FreightTemplateID        uint                 `json:"freight_template_id"`
	Source                   int                  `json:"source"`
	Sort                     int                  `json:"sort"`
	SourceGoodsID            uint                 `json:"source_goods_id"`
	SourceGoodsIDString      string               `json:"source_goods_id_string" form:"source_goods_id_string"`
	ChildTitle               string               `json:"child_title"`
	Skus                     []Sku                `json:"skus" gorm:"foreignKey:ProductID"`
	Gallery                  model.Gallery        `json:"gallery"`
	Qualifications           model.Qualifications `json:"qualifications"`
	Attrs                    model.Attrs          `json:"attrs"`
	MaxPrice                 uint                 `json:"max_price"`
	MinPrice                 uint                 `json:"min_price"`
	MinBuyQty                uint                 `json:"min_buy_qty"` // 最少起订量
	Code                     string               `json:"code"`
	Weight                   int                  `json:"weight" form:"weight" gorm:"-"`
	SkuID                    uint                 `json:"sku_id" form:"sku_id" gorm:"-"`
	BillPosition             int                  `json:"bill_position" form:"bill_position"` //发票信息存储位置  1商品本体 2sku
	TaxCode                  string               `json:"tax_code"`                           //税收分类编码
	TaxProductName           string               `json:"tax_product_name"`                   //税收商品名称
	TaxShortName             string               `json:"tax_short_name"`                     //税收分类简称
	TaxOption                string               `json:"tax_option"`                         //规格型号
	TaxUnit                  string               `json:"tax_unit"`                           //单位
	FavorablePolicy          string               `json:"favorable_policy"`                   //优惠政策
	IsFavorablePolicy        int                  `json:"is_favorable_policy"`                //是否使用优惠政策
	FreeOfTax                int                  `json:"free_of_tax"`                        //0正常税率1出口免税和其他免税优惠政策2不征增值税3普通零税率
	ShortCode                string               `json:"short_code"`                         // 商品简码
	TaxMeasurePrice          uint                 `json:"tax_measure_price"`                  //税收计量单价
	TaxRate                  int                  `json:"tax_rate"`                           //税率
	IsTaxLogo                int                  `json:"is_tax_logo"`
	IsDisplay                int                  `json:"is_display"`
	StatusLock               int                  `json:"status_lock"`
	MD5                      string               `json:"md5"`
	SkuImage                 string               `json:"sku_image" form:"sku_image" gorm:"-"`             //sku图片 单规格使用
	OriginalSkuId            int                  `json:"original_sku_id" form:"original_sku_id" gorm:"-"` //规格第第三方id 单规格使用
	SupplierSourceID         uint                 `json:"supplier_source_id" form:"supplier_source_id"`
	SupplierSourceCategoryID uint                 `json:"supplier_source_category_id" form:"supplier_source_category_id"`
	Category1                other.Category       `json:"category_1" form:"category_1"`
	Category2                other.Category       `json:"category_2" form:"category_2"`
	Category3                other.Category       `json:"category_3" form:"category_3"`
	IsPlugin                 int                  `json:"is_plugin" gorm:"column:is_plugin;default:0;"`
	PluginID                 int                  `json:"plugin_id" gorm:"column:plugin_id;default:0;"`
	ProfitRate               float64              `json:"profit_rate"`
	IsBill                   int                  `json:"is_bill"`
	SupplyLine               string               `json:"supply_line"`
	IsSupplyLine             uint                 `json:"is_supply_line"`
	ShopName                 string               `json:"shop_name"`
	JushuitanDistributorCoId string               `json:"jushuitan_distributor_co_id"`
	SupplySupplierId         string               `json:"supply_supplier_id"`
	JushuitanUploadStatus    int                  `json:"jushuitan_upload_status" `
	StopDateTime             string               `json:"stop_date_time"`
	StartDateTime            string               `json:"start_date_time"`
	Brand                    model.Brand          `json:"brand"`
	SourceName               string               `json:"source_name"`
	LocationID               uint                 `json:"location_id"`
	// 会员价独立开关
	UserPriceSwitch int `json:"user_price_switch" gorm:"column:user_price_switch;comment:会员价独立开关;"`
	// 会员价
	UserPrice         model.UserPrice `json:"user_price" gorm:"column:user_price;comment:会员价设置;"`
	IsAutoUpdateCost  int             `json:"is_auto_update_cost"`
	IsAutoUpdatePrice int             `json:"is_auto_update_price"`
	GatherSupply      GatherSupply    `json:"gather_supply"`
}

func (s *Sku) BeforeSave(tx *gorm.DB) (err error) {
	if s.SpecItems != nil {
		// 保存SpecItems的冗余到Options中
		for _, v := range s.SpecItems {
			if v.Spec == nil {
				continue
			}
			s.Options = append(s.Options, model.Option{
				SpecName:     v.Spec.Title,
				SpecItemName: v.Value,
			})
		}
	}
	return
}

func (ProductForUpdate) TableName() string {
	return "products"
}

// 获取会员等级权重顺序返回0-10  0代表没有会员等级  1代表会员等级1级
func GetLevel(uid uint) (level int) {
	level = 0
	var user model2.User
	err := source.DB().Preload("UserLevel").Where("id = ?", uid).First(&user).Error
	if err != nil {
		return
	}

	return user.UserLevel.Level
}

// GetProductForUpdate
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProductForUpdate
// @description: 根据id获取Product完整信息
// @param: id uint
// @return: err error, product ProductForUpdate
func GetProductForUpdate(id uint) (err error, product ProductForUpdate) {
	err = source.DB().Preload("Brand").Preload("GatherSupply").Preload("Skus").Preload("Skus.AliProduct").Where("id = ?", id).First(&product).Error
	if err != nil {
		return
	}
	for i, sku := range product.Skus {
		err, sku.LockStock = stock.GetSkuLockStock(sku.ID)
		product.Skus[i] = sku
	}
	return
}

// CreateProduct
// @author: [piexlmax](https://github.com/piexlmax)
// @function: CreateProduct
// @description: 创建Product记录
// @param: product ProductForUpdate
// @return: err error
func CreateProduct(product ProductForUpdate) (err error, id uint) {
	if product.SingleOption != 1 && product.UserPriceSwitch == 1 {
		err = errors.New("多规格商品不支持会员价")
		return
	}
	var maxPrice uint
	var minPrice uint
	product.Stock = 0
	product.SupplyLine = gva.GlobalAuth.EncryptID
	for k, sku := range product.Skus {
		product.Skus[k].SupplierID = product.SupplierID
		product.Stock += uint(sku.Stock)
		if sku.Price > maxPrice {
			maxPrice = sku.Price
		}
		if minPrice == 0 || sku.Price <= minPrice {
			minPrice = sku.Price
		}
	}
	product.MaxPrice = maxPrice
	product.MinPrice = minPrice
	if len(product.DetailImages) > model.PRODUCT_DETAILIMAGES_COUNT_LIMIT || len(product.Gallery) > 10000 {
		err = errors.New("详情长度超出限制")
		return
	}
	product.ProfitRate = utils.ExecProfitRate(product.OriginPrice, product.Price)

	err = source.DB().Create(&product).Error
	if err != nil {
		return
	}
	err = mq.PublishMessage(product.ID, mq.Create, 0)
	return err, product.ID
}

func CreateCloudProduct(product ProductForUpdate) (err error, id uint) {
	var maxPrice uint
	var minPrice uint
	for k, sku := range product.Skus {
		product.Skus[k].SupplierID = product.SupplierID
		if sku.Price > maxPrice {
			maxPrice = sku.Price
		}
		if minPrice == 0 || sku.Price <= minPrice {
			minPrice = sku.Price
		}
	}
	product.MaxPrice = maxPrice
	product.MinPrice = minPrice

	product.Source = 98
	err = source.DB().Create(&product).Error
	if err != nil {
		return
	}
	err = mq.PublishMessage(product.ID, mq.Create, 0)
	return err, product.ID
}

// DeleteProduct
// @author: [piexlmax](https://github.com/piexlmax)
// @function: DeleteProduct
// @description: 删除Product记录
// @param: product ProductForUpdate
// @return: err error
func DeleteProduct(product model.Product) (err error) {
	err = source.DB().Delete(&product).Error

	if err != nil {
		return
	}
	err = source.DB().Delete(&[]model.Sku{}, "product_id = ?", product.ID).Error
	if err != nil {
		return
	}
	err = source.DB().Delete(&[]SupplyGoods{}, "product_id = ?", product.ID).Error
	if err != nil {
		return
	}
	err = mq.PublishMessage(product.ID, mq.Delete, 0)
	return err
}

// DeleteProductByIds
// @author: [piexlmax](https://github.com/piexlmax)
// @function: DeleteProductByIds
// @description: 批量删除Product记录
// @param: ids yzRequest.IdsReq
// @return: err error
func DeleteProductByIds(ids yzRequest.IdsReq, userID uint, ip string) (err error) {
	var products []Product
	err = source.DB().Where("id in ?", ids.Ids).Find(&products).Error
	if err != nil {
		return
	}
	var productTitle = make(map[uint]string)
	for _, product := range products {
		productTitle[product.ID] = product.Title
	}
	err = source.DB().Delete(&[]model.Product{}, "id in ?", ids.Ids).Error
	if err != nil {
		return
	}
	err = source.DB().Delete(&[]model.Sku{}, "product_id in ?", ids.Ids).Error
	if err != nil {
		return
	}
	err = source.DB().Delete(&[]SupplyGoods{}, "product_id in ?", ids.Ids).Error
	if err != nil {
		return
	}
	for _, v := range ids.Ids {
		err = mq.PublishMessage(v, mq.Delete, 0)
		if err != nil {
			return
		}
		service.CreateOperationRecord(userID, 1, ip, "删除了商品'"+productTitle[v]+"'")
		if err != nil {
			return
		}
	}
	return err
}

type SupplyGoods struct {
	source.Model
}

// DisplayProductByIds
// @author: [piexlmax](https://github.com/piexlmax)
// @function: DeleteProductByIds
// @description: 批量删除Product记录
// @param: ids yzRequest.IdsReq
// @return: err error
func DisplayProductByIds(ids yzRequest.IdsReqWithValue) (err error) {
	var handleData []uint
	for _, v := range ids.Ids {
		var product Product
		err = source.DB().First(&product, v).Error
		if err != nil {
			continue
		}
		if product.SupplierID != 0 {
			var productVerify ProductVerify
			err = source.DB().Where("product_id = ?", v).Where("supplier_id = ?", product.SupplierID).Order("created_at desc").First(&productVerify).Error
			if err == nil && (productVerify.Status == 0 || productVerify.IsDisplay == 0) {
				continue
			}
		}
		handleData = append(handleData, v)

	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	if len(handleData) > 0 {
		err = source.DB().Model(&model.Product{}).Where("`id` in ?", handleData).Update("is_display", ids.Value).Error
		for _, id := range handleData {
			if ids.Value == 0 {
				err = mq.PublishMessage(id, mq.Undercarriage, 0)
			} else {
				err = mq.PublishMessage(id, mq.OnSale, 0)
			}
		}
	} else {
		return errors.New("商品未审核通过")
	}

	return err
}

type ProductVerify struct {
	source.Model
	ProductID  uint `json:"product_id"`
	SupplierID uint `json:"supplier_id"`
	Status     int  `json:"status"`     //0未审核1审核完毕
	IsDisplay  int  `json:"is_display"` //同商品的is_display
}

// UpdateProduct
// @author: [piexlmax](https://github.com/piexlmax)
// @function: UpdateProduct
// @description: 更新Product记录
// @param: product *model.Product
// @return: err error
func UpdateProduct(product ProductForUpdate) (err error) {
	// 接口与回调都会调用此方法，回调调用时，如果是多规格商品，清空会员价
	if product.SingleOption != 1 {
		product.UserPriceSwitch = 0
		product.UserPrice = model.UserPrice{}
	}
	if product.SourceGoodsID == 23428 {
		log.Log().Info("更新商品测试2", zap.Any("info2", product))
	}
	var fullProduct model.Product
	err = source.DB().Model(&ProductForUpdate{}).Where("id = ?", product.ID).Find(&fullProduct).Error
	if err != nil {
		return
	}
	if len(product.DetailImages) > model.PRODUCT_DETAILIMAGES_COUNT_LIMIT || len(product.Gallery) > 10000 {
		err = errors.New("详情长度超出限制")
		return
	}
	if product.SupplierID != 0 {
		if product.IsDisplay != fullProduct.IsDisplay {
			var productVerify ProductVerify
			err = source.DB().Where("product_id = ?", product.ID).Where("supplier_id = ?", product.SupplierID).Order("created_at desc").First(&productVerify).Error
			if err == nil && (productVerify.Status == 0 || productVerify.IsDisplay == 0) {
				err = errors.New("商品还未审核或审核被驳回，请审核通过以后再进行上下架操作")
				return
			}
		}
	}

	return source.DB().Transaction(func(tx *gorm.DB) (err error) {
		// 此次提交的sku
		newSkuIDs := hashset.New()
		for k, sku := range product.Skus {
			// sku的supplierID
			product.Skus[k].SupplierID = fullProduct.SupplierID
			if sku.ID == 0 {
				continue
			}
			newSkuIDs.Add(sku.ID)

		}
		// 需要删除的sku
		var delSkus, oldSkus []Sku
		err = source.DB().Model(&product).Association("Skus").Find(&oldSkus)
		for _, oldSku := range oldSkus {
			if !newSkuIDs.Contains(oldSku.ID) {
				delSkus = append(delSkus, oldSku)
			}
		}
		if err != nil {
			return
		}
		//log.Log().Info("需要删除的sku", zap.Any("sku", delSkus))
		// 删除此次未提交的sku
		err = source.DB().Model(&product).Association("Skus").Delete(delSkus)
		if err != nil {
			return
		}

		var maxPrice uint
		var minPrice uint
		product.Stock = 0
		var minProfitRate float64
		for k, sku := range product.Skus {
			if sku.Price == 0 || sku.CostPrice == 0 {
				//价格为0,库存为0
				product.Skus[k].Stock = 0
			}
			product.Skus[k].SupplierID = product.SupplierID
			product.Stock += uint(sku.Stock)
			if sku.Price > maxPrice {
				maxPrice = sku.Price
			}
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
			}
			sku.ProfitRate = utils.ExecProfitRate(sku.OriginPrice, sku.Price)
			if k == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
		}
		product.MaxPrice = maxPrice
		product.MinPrice = minPrice

		if len(product.Skus) > 0 {
			product.ProfitRate = minProfitRate
		}
		product.IsBill = 0
		if product.BillPosition == 1 {
			if product.TaxCode != "" {
				product.IsBill = 1
			}
		} else if product.BillPosition == 2 {
			if len(product.Skus) > 0 {
				if product.Skus[0].TaxCode != "" {
					product.IsBill = 1
				}
			}
		}
		// 更新产品并添加或删除修改的关联sku与规格、规格项
		err = source.DB().Omit("spec_id", "created_at", "deleted_at", "supplier_id", "gather_supply_id", "source", "source_goods_id", "source_goods_id_string", "is_plugin", "plugin_id", "jushuitan_distributor_co_id", "shop_name", "supply_supplier_id", "jushuitan_distributor_supplier_name").Session(&gorm.Session{FullSaveAssociations: true}).Save(&product).Error

		if err != nil {
			return
		}
		err = mq.PublishMessage(product.ID, mq.Edit, 0)
		if err != nil {
			return
		}
		return err
	})
}

// UpdateProductForce
// @author: [piexlmax](https://github.com/piexlmax)
// @function: UpdateProduct
// @description: 不保留任何字段，强制更新
// @param: product *model.Product
// @return: err error
func UpdateProductForce(product ProductForUpdate) (err error) {
	//不保留任何字段，强制更新
	// 接口与回调都会调用此方法，回调调用时，如果是多规格商品，清空会员价
	if product.SingleOption != 1 {
		product.UserPriceSwitch = 0
		product.UserPrice = model.UserPrice{}
	}
	if product.SourceGoodsID == 23428 {
		log.Log().Info("更新商品测试2", zap.Any("info2", product))
	}
	var fullProduct model.Product
	err = source.DB().Model(&ProductForUpdate{}).Where("id = ?", product.ID).Find(&fullProduct).Error
	if err != nil {
		return
	}
	if len(product.DetailImages) > model.PRODUCT_DETAILIMAGES_COUNT_LIMIT || len(product.Gallery) > 10000 {
		err = errors.New("详情长度超出限制")
		return
	}
	if product.SupplierID != 0 {
		if product.IsDisplay != fullProduct.IsDisplay {
			var productVerify ProductVerify
			err = source.DB().Where("product_id = ?", product.ID).Where("supplier_id = ?", product.SupplierID).Order("created_at desc").First(&productVerify).Error
			if err == nil && (productVerify.Status == 0 || productVerify.IsDisplay == 0) {
				err = errors.New("商品还未审核或审核被驳回，请审核通过以后再进行上下架操作")
				return
			}
		}
	}

	return source.DB().Transaction(func(tx *gorm.DB) (err error) {
		// 此次提交的sku
		newSkuIDs := hashset.New()
		for k, sku := range product.Skus {
			// sku的supplierID
			product.Skus[k].SupplierID = fullProduct.SupplierID
			if sku.ID == 0 {
				continue
			}
			newSkuIDs.Add(sku.ID)

		}
		// 需要删除的sku
		var delSkus, oldSkus []Sku
		err = source.DB().Model(&product).Association("Skus").Find(&oldSkus)
		for _, oldSku := range oldSkus {
			if !newSkuIDs.Contains(oldSku.ID) {
				delSkus = append(delSkus, oldSku)
			}
		}
		if err != nil {
			return
		}
		//log.Log().Info("需要删除的sku", zap.Any("sku", delSkus))
		// 删除此次未提交的sku
		err = source.DB().Model(&product).Association("Skus").Delete(delSkus)
		if err != nil {
			return
		}

		var maxPrice uint
		var minPrice uint
		product.Stock = 0
		var minProfitRate float64
		for k, sku := range product.Skus {
			if sku.Price == 0 || sku.CostPrice == 0 {
				//价格为0,库存为0
				product.Skus[k].Stock = 0
			}
			product.Skus[k].SupplierID = product.SupplierID
			product.Stock += uint(sku.Stock)
			if sku.Price > maxPrice {
				maxPrice = sku.Price
			}
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
			}
			sku.ProfitRate = utils.ExecProfitRate(sku.OriginPrice, sku.Price)
			if k == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
		}
		product.MaxPrice = maxPrice
		product.MinPrice = minPrice

		if len(product.Skus) > 0 {
			product.ProfitRate = minProfitRate
		}
		product.IsBill = 0
		if product.BillPosition == 1 {
			if product.TaxCode != "" {
				product.IsBill = 1
			}
		} else if product.BillPosition == 2 {
			if len(product.Skus) > 0 {
				if product.Skus[0].TaxCode != "" {
					product.IsBill = 1
				}
			}
		}
		// 更新产品并添加或删除修改的关联sku与规格、规格项
		err = source.DB().Session(&gorm.Session{FullSaveAssociations: true}).Save(&product).Error

		if err != nil {
			return
		}
		err = mq.PublishMessage(product.ID, mq.Edit, 0)
		if err != nil {
			return
		}
		return err
	})
}

func ChangeCategoryByIds(data request.ChangeCategory) (err error) {
	//if data.Category1ID == 0 || data.Category2ID == 0 || data.Category3ID == 0 {
	//	err = errors.New("分类id 不能为0")
	//	return
	//}
	var product = model.Product{
		ProductModel: model.ProductModel{
			IsPromotion: data.IsPromotion,
			IsHot:       data.IsHot,
			IsNew:       data.IsNew,
		},
	}
	if data.Category1ID != nil {
		product.Category1ID = *data.Category1ID
	}
	if data.Category2ID != nil {
		product.Category2ID = *data.Category2ID
	}
	if data.Category3ID != nil {
		product.Category3ID = *data.Category3ID
	}
	if data.BrandID != nil {
		product.BrandID = *data.BrandID
	}
	err = source.DB().Model(&model.Product{}).Where("id in ?", data.Ids).Updates(&product).Error
	if err != nil {
		return
	}
	for _, id := range data.Ids {
		err = mq.PublishMessage(id, mq.Edit, 0)
		if err != nil {
			return
		}
	}
	return
}

//func UpdateCloudProduct(product ProductForUpdate) (err error) {
//
//	return source.DB().Transaction(func(tx *gorm.DB) (err error) {
//
//		// 删除之前的sku
//		err = source.DB().Where("product_id = ?", product.ID).Delete(&model.Sku{}).Error
//		if err != nil {
//			return
//		}
//
//		var maxPrice uint
//		var minPrice uint
//		for k, sku := range product.Skus {
//			product.Skus[k].SupplierID = product.SupplierID
//			if sku.Price > maxPrice {
//				maxPrice = sku.Price
//			}
//			if minPrice == 0 || sku.Price <= minPrice {
//				minPrice = sku.Price
//			}
//		}
//		product.MaxPrice = maxPrice
//		product.MinPrice = minPrice
//
//		// 更新产品并添加或删除修改的关联sku与规格、规格项
//		err = source.DB().Session(&gorm.Session{FullSaveAssociations: true}).Updates(product).Error
//		if err != nil {
//			return
//		}
//
//		err = mq.PublishMessage(product.ID, mq.Edit)
//		if err != nil {
//			return
//		}
//		return err
//	})
//}

func UpdateCloudSku(sku SkuForUpdate) (err error) {

	return source.DB().Transaction(func(tx *gorm.DB) (err error) {
		// 更新sku信息
		err = source.DB().Where("id = ?", sku.ID).Save(&sku).Error
		if err != nil {
			return
		}
		var product Product
		var fullSku Sku
		// 获取Sku信息
		err = source.DB().Where("id = ?", sku.ID).Find(&fullSku).Error
		if err != nil {
			return
		}
		// 获取商品信息
		err = source.DB().Where("id = ?", fullSku.ProductID).Find(&product).Error
		if err != nil {
			return
		}
		// 更新产品最高最低价格
		var maxPrice uint
		var minPrice uint
		var productChange bool
		if sku.Price > product.MaxPrice {
			maxPrice = sku.Price
			productChange = true
		} else {
			maxPrice = product.MaxPrice
		}
		if sku.Price < product.MinPrice {
			productChange = true
			minPrice = sku.Price
		} else {
			minPrice = product.MinPrice
		}
		if productChange {
			err = source.DB().Model(Product{}).Where("id = ?", fullSku.ProductID).Updates(map[string]interface{}{"max_price": maxPrice, "min_price": minPrice}).Error
			if err != nil {
				return
			}
		}

		err = mq.PublishMessage(product.ID, mq.Edit, 0)
		if err != nil {
			return
		}
		return err
	})
}

// GetProduct
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProduct
// @description: 根据id获取Product完整信息
// @param: id uint
// @return: err error, product model.Product
func GetProduct(id uint) (err error, product model.Product) {
	err = source.DB().Preload("Skus").Where("id = ?", id).First(&product).Error
	return
}
func GetSku(id uint) (err error, sku model.Sku) {
	err = source.DB().Where("id = ?", id).First(&sku).Error
	return
}
func SearchProductIDFromEs(info request.ProductSearch) (isSearch int, ids []uint, err error) {
	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
		isSearch++
	}
	if info.AppID != 0 {
		boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		isSearch++
	}

	filterQ := elastic.NewBoolQuery()
	if info.Barcode != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("barcode", info.Barcode).Slop(2))
		isSearch++
	}
	boolQ.Filter(filterQ)
	if isSearch > 0 {
		var res *elastic.SearchResult
		res, err = es.Search("product" + common_data.GetOldProductIndex()).Size(10000).Query(boolQ).Do(context.Background())
		if err != nil {
			return
		}
		var productSearchs []ProductElasticSearch
		//获取es搜索结果
		productSearchs, err = GetSearchResult(res)
		var idsString string
		for _, v := range productSearchs {
			idsString += "," + strconv.Itoa(int(v.ID))
			ids = append(ids, v.ID)
		}
	}
	return isSearch, ids, err
}
func setProductEsSeachInfo(info request.ProductSearch) (err error, boolQ *elastic.BoolQuery, isSearch int) {
	isSearch = 0
	boolQ = elastic.NewBoolQuery()

	if info.Title != "" {
		// 如果包含英文逗号，则按逗号分割后，以 OR 的形式分别搜索每个关键字
		title := info.Title
		if strings.Contains(title, ",") {
			parts := strings.Split(title, ",")
			for _, part := range parts {
				kw := strings.TrimSpace(part)
				if kw == "" {
					continue
				}
				// 单个关键字：search_title 短语匹配 或 title.keyword 通配符匹配
				subQ := elastic.NewBoolQuery().Should(
					elastic.NewMatchPhraseQuery("search_title", kw).Slop(1),
					elastic.NewWildcardQuery("title.keyword", "*"+kw+"*"),
				).MinimumShouldMatch("1")
				boolQ.Should(subQ)
			}
			// 整体至少命中一个关键字
			boolQ.MinimumShouldMatch("1")
		} else {
			// 单一关键字：search_title 短语匹配 或 title.keyword 通配符匹配
			boolQ.Should(
				elastic.NewMatchPhraseQuery("search_title", title).Slop(1),
				elastic.NewWildcardQuery("title.keyword", "*"+title+"*"),
			).MinimumShouldMatch("1")
		}
		isSearch++
	}
	if info.SourceName != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("search_title", info.Title))
		//boolQ.Must(elastic.NewMatchPhraseQuery("title", info.Title).Slop(0))
		//boolQ.Must(elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*"))
		boolQ.Must(elastic.NewMatchPhraseQuery("source_name", info.SourceName).Slop(2))

		isSearch++
	}
	filterQ := elastic.NewBoolQuery()
	if info.Filter == 3 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		filterQ.Must(elastic.NewRangeQuery("stock").Lte(0))
		//db = db.Where("is_display = 1 and stock <= 0")

		isSearch++
	}
	if info.Barcode != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("barcode", info.Barcode).Slop(2))
		isSearch++
	}
	if info.AppID != 0 {
		boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		isSearch++
	}
	boolQ.Filter(filterQ)
	return
}

// GetProductInfoList
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProductInfoList
// @description: 分页获取Product完整记录列表
// @param: info request.ProductSearch
// @return: err error, list []model.Product, total int64
func GetProductInfoList(info request.ProductSearch) (err error, list []response.ProductInfoList, total int64) {

	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	err, boolQ, isSearch := setProductEsSeachInfo(info)
	if err != nil {
		return
	}
	db := source.DB().Model(&response.ProductInfoList{}).Preload("Supplier").Preload("Skus").Preload("Brand").Preload("GatherSupply").Omit("detail_images")
	db, _ = setProductDbSearchInfo(info, db)

	//boolQ.Must(elastic.NewMatchQuery("is_plugin", 0))

	//if info.Page > 100 {
	//	info.Page = 100
	//}
	if isSearch > 0 {
		fmt.Println("当前productIndex为：" + common_data.GetOldProductIndex())
		var res *elastic.SearchResult
		res, err = es.Search("product" + common_data.GetOldProductIndex()).FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id")).Size(10000).Query(boolQ).Do(context.Background())
		if err != nil {
			return
		}
		var ids []uint
		var productSearchs []ProductElasticSearch
		//获取es搜索结果
		productSearchs, err = GetSearchResult(res)
		var idsString string
		for _, v := range productSearchs {
			idsString += "," + strconv.Itoa(int(v.ID))
			ids = append(ids, v.ID)
		}
		if len(ids) > 0 {
			//按照es查询出来的id顺序进行排序
			db.Where("id in ?", ids)
		} else {
			db.Where("id in (0)")

		}
	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 指定供应链
	//if info.GatherSupplyID != nil {
	//	db.Where("gather_supply_id = ?", info.GatherSupplyID)
	//} else {
	//	var specialSupplyIDs []uint
	//	err, specialSupplyIDs = getSupplyIDs()
	//	if len(specialSupplyIDs) > 0 {
	//		db.Where("gather_supply_id not in ?", specialSupplyIDs)
	//	}
	//}
	err = db.Count(&total).Error
	var sortType string
	switch info.SortType {
	case "0":
		sortType = "id"
		break
	case "1":
		sortType = "price"
		break
	case "2":
		sortType = "cost_price"
		break
	case "3":
		sortType = "origin_price"
		break
	case "4":
		sortType = "profit_rate"
		break
	case "5":
		sortType = "sales"
		break
	case "6":
		sortType = "sort desc, id"
		break
	default:
		sortType = "id"
		break
	}
	if info.SortMode == "" {
		info.SortMode = "desc"
	}
	var products []response.ProductInfoList
	err = db.Order(sortType + " " + info.SortMode).Limit(limit).Offset(offset).Find(&products).Error

	for k, product := range products {
		var maxPrice uint
		var minPrice uint
		var maxGuidePrice uint
		var minGuidePrice uint
		var maxCostPrice uint
		var minCostPrice uint
		var maxOriginPrice uint
		var minOriginPrice uint
		var minProfitRate float64 = 0
		var maxProfitRate float64 = 0
		var minGuideProfitRate float64 = 0
		var maxGuideProfitRate float64 = 0
		for pk, psku := range product.Skus {
			if psku.OriginPrice > 0 && psku.OriginPrice > psku.Price && psku.Price > 0 {
				product.Skus[pk].ProfitRate = utils.ExecProfitRate(psku.OriginPrice, psku.Price)
			} else {
				product.Skus[pk].ProfitRate = 0
			}
			if psku.GuidePrice > 0 && psku.GuidePrice > psku.Price && psku.Price > 0 {
				product.Skus[pk].GuideProfitRate = utils.ExecProfitRate(psku.GuidePrice, psku.Price)
			} else {
				product.Skus[pk].GuideProfitRate = 0
			}

		}
		var productStock int
		for sk, sku := range product.Skus {
			productStock += sku.Stock
			if sku.Price > maxPrice {
				maxPrice = sku.Price
			}
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
			}

			if sku.ExecPrice > maxCostPrice {
				maxCostPrice = sku.ExecPrice
			}
			if minCostPrice == 0 || sku.ExecPrice <= minCostPrice {
				minCostPrice = sku.ExecPrice
			}

			if sku.OriginPrice > maxOriginPrice {
				maxOriginPrice = sku.OriginPrice
			}
			if minOriginPrice == 0 || sku.OriginPrice <= minOriginPrice {
				minOriginPrice = sku.OriginPrice
			}

			if sku.GuidePrice > maxGuidePrice {
				maxGuidePrice = sku.GuidePrice
			}
			if minGuidePrice == 0 || sku.GuidePrice <= minGuidePrice {
				minGuidePrice = sku.GuidePrice
			}

			if sku.ProfitRate > maxProfitRate {
				maxProfitRate = sku.ProfitRate
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}

			if sku.GuideProfitRate > maxGuideProfitRate {
				maxGuideProfitRate = sku.GuideProfitRate
			}
			if sk == 0 {
				minGuideProfitRate = sku.GuideProfitRate
			}
			if sku.GuideProfitRate <= minGuideProfitRate {
				minGuideProfitRate = sku.GuideProfitRate
			}
		}

		products[k].MinPrice = minPrice
		products[k].MaxPrice = maxPrice
		products[k].MinCostPrice = minCostPrice
		products[k].MaxCostPrice = maxCostPrice
		products[k].MinOriginPrice = minOriginPrice
		products[k].MaxOriginPrice = maxOriginPrice
		products[k].MinProfitRate = minProfitRate
		products[k].MaxProfitRate = maxProfitRate
		products[k].MinGuideProfitRate = minGuideProfitRate
		products[k].MaxGuideProfitRate = maxGuideProfitRate
		products[k].Stock = uint(productStock)

	}
	//err = db.Count(&total).Error

	return err, products, total
}
func GetGuangdianProductInfoList(info request.ProductSearch) (err error, list []response.ProductInfoList, total int64) {

	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	err, boolQ, isSearch := setProductEsSeachInfo(info)
	if err != nil {
		return
	}
	db := source.DB().Model(&response.ProductInfoList{}).Preload("Supplier").Preload("Skus").Preload("Brand").Preload("GatherSupply").Omit("detail_images")
	db, _ = setGuangdianProductDbSearchInfo(info, db)

	//boolQ.Must(elastic.NewMatchQuery("is_plugin", 0))

	//if info.Page > 100 {
	//	info.Page = 100
	//}
	if isSearch > 0 {
		fmt.Println("当前productIndex为：" + common_data.GetOldProductIndex())
		var res *elastic.SearchResult
		res, err = es.Search("product" + common_data.GetOldProductIndex()).FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id")).Size(10000).Query(boolQ).Do(context.Background())
		if err != nil {
			return
		}
		var ids []uint
		var productSearchs []ProductElasticSearch
		//获取es搜索结果
		productSearchs, err = GetSearchResult(res)
		var idsString string
		for _, v := range productSearchs {
			idsString += "," + strconv.Itoa(int(v.ID))
			ids = append(ids, v.ID)
		}
		if len(ids) > 0 {
			//按照es查询出来的id顺序进行排序
			db.Where("id in ?", ids)
		} else {
			db.Where("id in (0)")

		}
	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 指定供应链
	//if info.GatherSupplyID != nil {
	//	db.Where("gather_supply_id = ?", info.GatherSupplyID)
	//} else {
	//	var specialSupplyIDs []uint
	//	err, specialSupplyIDs = getSupplyIDs()
	//	if len(specialSupplyIDs) > 0 {
	//		db.Where("gather_supply_id not in ?", specialSupplyIDs)
	//	}
	//}
	err = db.Count(&total).Error
	var sortType string
	switch info.SortType {
	case "0":
		sortType = "id"
		break
	case "1":
		sortType = "price"
		break
	case "2":
		sortType = "cost_price"
		break
	case "3":
		sortType = "origin_price"
		break
	case "4":
		sortType = "profit_rate"
		break
	case "5":
		sortType = "sales"
		break
	case "6":
		sortType = "sort desc, id"
		break
	default:
		sortType = "id"
		break
	}
	if info.SortMode == "" {
		info.SortMode = "desc"
	}
	var products []response.ProductInfoList
	err = db.Order(sortType + " " + info.SortMode).Limit(limit).Offset(offset).Find(&products).Error

	for k, product := range products {
		var maxPrice uint
		var minPrice uint
		var maxGuidePrice uint
		var minGuidePrice uint
		var maxCostPrice uint
		var minCostPrice uint
		var maxOriginPrice uint
		var minOriginPrice uint
		var minProfitRate float64 = 0
		var maxProfitRate float64 = 0
		for pk, psku := range product.Skus {
			if psku.OriginPrice > 0 && psku.OriginPrice > psku.Price && psku.Price > 0 {
				product.Skus[pk].ProfitRate = utils.ExecProfitRate(psku.OriginPrice, psku.Price)
			} else {
				product.Skus[pk].ProfitRate = 0
			}

		}
		var productStock int
		for sk, sku := range product.Skus {
			productStock += sku.Stock
			if sku.Price > maxPrice {
				maxPrice = sku.Price
			}
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
			}

			if sku.ExecPrice > maxCostPrice {
				maxCostPrice = sku.ExecPrice
			}
			if minCostPrice == 0 || sku.ExecPrice <= minCostPrice {
				minCostPrice = sku.ExecPrice
			}

			if sku.OriginPrice > maxOriginPrice {
				maxOriginPrice = sku.OriginPrice
			}
			if minOriginPrice == 0 || sku.OriginPrice <= minOriginPrice {
				minOriginPrice = sku.OriginPrice
			}

			if sku.GuidePrice > maxGuidePrice {
				maxGuidePrice = sku.GuidePrice
			}
			if minGuidePrice == 0 || sku.GuidePrice <= minGuidePrice {
				minGuidePrice = sku.GuidePrice
			}

			if sku.ProfitRate > maxProfitRate {
				maxProfitRate = sku.ProfitRate
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
		}

		products[k].MinPrice = minPrice
		products[k].MaxPrice = maxPrice
		products[k].MinCostPrice = minCostPrice
		products[k].MaxCostPrice = maxCostPrice
		products[k].MinOriginPrice = minOriginPrice
		products[k].MaxOriginPrice = maxOriginPrice
		products[k].MinProfitRate = minProfitRate
		products[k].MaxProfitRate = maxProfitRate
		products[k].Stock = uint(productStock)

	}
	//err = db.Count(&total).Error

	return err, products, total
}
func decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.1f", value), 64)
	if err != nil {
		log.Log().Error("数据转换失败", zap.Any("err", err))
		return 0
	}
	return value
}
func GetAliProductInfoList(info request.ProductSearch) (err error, list []response.ProductInfoList, total int64) {

	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	db := source.DB().Preload("Supplier").Preload("Brand").Preload("GatherSupply").Preload("AliProduct")
	db = db.Where("deleted_at is NULL")
	var products []response.ProductInfoList

	isSearch := 0
	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
		isSearch++
	}

	filterQ := elastic.NewBoolQuery()
	if info.SupplierID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		if *info.SupplierID == 999999 {
			filterQ.Must(elastic.NewRangeQuery("supplier_id").Gt(0))
		} else {
			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
		}
		isSearch++
	}
	if info.GatherSupplyID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
		isSearch++
	}
	if info.BrandID != 0 {
		filterQ.Must(elastic.NewMatchQuery("brand_id", info.BrandID))
		isSearch++

	}
	if info.ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("id", info.ID))
		isSearch++

	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		isSearch++
	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
		isSearch++
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
		isSearch++
	}

	if info.IsRecommend != nil {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", &info.IsRecommend))
		isSearch++
	}
	if info.IsNew != nil {
		filterQ.Must(elastic.NewMatchQuery("is_new", &info.IsNew))
		isSearch++
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewMatchQuery("is_hot", &info.IsHot))
		isSearch++
	}
	if info.IsPromotion != nil {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", &info.IsPromotion))
		isSearch++
	}
	if info.StatusLock != nil {
		filterQ.Must(elastic.NewMatchQuery("status_lock", &info.StatusLock))
		isSearch++
	}
	if info.Filter == 1 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		isSearch++
	}
	if info.Filter == 2 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 0))
		isSearch++
	}
	if info.Filter == 3 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		filterQ.Must(elastic.NewRangeQuery("stock").Lte(0))
		isSearch++
	}
	if info.MinPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice * 100))
		isSearch++
	}
	if info.MaxPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice * 100))
		isSearch++
	}
	if info.Barcode != "" {
		boolQ.Must(elastic.NewMatchPhraseQuery("barcode", info.Barcode).Slop(2))
		isSearch++
	}

	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}

	// 指定供应链
	if info.GatherSupplyID != nil {
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
	} else {
		if supply.ID != 0 {
			filterQ.MustNot(elastic.NewMatchQuery("gather_supplier_id", supply.ID))
		}
	}

	boolQ.Filter(filterQ)

	if isSearch > 0 {
		//if info.Page > 100 {
		//	info.Page = 100
		//}
		limit := info.PageSize
		offset := info.PageSize * (info.Page - 1)
		//es执行搜索
		total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
		if err != nil {
			return
		}
		//if total > int64(info.PageSize*100) {
		//	total = int64(info.PageSize * 100)
		//}
		var res *elastic.SearchResult
		res, err = es.Search("product" + common_data.GetOldProductIndex()).Size(limit).From(offset).Query(boolQ).Do(context.Background())
		if err != nil {
			return
		}
		var ids []uint
		var productSearchs []ProductElasticSearch
		//获取es搜索结果
		productSearchs, err = GetSearchResult(res)
		for _, v := range productSearchs {
			ids = append(ids, v.ID)
		}
		db = db.Where("id in ?", ids).Where("is_plugin = 0")
		err = db.Order("created_at desc").Find(&products).Error
	} else {
		limit := info.PageSize
		offset := info.PageSize * (info.Page - 1)

		// 指定供应链
		if info.GatherSupplyID != nil {
			db.Where("gather_supply_id = ?", info.GatherSupplyID)
		} else {
			if supply.ID != 0 {
				db.Where("gather_supply_id != ?", supply.ID)
			}
		}
		db.Where("is_plugin = 0")
		err = db.Count(&total).Error
		err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&products).Error
	}

	//err = db.Count(&total).Error

	return err, products, total
}
func setProductDbSearchInfo(info request.ProductSearch, db *gorm.DB) (dB *gorm.DB, isSearch int) {
	db = db.Where("deleted_at is NULL")
	db = db.Where("not exists(SELECT 1  FROM (select id from products where plugin_id=18)  AS temp_table WHERE temp_table.id = products.id)") //排除中台自营商品，仅后端不显示，前端正常显示
	db = db.Where("is_plugin = 0")
	if info.ShopName != "" {
		db = db.Where("(shop_name = ? or jushuitan_distributor_supplier_name like ?)", strings.TrimSpace(info.ShopName), "%"+strings.TrimSpace(info.ShopName)+"%")
	}

	if info.DetailImages != nil {
		if *info.DetailImages == 1 {
			db.Where("detail_images='' or detail_images is null")
		}

	}
	if info.ImageUrl != nil {
		if *info.ImageUrl == 1 {
			db.Where("image_url='' or image_url is null")

		}

	}

	if info.GalleryImages != nil {
		if *info.GalleryImages == 1 {
			db.Where("gallery='' or gallery is null")

		}

	}

	if info.ThousandId != 0 {
		// 使用 NOT EXISTS 查询，避免先查询所有ID
		db = db.Where("NOT EXISTS (SELECT 1 FROM thousands_prices_products WHERE thousands_prices_id = ? AND product_id = products.id)", info.ThousandId)
	}
	if info.IsVideoShop != 0 {
		if info.IsVideoShop == -1 {
			info.IsVideoShop = 0
		}
		db = db.Where("is_video_shop = ?", info.IsVideoShop)
		isSearch++
	}
	if info.SupplierID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		if *info.SupplierID == 999999 {
			//filterQ.Must(elastic.NewRangeQuery("supplier_id").Gt(0))
			db = db.Where("supplier_id > 0")
		} else {
			supplierID := *info.SupplierID
			if supplierID == 0 {
				db = db.Where("supplier_id = ?", supplierID)
				db = db.Where("gather_supply_id = ?", supplierID)

			} else {
				db = db.Where("supplier_id = ?", supplierID)
			}
			//filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
		}
		isSearch++
	}

	if info.GatherSupplyID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		//filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
		gatherSupplyID := *info.GatherSupplyID
		if gatherSupplyID == 0 {
			db = db.Where("supplier_id = ?", gatherSupplyID)
			db = db.Where("gather_supply_id = ?", gatherSupplyID)

		} else {
			db = db.Where("gather_supply_id = ?", gatherSupplyID)
		}
		isSearch++
	}
	if info.BrandID != 0 {
		//filterQ.Must(elastic.NewMatchQuery("brand_id", info.BrandID))
		db = db.Where("brand_id = ?", info.BrandID)

		isSearch++

	}
	if info.ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		//filterQ.Must(elastic.NewMatchQuery("id", info.ID))
		db = db.Where("id = ?", info.ID)
		isSearch++

	}
	if info.JushuitanBind != nil {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		//filterQ.Must(elastic.NewMatchQuery("id", info.ID))
		db = db.Where("jushuitan_bind = ?", info.JushuitanBind)
		isSearch++

	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		//filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		db = db.Where("category1_id = ?", info.Category1ID)

		isSearch++
	}
	if info.Category2ID != 0 {
		//filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
		db = db.Where("category2_id = ?", info.Category2ID)

		isSearch++
	}
	if info.Category3ID != 0 {
		//filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
		db = db.Where("category3_id = ?", info.Category3ID)

		isSearch++
	}

	if info.IsRecommend != nil {
		//filterQ.Must(elastic.NewMatchQuery("is_recommend", &info.IsRecommend))
		db = db.Where("is_recommend = ?", &info.IsRecommend)

		isSearch++
	}
	if info.IsNew != nil {
		//filterQ.Must(elastic.NewMatchQuery("is_new", &info.IsNew))
		db = db.Where("is_new = ?", &info.IsNew)

		isSearch++
	}
	if info.IsHot != nil {
		//filterQ.Must(elastic.NewMatchQuery("is_hot", &info.IsHot))
		db = db.Where("is_hot = ?", &info.IsHot)

		isSearch++
	}
	if info.IsPromotion != nil {
		//filterQ.Must(elastic.NewMatchQuery("is_promotion", &info.IsPromotion))
		db = db.Where("is_promotion = ?", &info.IsPromotion)

		isSearch++
	}
	if info.StatusLock != nil {
		//filterQ.Must(elastic.NewMatchQuery("status_lock", &info.StatusLock))
		db = db.Where("status_lock = ?", &info.StatusLock)

		isSearch++
	}
	if info.Filter == 1 {
		//filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		db = db.Where("is_display = 1")

		isSearch++
	}
	if info.Filter == 2 {
		//filterQ.Must(elastic.NewMatchQuery("is_display", 0))
		db = db.Where("is_display = 0")

		isSearch++
	}

	if info.MinPrice != 0 {
		//filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice * 100))
		db = db.Where("price >= ?", info.MinPrice*100)

		isSearch++
	}
	if info.MaxPrice != 0 {
		//filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice * 100))
		db = db.Where("price <= ?", info.MaxPrice*100)

		isSearch++
	}
	if info.SortType == "4" || info.SortType == "1" {
		if info.SectionStart > 0 {
			if info.SortType == "4" {
				db = db.Where("profit_rate >= ?", info.SectionStart)
			} else {
				db = db.Where("price >= ?", info.SectionStart)
			}
		}
		if info.SectionEnd > 0 {
			if info.SortType == "4" {
				db = db.Where("profit_rate <= ?", info.SectionEnd)
			} else {
				db = db.Where("price <= ?", info.SectionEnd)
			}
		}
	}

	if info.IsBill != nil {
		db = db.Where("is_bill = ?", info.IsBill)
		isSearch++

	}
	if info.Sn != "" {
		db = db.Where("sn = ?", info.Sn)
		isSearch++

	}
	// 指定供应链
	if info.GatherSupplyID != nil {
		//filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
		db = db.Where("gather_supply_id = ?", info.GatherSupplyID)
		isSearch++

	}
	// 赋码状态搜索
	if info.Coding != nil {
		db.Where("is_bill = ?", info.Coding)
	}
	// 会员价独立规则开关搜索
	if info.MemberPrice != nil {
		db.Where("user_price_switch = ?", info.MemberPrice)
	}
	return db, isSearch
}

func setGuangdianProductDbSearchInfo(info request.ProductSearch, db *gorm.DB) (dB *gorm.DB, isSearch int) {
	db = db.Where("deleted_at is NULL")
	db = db.Where("source = 132")

	if info.ThousandId != 0 {
		// 使用 NOT EXISTS 查询，避免先查询所有ID
		db = db.Where("NOT EXISTS (SELECT 1 FROM thousands_prices_products WHERE thousands_prices_id = ? AND product_id = products.id)", info.ThousandId)
	}
	if info.IsVideoShop != 0 {
		if info.IsVideoShop == -1 {
			info.IsVideoShop = 0
		}
		db = db.Where("is_video_shop = ?", info.IsVideoShop)
		isSearch++
	}
	if info.SupplierID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		if *info.SupplierID == 999999 {
			//filterQ.Must(elastic.NewRangeQuery("supplier_id").Gt(0))
			db = db.Where("supplier_id > 0")
		} else {
			supplierID := *info.SupplierID
			if supplierID == 0 {
				db = db.Where("supplier_id = ?", supplierID)
				db = db.Where("gather_supply_id = ?", supplierID)

			} else {
				db = db.Where("supplier_id = ?", supplierID)
			}
			//filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
		}
		isSearch++
	}

	if info.BrandID != 0 {
		//filterQ.Must(elastic.NewMatchQuery("brand_id", info.BrandID))
		db = db.Where("brand_id = ?", info.BrandID)

		isSearch++

	}
	if info.ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		//filterQ.Must(elastic.NewMatchQuery("id", info.ID))
		db = db.Where("id = ?", info.ID)
		isSearch++

	}
	if info.JushuitanBind != nil {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		//filterQ.Must(elastic.NewMatchQuery("id", info.ID))
		db = db.Where("jushuitan_bind = ?", info.JushuitanBind)
		isSearch++

	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		//filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		db = db.Where("category1_id = ?", info.Category1ID)

		isSearch++
	}
	if info.Category2ID != 0 {
		//filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
		db = db.Where("category2_id = ?", info.Category2ID)

		isSearch++
	}
	if info.Category3ID != 0 {
		//filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
		db = db.Where("category3_id = ?", info.Category3ID)

		isSearch++
	}

	if info.IsRecommend != nil {
		//filterQ.Must(elastic.NewMatchQuery("is_recommend", &info.IsRecommend))
		db = db.Where("is_recommend = ?", &info.IsRecommend)

		isSearch++
	}
	if info.IsNew != nil {
		//filterQ.Must(elastic.NewMatchQuery("is_new", &info.IsNew))
		db = db.Where("is_new = ?", &info.IsNew)

		isSearch++
	}
	if info.IsHot != nil {
		//filterQ.Must(elastic.NewMatchQuery("is_hot", &info.IsHot))
		db = db.Where("is_hot = ?", &info.IsHot)

		isSearch++
	}
	if info.IsPromotion != nil {
		//filterQ.Must(elastic.NewMatchQuery("is_promotion", &info.IsPromotion))
		db = db.Where("is_promotion = ?", &info.IsPromotion)

		isSearch++
	}
	if info.StatusLock != nil {
		//filterQ.Must(elastic.NewMatchQuery("status_lock", &info.StatusLock))
		db = db.Where("status_lock = ?", &info.StatusLock)

		isSearch++
	}
	if info.Filter == 1 {
		//filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		db = db.Where("is_display = 1")

		isSearch++
	}
	if info.Filter == 2 {
		//filterQ.Must(elastic.NewMatchQuery("is_display", 0))
		db = db.Where("is_display = 0")

		isSearch++
	}

	if info.MinPrice != 0 {
		//filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice * 100))
		db = db.Where("price >= ?", info.MinPrice*100)

		isSearch++
	}
	if info.MaxPrice != 0 {
		//filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice * 100))
		db = db.Where("price <= ?", info.MaxPrice*100)

		isSearch++
	}
	if info.SortType == "4" || info.SortType == "1" {
		if info.SectionStart > 0 {
			if info.SortType == "4" {
				db = db.Where("profit_rate >= ?", info.SectionStart)
			} else {
				db = db.Where("price >= ?", info.SectionStart)
			}
		}
		if info.SectionEnd > 0 {
			if info.SortType == "4" {
				db = db.Where("profit_rate <= ?", info.SectionEnd)
			} else {
				db = db.Where("price <= ?", info.SectionEnd)
			}
		}
	}

	if info.IsBill != nil {
		db = db.Where("is_bill = ?", info.IsBill)
		isSearch++

	}
	if info.Sn != "" {
		db = db.Where("sn = ?", info.Sn)
		isSearch++

	}

	return db, isSearch
}
func DeleteProductInfoList(info request.ProductSearch) (err error) {

	// 创建db
	es, err := source.ES()
	var boolQ *elastic.BoolQuery
	var esisSearch = 0
	err, boolQ, esisSearch = setProductEsSeachInfo(info)
	if err != nil {
		return
	}
	db := source.DB().Model(&response.ProductInfoList{})

	if esisSearch > 0 {
		var res *elastic.SearchResult
		res, err = es.Search("product" + common_data.GetOldProductIndex()).FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id")).Size(10000).Query(boolQ).Do(context.Background())
		if err != nil {
			return
		}
		var ids []uint
		var productSearchs []ProductElasticSearch
		//获取es搜索结果
		productSearchs, err = GetSearchResult(res)
		var idsString string
		for _, v := range productSearchs {
			idsString += "," + strconv.Itoa(int(v.ID))
			ids = append(ids, v.ID)
		}
		if len(ids) > 0 {
			//按照es查询出来的id顺序进行排序
			db.Where("id in ?", ids)
		} else {
			db.Where("id in (0)")

		}
	}
	db, isSearch := setProductDbSearchInfo(info, db)

	if isSearch+esisSearch > 0 {
		err = db.Delete(&model.Product{}).Error
	} else {
		err = errors.New("必须要指定筛选条件才可以全部删除")
	}

	//err = db.Count(&total).Error

	return err
}

func GetDeleteProductCount(info request.ProductSearch) (err error, total int64) {

	es, err := source.ES()
	var boolQ *elastic.BoolQuery
	var essSearch = 0
	err, boolQ, essSearch = setProductEsSeachInfo(info)
	if err != nil {
		return
	}
	db := source.DB().Model(&response.ProductInfoList{})

	if essSearch > 0 {
		var res *elastic.SearchResult
		res, err = es.Search("product" + common_data.GetOldProductIndex()).FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id")).Size(10000).Query(boolQ).Do(context.Background())
		if err != nil {
			return
		}
		var ids []uint
		var productSearchs []ProductElasticSearch
		//获取es搜索结果
		productSearchs, err = GetSearchResult(res)
		var idsString string
		for _, v := range productSearchs {
			idsString += "," + strconv.Itoa(int(v.ID))
			ids = append(ids, v.ID)
		}
		if len(ids) > 0 {
			//按照es查询出来的id顺序进行排序
			db.Where("id in ?", ids)
		} else {
			db.Where("id in (0)")

		}
	}
	db, isSearch := setProductDbSearchInfo(info, db)

	if isSearch+essSearch > 0 {
		err = db.Count(&total).Error
	} else {
		err = errors.New("必须要指定筛选条件才可以全部删除")
	}

	//err = db.Count(&total).Error

	return err, total
}

func UpdateProductInfoList(info request.ProductSearch) (err error) {

	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	db := source.DB()
	db = db.Where("deleted_at is NULL")

	isSearch := 0
	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
		isSearch++
	}

	filterQ := elastic.NewBoolQuery()
	if info.SupplierID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		if *info.SupplierID == 999999 {
			filterQ.Must(elastic.NewRangeQuery("supplier_id").Gt(0))
		} else {
			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
		}
		isSearch++
	}
	if info.GatherSupplyID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
		isSearch++
	}
	if info.BrandID != 0 {
		filterQ.Must(elastic.NewMatchQuery("brand_id", info.BrandID))
		isSearch++

	}
	if info.ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("id", info.ID))
		isSearch++

	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		isSearch++
	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
		isSearch++
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
		isSearch++
	}

	if info.IsRecommend != nil {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", &info.IsRecommend))
		isSearch++
	}
	if info.IsNew != nil {
		filterQ.Must(elastic.NewMatchQuery("is_new", &info.IsNew))
		isSearch++
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewMatchQuery("is_hot", &info.IsHot))
		isSearch++
	}
	if info.IsPromotion != nil {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", &info.IsPromotion))
		isSearch++
	}
	if info.StatusLock != nil {
		filterQ.Must(elastic.NewMatchQuery("status_lock", &info.StatusLock))
		isSearch++
	}
	if info.Filter == 1 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		isSearch++
	}
	if info.Filter == 2 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 0))
		isSearch++
	}
	if info.Filter == 3 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		filterQ.Must(elastic.NewRangeQuery("stock").Lte(0))
		isSearch++
	}
	if info.MinPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice * 100))
		isSearch++
	}
	if info.MaxPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice * 100))
		isSearch++
	}

	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}

	// 指定供应链
	if info.GatherSupplyID != nil {
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
	} else {
		if supply.ID != 0 {
			filterQ.MustNot(elastic.NewMatchQuery("gather_supplier_id", supply.ID))
		}
	}

	boolQ.Filter(filterQ)

	if isSearch > 0 {
		//if info.Page > 100 {
		//	info.Page = 100
		//}

		//if total > int64(info.PageSize*100) {
		//	total = int64(info.PageSize * 100)
		//}
		var res *elastic.SearchResult
		res, err = es.Search("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
		if err != nil {
			return
		}
		var ids []uint
		var productSearchs []ProductElasticSearch
		//获取es搜索结果
		productSearchs, err = GetSearchResult(res)
		for _, v := range productSearchs {
			ids = append(ids, v.ID)
		}
		db = db.Where("id in ?", ids)
		var products []model.Product
		err = db.Find(&products).Error
		if err != nil {
			return
		}
		var productUpdateMap []map[string]interface{}
		for _, v := range products {
			var productUdpateMapItem = make(map[string]interface{})
			productUdpateMapItem["id"] = v.ID
			productUdpateMapItem["min_price"] = v.Price
			productUdpateMapItem["max_price"] = v.Price
			productUpdateMap = append(productUpdateMap, productUdpateMapItem)
		}
		err = source.BatchUpdate(productUpdateMap, "products", "id")
	} else {
		err = errors.New("必须要指定筛选条件才可以全部删除")
	}

	//err = db.Count(&total).Error

	return err
}
func ExportProductInfoList(info request.ProductSearch) (err error) {
	go ExportProduct(info)
	return
}

type ExportProductModel struct {
	model.Product
	SupplierSource         SupplierSource         `json:"supplier_source"`
	SupplierSourceCategory SupplierSourceCategory `json:"supplier_source_category"`
	Category1              model.Category         `json:"category_1"`
	Category2              model.Category         `json:"category_2"`
	Category3              model.Category         `json:"category_3"`
}
type SupplierSource struct {
	ID   uint   `json:"id" form:"id"`
	Name string `json:"name" form:"name"`
}
type SupplierSourceCategory struct {
	ID   uint   `json:"id" form:"id"`
	Name string `json:"name" form:"name"`
}

func (receiver ExportProductModel) TableName() string {
	return "products"
}
func ExportProduct(info request.ProductSearch) (err error, link string) {
	isSearch, ids, err := SearchProductIDFromEs(info)
	// 创建db
	db := source.DB().Model(&ExportProductModel{})
	db = db.Where("deleted_at is NULL")
	if isSearch > 0 {
		if len(ids) > 0 {
			//按照es查询出来的id顺序进行排序
			db.Where("id in ?", ids)
		} else {
			db.Where("id in (0)")
		}
	}

	if info.SupplierID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		if *info.SupplierID == 999999 {
			db.Where("supplier_id > 0")
		} else {
			db.Where("supplier_id = ?", info.SupplierID)
		}
	}

	if info.BrandID != 0 {
		db = db.Where("brand_id = ?", info.BrandID)
	}
	if info.ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		db = db.Where("id = ?", info.ID)
	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		db = db.Where("category1_id = ?", info.Category1ID)
	}
	if info.Category2ID != 0 {
		db = db.Where("category2_id = ?", info.Category2ID)
	}
	if info.Category3ID != 0 {
		db = db.Where("category3_id = ?", info.Category3ID)
	}
	if info.ShopName != "" {
		db = db.Where("(shop_name = ? or jushuitan_distributor_supplier_name like ?)", strings.TrimSpace(info.ShopName), "%"+strings.TrimSpace(info.ShopName)+"%")
	}
	if info.IsRecommend != nil {
		db = db.Where("is_recommend = ?", info.IsRecommend)
	}
	if info.IsNew != nil {
		db = db.Where("is_new = ?", info.IsNew)
	}
	if info.IsHot != nil {
		db = db.Where("is_hot = ?", info.IsHot)
	}
	if info.IsPromotion != nil {
		db = db.Where("is_promotion = ?", info.IsPromotion)
	}
	if info.StatusLock != nil {
		db = db.Where("status_lock = ?", info.StatusLock)
	}
	if info.Filter == 1 {
		db = db.Where("is_display = 1")
	}
	if info.Filter == 2 {
		db = db.Where("is_display = 0")
	}
	if info.Filter == 3 {
		db = db.Where("is_display = 1 AND stock <= 0")
	}
	if info.MinPrice != 0 {
		db = db.Where("price >= ?", info.MinPrice*100)
	}
	if info.MaxPrice != 0 {
		db = db.Where("price <= ?", info.MaxPrice*100)
	}

	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}

	// 指定供应链
	if info.GatherSupplyID != nil {
		//filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
		db = db.Where("gather_supply_id = ?", info.GatherSupplyID)

	} else {
		var specialSupplyIDs []uint
		err, specialSupplyIDs = getSupplyIDs()
		if len(specialSupplyIDs) > 0 {
			supplyIDs := make([]interface{}, len(specialSupplyIDs))
			for index, value := range specialSupplyIDs {
				supplyIDs[index] = value
			}
			//filterQ.MustNot(elastic.NewTermsQuery("gather_supplier_id", supplyIDs...)
			db = db.Where("gather_supply_id not in ?", specialSupplyIDs)

		}
	}
	var total int64
	db.Where("is_plugin = 0")
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var productExportRecord model.ProductExportRecord
	productExportRecord.ProductCount = total
	productExportRecord.StatusString = "导出中"
	if info.SysUserID != nil {
		productExportRecord.SysUserID = *info.SysUserID
	}
	err = source.DB().Create(&productExportRecord).Error
	if err != nil {
		return
	}
	excelPage := total/5000 + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_product"
	for ei := 1; ei <= int(excelPage); ei++ {
		f := excelize.NewFile()
		// 创建一个工作表
		index := f.NewSheet("Sheet1")
		// 设置单元格的值
		//！！！！！！！！！！！！！不要随意修改目前的列，因为excel有其他业务在读取这个excel的列，修改现在的列结构可能导致其他功能失效
		f.SetCellValue("Sheet1", "A1", "商品id")
		f.SetCellValue("Sheet1", "B1", "商品名称")
		f.SetCellValue("Sheet1", "C1", "供应渠道")
		f.SetCellValue("Sheet1", "D1", "状态")
		f.SetCellValue("Sheet1", "E1", "商品单位")
		f.SetCellValue("Sheet1", "F1", "商品规格标题")
		f.SetCellValue("Sheet1", "G1", "库存")
		f.SetCellValue("Sheet1", "H1", "销量")
		f.SetCellValue("Sheet1", "I1", "指导价")
		f.SetCellValue("Sheet1", "J1", "供货价")
		f.SetCellValue("Sheet1", "K1", "成本价")
		f.SetCellValue("Sheet1", "L1", "建议零售价")
		f.SetCellValue("Sheet1", "M1", "营销价")
		//！！！！！！！！！！！！！不要随意修改目前的列，因为excel有其他业务在读取这个excel的列，修改现在的列结构可能导致其他功能失效
		f.SetCellValue("Sheet1", "N1", "商品编码")
		f.SetCellValue("Sheet1", "O1", "商品条码")
		f.SetCellValue("Sheet1", "P1", "重量")
		f.SetCellValue("Sheet1", "Q1", "税收分类编码")
		f.SetCellValue("Sheet1", "R1", "税率")
		f.SetCellValue("Sheet1", "S1", "库存")
		f.SetCellValue("Sheet1", "T1", "分类")
		f.SetCellValue("Sheet1", "U1", "品牌")
		f.SetCellValue("Sheet1", "V1", "发票名称")
		f.SetCellValue("Sheet1", "W1", "商品属性")
		f.SetCellValue("Sheet1", "X1", "供应商商品分类")
		f.SetCellValue("Sheet1", "Y1", "供应商商品来源")
		f.SetCellValue("Sheet1", "Z1", "第三方商品ID")
		f.SetCellValue("Sheet1", "AA1", "京东商品连接")
		f.SetCellValue("Sheet1", "AB1", "规格ID")
		f.SetCellValue("Sheet1", "AC1", "运费")
		//！！！！！！！！！！！！！不要随意修改目前的列，因为excel有其他业务在读取这个excel的列，修改现在的列结构可能导致其他功能失效
		i := 2
		var products []ExportProductModel
		err = db.Preload("Supplier").Preload("Skus").Preload("SupplierSource").Preload("SupplierSourceCategory").Preload("Brand").Preload("Category1").Preload("Category2").Preload("Category3").Preload("GatherSupply").Order("created_at desc").Limit(5000).Offset(5000 * (ei - 1)).Find(&products).Error

		for _, v := range products {
			for _, sku := range v.Skus {
				var statusName = "下架"
				if v.IsDisplay == 1 {
					statusName = "上架"
				}
				var sourceGoodsID interface{}
				if v.SourceGoodsID > 0 {
					sourceGoodsID = v.SourceGoodsID
				} else if v.SourceGoodsIDString != "" {
					sourceGoodsID = v.SourceGoodsIDString
				} else if v.Code != "" {
					sourceGoodsID = v.Code
				} else if v.Sn != "" {
					sourceGoodsID = v.Sn
				}
				f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
				f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.Title)
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.GatherSupply.Name+v.Supplier.Name)
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), statusName)
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Unit)
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), sku.Title)
				f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), sku.Stock)
				f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.Sales)
				f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), float64(sku.GuidePrice)/100)
				f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), float64(sku.Price)/100)
				f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), float64(sku.CostPrice)/100)
				f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), float64(sku.OriginPrice)/100)
				f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), float64(sku.ActivityPrice)/100)
				if sku.Sn == "" {
					f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), v.Sn)
				} else {
					f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), sku.Sn)
				}
				if sku.Barcode == "" {
					f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), v.Barcode)
				} else {
					f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), sku.Barcode)
				}
				f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), sku.Weight)
				if v.BillPosition == 1 {
					f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), v.TaxCode)
					f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), v.TaxRate)
				} else {
					f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), sku.TaxCode)
					f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), sku.TaxRate)
				}
				f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), sku.Stock)
				var categoryStr string
				categoryStr = fmt.Sprintf("%s-%s-%s", v.Category1.Name, v.Category2.Name, v.Category3.Name)
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), categoryStr)

				f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), v.Brand.Name)
				f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), v.TaxProductName)
				var attrsStr string
				for _, attr := range v.Attrs {
					attrsStr += fmt.Sprintf("%s:%s,", attr.Name, attr.Value)
				}
				JdlinK := ""

				if v.Source == 2 && v.StartDateTime != "" {
					JdlinK = "https://item.jd.com/" + v.StartDateTime + ".html"
				}
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), attrsStr)
				f.SetCellValue("Sheet1", "X"+strconv.Itoa(i), v.SupplierSourceCategory.Name)
				f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i), v.SupplierSource.Name)
				f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i), sourceGoodsID)
				f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i), JdlinK)
				f.SetCellValue("Sheet1", "AB"+strconv.Itoa(i), sku.ID)

				var freightTypeString string
				switch v.FreightType {
				case 0:
					freightTypeString = fmt.Sprintf("统一：%d元", v.Freight)
				case 1:
					var expressTemplate shippingModel.ExpressTemplate
					source.DB().Where("id = ?", v.FreightTemplateID).First(&expressTemplate)

					freightTypeString = fmt.Sprintf("模板：%s", expressTemplate.Name)
				case 2:
					freightTypeString = "第三方运费"
				case 3:
					freightTypeString = "包邮"
				}
				f.SetCellValue("Sheet1", "AC"+strconv.Itoa(i), freightTypeString)

				i++
			}

		}
		// 设置工作簿的默认工作表
		f.SetActiveSheet(index)
		// 根据指定路径保存文件
		//year, month, day := time.Now().Format("2006-01-02 15:04:05")

		exist, _ := utils.PathExists(path)

		if !exist {
			// 创建文件夹及其所有父目录
			err = os.MkdirAll(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "商品导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeString + "商品导出.zip"
		err = Zip(link, links)
	}
	productExportRecord.Link = link
	productExportRecord.StatusString = "导出完成"
	err = source.DB().Save(&productExportRecord).Error
	if err != nil {
		return
	}
	err = service.PublishNotify("商品导出完成", "共导出了"+strconv.Itoa(int(total))+"件商品")

	return err, link
}
func GetProductExportRecordList(info request.ProductExportRecordRequest) (err error, total int64, list interface{}) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ProductExportRecord{})
	var applications []model.ProductExportRecord
	// 总后台导出记录sys_user_id为0，供应商为supplier表的user_id
	if info.SysUserID != 0 {
		db = db.Where("`sys_user_id` = ?", info.SysUserID)
	}
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StatusString != "" {
		db = db.Where("`status_string` LIKE ?", "%"+info.StatusString+"%")
	}

	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&applications).Error

	return err, total, applications
}
func DeleteProductExportRecord(application model.ProductExportRecord) (err error) {
	err = source.DB().Delete(&application).Error
	return err
}
func Zip(dest string, paths []string) error {
	zfile, err := os.Create(dest)
	if err != nil {
		return err
	}
	defer zfile.Close()
	zipWriter := zip.NewWriter(zfile)
	defer zipWriter.Close()
	for _, src := range paths {
		// remove the trailing path sepeartor if it is a directory
		src := strings.TrimSuffix(src, string(os.PathSeparator))
		err = filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			// create local file header
			header, err := zip.FileInfoHeader(info)
			if err != nil {
				return err
			}
			// set compression method to deflate
			header.Method = zip.Deflate
			// set relative path of file in zip archive
			header.Name, err = filepath.Rel(filepath.Dir(src), path)
			if err != nil {
				return err
			}
			if info.IsDir() {
				header.Name += string(os.PathSeparator)
			}
			// create writer for writing header
			headerWriter, err := zipWriter.CreateHeader(header)
			if err != nil {
				return err
			}
			if info.IsDir() {
				return nil
			}
			f, err := os.Open(path)
			if err != nil {
				return err
			}
			defer f.Close()
			_, err = io.Copy(headerWriter, f)
			return err
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// 如果含有time.Time 请自行import time包
type Application struct {
	source.Model

	AppLevelID           uint             `json:"appLevelId" form:"appLevelId" gorm:"column:app_level_id;comment:;type:int;size:10;"`
	MemberId             int              `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	ApplicationLevel     ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
	CallBackLinkValidity int              `json:"call_back_link_validity"`
	AppSecret            string           `json:"app_secret"`
	CallBackLink         string           `json:"call_back_link"`
	PetSupplierID        uint             `json:"pet_supplier_id"`
	User                 User             `json:"user" gorm:"foreignKey:MemberId"`
	AppName              string           `json:"app_name"`
}

func (Application) TableName() string {
	return "application"
}

// 如果含有time.Time 请自行import time包
type ApplicationLevel struct {
	source.Model
	ServerRadio int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:;type:int;size:10;"`
	NumMax      int `json:"numMax" form:"numMax" gorm:"column:num_max;comment:;type:int;size:10;"`
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}

type ApplicationSource struct {
	source.Model
	Type             string `json:"type" form:"type" gorm:"column:type;comment:来源标识（属于哪个供应链）;type:varchar(255);size:255;"`
	Name             string `json:"name" form:"name" gorm:"column:name;comment:来源原本名称;type:varchar(255);size:255;"`
	SourceID         uint   `json:"source_id" form:"source_id" gorm:"column:source_id;comment:来源id 唯一;type:int(10);size:10;"`
	Title            string `json:"title" form:"title" gorm:"column:title;comment:来源自定义名称;type:varchar(255);size:255;"`
	ApplicationTitle string `json:"application_title" form:"application_title" gorm:"column:application_title;comment:供应链名称;type:varchar(255);size:255;"`
}

// GetProductInfoList
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProductInfoList
// @description: 分页获取Product完整记录列表
// @param: info request.ProductSearch
// @return: err error, list []model.Product, total int64
func GetProductDetailList(ids []int, appID uint, supplyLineId string, userID uint) (err error, list []other.Product) {
	// 获取所有应用来源
	applicationSource, err := cache.GetAllApplicationSourcesFromCache()
	if err != nil {
		return
	}
	var application *model3.Application
	application, err = cache.GetApplicationFromCache(appID)
	if err != nil {
		return
	}

	db := source.DB().Preload("Brand").Preload("Skus").Preload("Category1").Preload("Category2").Preload("Category3").Preload("Supplier").Preload("GatherSupply")
	var banSupplierIds []uint
	err = source.DB().Model(response.Supplier{}).Where("is_storage = 1").Pluck("id", &banSupplierIds).Error
	if err != nil {
		return
	}
	if len(banSupplierIds) > 0 {
		db = db.Where("supplier_id not in ?", banSupplierIds)

	}

	var user Users
	err = source.DB().Preload("ThousandsPrices.Products").Preload("ThousandsPrices.ThousandsPricesProducts").First(&user, userID).Error
	if err != nil {
		return
	}

	var thousandsProductIds = make(map[uint]uint)
	if len(user.ThousandsPrices.Products) > 0 && user.ThousandsPrices.Status == 1 && user.ThousandsPrices.FilterImport == 1 {
		//千人千价 只查询指定商品
		for _, product := range user.ThousandsPrices.Products {
			thousandsProductIds[product.ID] = product.ID
		}
		var tids []int
		for _, id := range ids {
			if _, ok := thousandsProductIds[uint(id)]; ok {
				tids = append(tids, id)
			}
		}
		ids = tids
	}

	db = db.Where("id in ?", ids)

	db = db.Where("deleted_at is null")

	err = db.Limit(100).Find(&list).Error

	var productList []other.Product
	var percent, isDefault int
	err, percent, isDefault = levelModel.GetLevelDiscountPercent(user.LevelID)
	var isValuation = 0

	supplierMap := make(map[int]string)
	// 查询供应商基础设置
	supplierSettingErr, supplierSetting := GetSupplierSettingByName()
	if supplierSettingErr != nil {
		err = errors.New("获取供应商设置失败")
		return
	}
	if supplierSetting.Values.SupplierNameType == 1 {
		// 获取店铺设置
		shopSettingErr, shopSet := shopSetting.Get()
		if shopSettingErr != nil {
			err = errors.New("获取店铺设置失败")
			return
		}
		for _, product := range list {
			if product.SupplierID > 0 {
				supplierMap[product.Supplier.ID] = shopSet.ShopName + "[" + strconv.Itoa(product.Supplier.ID) + "]"
			}
		}
	}

	for _, product := range list {
		if supplyLineId != "" {
			//防止导入自己的商品
			if strings.Contains(product.SupplyLine, supplyLineId) {
				continue
			}
		}
		// 这里覆盖掉name 对外 显示 ShopName
		// product.Supplier.Name = product.Supplier.ShopName
		if product.Supplier.ID > 0 {
			if name, ok := supplierMap[product.Supplier.ID]; ok {
				product.Supplier.Name = name
			}
		}

		//对外供应链名称
		var gettingData GettingData
		if product.GatherSupplyID > 0 {
			_, gettingData = GetGatherSetting(product.GatherSupplyID)
			product.GatherSupplyName = gettingData.BaseInfo.StoreName
		}

		if product.SourceName == "" {
			var name = ""
			if product.GatherSupplyID == 0 {
				name = "自营"
			} else {

				//如果供应链没设置名称则使用 自定义的来源名称
				if product.GatherSupply.CategoryID == 1 {
					isValuation = 0
					for _, asItem := range applicationSource {
						if asItem.SourceID == uint(product.Source) {
							if asItem.Title == "" {
								name = asItem.Name
							} else {
								name = asItem.Title
							}
							isValuation = 1
						}
					}
					if isValuation == 0 {
						name = GetMap(uint(product.Source))
					}
				} else {
					if gettingData.BaseInfo.StoreName != "" {
						name = gettingData.BaseInfo.StoreName
					} else if product.GatherSupply.Name != "" {
						name = product.GatherSupply.Name
					} else {
						isValuation = 0
						for _, asItem := range applicationSource {
							if asItem.SourceID == uint(product.Source) {
								if asItem.Title == "" {
									name = asItem.Name
								} else {
									name = asItem.Title
								}
								isValuation = 1
							}
						}
						if isValuation == 0 {
							name = GetMap(uint(product.Source))
						}
					}
				}
			}
			product.SourceName = name
		}

		if err != nil {
			return
		}
		if product.UserPriceSwitch == 1 {
			var calculateRes bool
			product.AgreementPrice, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Price, user.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, product.AgreementPrice = levelModel.GetLevelDiscountAmount(product.Price, product.CostPrice, percent)
				} else {
					product.AgreementPrice = product.Price
				}
			}
		} else {
			if isDefault == 0 {
				err, product.AgreementPrice = levelModel.GetLevelDiscountAmount(product.Price, product.CostPrice, percent)
			} else {
				product.AgreementPrice = product.Price
			}
		}

		if err != nil {
			return
		}
		product.AgreementPrice = uint(math.Floor(float64(product.AgreementPrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
		product.ActivityPrice = uint(math.Floor(float64(product.ActivityPrice)))
		product.MarketPrice = product.OriginPrice
		//if product.ActivityPrice > 0 {
		//	product.CostPrice = product.ActivityPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		//} else {
		product.Price = product.AgreementPrice     //建议成本价 如果存在ActivityiPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		product.CostPrice = product.AgreementPrice //建议成本价 如果存在ActivityiPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		//}
		product.SalePrice = uint(math.Floor(float64(product.GuidePrice))) //建议销售价

		var totalSku int
		for i, sku := range product.Skus {
			if product.UserPriceSwitch == 1 {
				var calculateRes bool
				product.Skus[i].Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, user.LevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					if isDefault == 0 {
						err, product.Skus[i].Price = levelModel.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
						if err != nil {
							return
						}
					}
				}
			} else {
				if isDefault == 0 {
					err, product.Skus[i].Price = levelModel.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
					if err != nil {
						return
					}
				}
			}
			product.Skus[i].Price = uint(math.Floor(float64(product.Skus[i].Price) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
			product.Skus[i].CostPrice = product.Skus[i].Price
			totalSku += sku.Stock
		}
		product.Stock = uint(totalSku)
		productList = append(productList, product)
	}
	//千人千价 替换价格
	if isThousandsPriceStatus := GetThousandsPriceStatus(user.ThousandsPrices); isThousandsPriceStatus > 0 {
		var productPriceMap = map[uint]uint{}
		var skuPriceMap = map[uint]uint{}
		var guidePriceMap = map[uint]uint{}
		var guideSkuPriceMap = map[uint]uint{}
		var originPriceMap = map[uint]uint{}
		var originSkuPriceMap = map[uint]uint{}
		for _, tppData := range user.ThousandsPrices.ThousandsPricesProducts {
			productPriceMap[tppData.ProductID] = tppData.Price
			skuPriceMap[tppData.SkuID] = tppData.SkuPrice
			guidePriceMap[tppData.ProductID] = tppData.GuidePrice
			guideSkuPriceMap[tppData.SkuID] = tppData.SkuGuidePrice
			originPriceMap[tppData.ProductID] = tppData.OriginPrice
			originSkuPriceMap[tppData.SkuID] = tppData.SkuOriginPrice
		}
		for pk, productData := range productList {
			if _, ok := productPriceMap[productData.ID]; ok {
				if productPriceMap[productData.ID] > 0 {
					if isThousandsPriceStatus == 1 {
						//不额外计算技术服务费
						productList[pk].AgreementPrice = productPriceMap[productData.ID]
					} else {
						productList[pk].AgreementPrice = uint(math.Floor(float64(productPriceMap[productData.ID]) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
					}

					productList[pk].Price = productList[pk].AgreementPrice
					productList[pk].CostPrice = productList[pk].AgreementPrice
				}
				if guidePriceMap[productData.ID] > 0 {
					productList[pk].GuidePrice = guidePriceMap[productData.ID]
				}
				if originPriceMap[productData.ID] > 0 {
					productList[pk].OriginPrice = originPriceMap[productData.ID]
				}
				for sk, sku := range productData.Skus {
					if _, sok := skuPriceMap[sku.ID]; sok {
						if skuPriceMap[sku.ID] > 0 {
							if isThousandsPriceStatus == 1 {
								//不额外计算技术服务费
								productData.Skus[sk].Price = skuPriceMap[sku.ID]
								productData.Skus[sk].CostPrice = skuPriceMap[sku.ID]
							} else {
								productData.Skus[sk].Price = uint(math.Floor(float64(skuPriceMap[sku.ID]) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
								productData.Skus[sk].CostPrice = uint(math.Floor(float64(skuPriceMap[sku.ID]) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
							}

						}
						if guideSkuPriceMap[sku.ID] > 0 {
							productData.Skus[sk].GuidePrice = guideSkuPriceMap[sku.ID]
						}
						if originSkuPriceMap[sku.ID] > 0 {
							productData.Skus[sk].OriginPrice = originSkuPriceMap[sku.ID]
						}
					}
				}
			}
			max, min := GetSkuPrice(productList[pk].Skus)
			productList[pk].MinPrice = min
			productList[pk].MaxPrice = max
		}

	}
	return err, productList
}
func GetSkuPrice(skuList []model.Sku) (maxPrice, minPrice uint) {
	var priceList []int
	for _, item := range skuList {
		priceList = append(priceList, int(item.Price))
	}
	if len(priceList) <= 0 {
		maxPrice = 0
		minPrice = 0
		return
	}
	sort.Ints(priceList)
	minPrice = uint(priceList[0])
	//sort.Sort(sort.Reverse(sort.IntSlice(priceList)))
	maxPrice = uint(priceList[len(priceList)-1])

	return
}
func GetMap(id uint) (name string) {

	for _, item := range config.SupplyMap {

		if item.ID == int(id) {
			return item.Name
		}

	}
	return
}

type SysSetting struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	Key       string         `json:"key" form:"key" gorm:"column:key;comment:关键字;type:varchar(255);size:255;"`
	Value     string         `json:"value" form:"value" gorm:"column:value;comment:值;type:longtext;"`
	CreatedAt time.Time      `json:"-"`
	UpdatedAt time.Time      `json:"-"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
type GettingData struct {
	BaseInfo BaseInfoData `json:"baseInfo"`
}

type BaseInfoData struct {
	StoreName             string `json:"storeName"`
	ShowSourceInMyStorage int    `json:"show_source_in_my_storage"`
}

var jstSetting = make(map[uint]GettingData)

func getSetting(key string) (err error, sysSetting SysSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}
func GetGatherSetting(gatherSupplyID uint) (err error, setting GettingData) {
	if _, ok := jstSetting[gatherSupplyID]; !ok {
		var sysSetting SysSetting
		err, sysSetting = getSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil {
			return
		}
		var settingD GettingData
		err = json.Unmarshal([]byte(sysSetting.Value), &settingD)
		jstSetting[gatherSupplyID] = settingD
	}
	return err, jstSetting[gatherSupplyID]
}

// GetProductInfoList
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProductInfoList
// @description: 分页获取Product完整记录列表
// @param: info request.ProductSearch
// @return: err error, list []model.Product, total int64
func PutProductStorage(ids []int, appID uint) (err error) {

	//选品数量限制
	var app *model3.Application
	app, err = cache.GetApplicationFromCache(appID)
	if err != nil {
		return
	}

	var alreadyImportProductIds []int
	isLoaded, alreadyImportProductIdsFromCache := GetAppProductIDs(appID)
	if isLoaded {
		alreadyImportProductIds = alreadyImportProductIdsFromCache
	} else {
		if err != nil {
			return
		}
	}

	if len(alreadyImportProductIds)+len(ids) > app.ApplicationLevel.NumMax && len(ids) > 0 && app.ApplicationLevel.NumMax > 0 {
		return errors.New("导入失败，超过此应用选品数量限制")
	}

	difference := collection.Collect(ids).Diff(alreadyImportProductIds).ToIntArray()

	//获得最终选品数据
	var insertStorages []model.Storage
	for _, iv := range difference {
		insertStorages = append(insertStorages, model.Storage{AppID: appID, ProductID: uint(iv)})
	}
	err = source.DB().Model(&model.Storage{}).CreateInBatches(&insertStorages, 100).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&Product{}).Where("id in ?", difference).Update("updated_at", time.Now().Format("2006-01-02 15:04:05")).Error
	if err != nil {
		return
	}
	for _, sv := range difference {
		err = mq.PublishMessage(uint(sv), mq.StorageChange, 0)
		if err != nil {
			continue
		}
	}
	return
}

func DeleteProductStorage(ids []int, appID uint) (err error) {
	err = source.DB().Where("app_id = ?", appID).Where("product_id in ?", ids).Delete(&model.Storage{}).Error
	if err != nil {
		return err
	}
	for _, sv := range ids {
		err = mq.PublishMessage(uint(sv), mq.StorageChange, 0)
		if err != nil {
			continue
		}
	}
	return
}

// GetProductCardList
//
// @function: GetProductCardList
// @description: 按条件分页获取Product Card销售信息列表
// @param: info request.ProductCardListSearch
// @return: err error, list []response.Product, total int64
// level 1-10 按照等级列表权重正序  未登录或者默认传1即可
func GetSupplierProductCardList(info request.SupplierProductCardListSearch, level int) (err error, list []response2.ProductNew, total int64) {
	if info.MaxPrice != 0 && info.MaxPrice <= info.MinPrice {
		err = errors.New("最大金额不能小于最小金额")
		return
	}
	var profitString = "profit"         //利润筛选字段
	var priceString = "agreement_price" //批发价筛选字段
	//根据等级赋值筛选字段
	if level != 0 {
		profitString = "level_" + strconv.Itoa(level) + "_profit"
		//priceString = "level_"+strconv.Itoa(level)+"_price" //不是筛选超级批发价暂时去掉这里
	}
	if info.PageSize == 0 {
		info.PageSize = 10
	}
	if info.Page == 0 {
		info.Page = 1
	}
	if info.Page > 100 {
		info.Page = 100
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	db := source.DB()
	//db = db.Where("`is_display` = ?", 1)
	db = db.Where("deleted_at is NULL")
	db = db.Where("freeze = 0")

	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
	}

	filterQ := elastic.NewBoolQuery()

	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
		db = db.Where("`is_display` = ?", &info.IsDisplay)
	} else {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		db = db.Where("`is_display` = ?", 1)
	}
	filterQ.Must(elastic.NewMatchQuery("is_plugin", 0))
	if info.SupplierID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}

	if info.IsRecommend != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", info.IsRecommend))
	}
	if info.IsNew != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_new", info.IsNew))
	}
	if info.IsHot != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_hot", info.IsHot))
	}
	if info.IsPromotion != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", info.IsPromotion))
	}
	if info.MinPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice))
	}
	if info.MaxPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice))
	}

	if info.PriceForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Gte(info.PriceForm))
	}
	if info.PriceTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Lte(info.PriceTo))
	}

	if info.ProfitForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Gte(info.ProfitForm))
	}
	if info.ProfitTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Lte(info.ProfitTo))
	}

	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("min_origin_rate").Gte(Decimal(float64(*info.OriginRate.From))))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("max_origin_rate").Lte(Decimal(float64(*info.OriginRate.To))))
	}

	//排除指定source商品
	//filterQ.MustNot(elastic.NewMatchQuery("source", 0))
	// 指定供应链
	if info.GatherSupplyID != nil {
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
	} else {
		var specialSupplyIDs []uint
		err, specialSupplyIDs = getSupplyIDs()
		if len(specialSupplyIDs) > 0 {
			supplyIDs := make([]interface{}, len(specialSupplyIDs))
			for index, value := range specialSupplyIDs {
				supplyIDs[index] = value
			}
			filterQ.MustNot(elastic.NewTermsQuery("gather_supplier_id", supplyIDs...))
		}
	}

	boolQ.Filter(filterQ)
	sort := "sort"
	asc := false
	//调整switch与备注一致 增加一些排序兼容H5使用
	// 1综合降2价格降3价格升4销量降5销量升6创建时间降7创建时间升8利润降9利润升
	if info.SortBy != 0 {
		switch info.SortBy {
		case 1:
			//sort = "id"
			//asc = false
			//db = db.Order("id desc")
		case 2:
			sort = "agreement_price"
			asc = true
		case 3:
			sort = "agreement_price"
			asc = false
		case 4:
			sort = "sales"
			asc = false
		case 5:
			sort = "sales"
			asc = true
		case 6:
			sort = "created_at"
			asc = false
		case 7:
			sort = "created_at"
			asc = true
		case 8:
			sort = profitString
			asc = false
		case 9:
			sort = profitString
			asc = true
			break
		case 10:
			sort = "origin_rate"
			asc = false
			break
		case 11:
			sort = "origin_rate"
			asc = true
			break

			//	sort = "id"
			//	asc = false
			//	db = db.Order("id desc")
			//	break
			//case 2:
			//	sort = "agreement_price"
			//	asc = true
			//	db = db.Order("price")
			//	break
			//case 3:
			//	sort = "agreement_price"
			//	asc = false
			//	db = db.Order("price desc")
			//	break
			//case 4:
			//	sort = "sales"
			//	asc = false
			//	db = db.Order("sales desc")
			//	break
			//case 5:
			//	sort = "updated_at"
			//	asc = false
			//	db = db.Order("updated_at desc")
			//	break
			//case 6:
			//	sort = "id"
			//	asc = true
			//	db = db.Order("id")
			//	break
			//case 7:
			//	sort = "sales"
			//	asc = true
			//	db = db.Order("sales")
			//	break
			//case 8:
			//	sort = "updated_at"
			//	asc = true
			//	db = db.Order("updated_at")
			//	break
			//case 9:
			//	sort = "origin_rate"
			//	asc = false
			//	break
			//case 10:
			//	sort = "origin_rate"
			//	asc = true
			//	break

		}
	}
	//es执行搜索
	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(sort, asc).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	var ids []uint

	var productSearchs []ProductElasticSearch
	//获取es搜索结果
	productSearchs, err = GetSearchResult(res)
	var idsString string
	for _, v := range productSearchs {
		idsString += "," + strconv.Itoa(int(v.ID))
		ids = append(ids, v.ID)
	}
	if len(ids) > 0 {
		//按照es查询出来的id顺序进行排序
		idsString = idsString[2:utf8.RuneCountInString(idsString)]
	}
	db = db.Where("id in ?", ids)
	var productCards []response2.ProductNew
	//因为排序会因为in 导致乱了 所以增加order进行排序
	err = db.Preload("Supplier").Order("FIND_IN_SET(id,'" + idsString + "')").Find(&productCards).Error
	for itemKey, item := range productCards {
		productCards[itemKey].Level = level
		for _, esItem := range productSearchs {
			if item.ID == esItem.ID {
				switch level {
				case 1:
					productCards[itemKey].LevelPrice = uint(esItem.Level1Price)
					productCards[itemKey].LevelProfit = esItem.Level1Profit
					break
				case 2:
					productCards[itemKey].LevelPrice = uint(esItem.Level2Price)
					productCards[itemKey].LevelProfit = esItem.Level2Profit
					break
				case 3:
					productCards[itemKey].LevelPrice = uint(esItem.Level3Price)
					productCards[itemKey].LevelProfit = esItem.Level3Profit
					break
				case 4:
					productCards[itemKey].LevelPrice = uint(esItem.Level4Price)
					productCards[itemKey].LevelProfit = esItem.Level4Profit
					break
				case 5:
					productCards[itemKey].LevelPrice = uint(esItem.Level5Price)
					productCards[itemKey].LevelProfit = esItem.Level5Profit
					break
				case 6:
					productCards[itemKey].LevelPrice = uint(esItem.Level6Price)
					productCards[itemKey].LevelProfit = esItem.Level6Profit
					break
				case 7:
					productCards[itemKey].LevelPrice = uint(esItem.Level7Price)
					productCards[itemKey].LevelProfit = esItem.Level7Profit
					break
				case 8:
					productCards[itemKey].LevelPrice = uint(esItem.Level8Price)
					productCards[itemKey].LevelProfit = esItem.Level8Profit
					break
				case 9:
					productCards[itemKey].LevelPrice = uint(esItem.Level9Price)
					productCards[itemKey].LevelProfit = esItem.Level9Profit
					break
				case 10:
					productCards[itemKey].LevelPrice = uint(esItem.Level10Price)
					productCards[itemKey].LevelProfit = esItem.Level10Profit
					break
				default:
					productCards[itemKey].LevelPrice = esItem.AgreementPrice
					productCards[itemKey].LevelProfit = esItem.Profit
					break
				}

			}

		}

	}
	return err, productCards, total
}

type User struct {
	ID                uint            `json:"id" form:"id" gorm:"primarykey"`
	LevelID           uint            `json:"level_id" form:"level_id"`
	UserLevel         UserLevel       `json:"user_level" form:"user_level" gorm:"foreignKey:LevelID;references:ID"`
	ThousandsPricesID uint            `json:"thousands_prices_id"`
	ThousandsPrices   ThousandsPrices `json:"thousands_prices"`
	Level             int             `json:"level"`
}

//GetProductCardList
//
//@function: GetProductCardList
//@description: 按条件分页获取Product Card销售信息列表 根据用户等级返回相应的批发价和利润
//@param: info request.ProductCardListSearch
//@return: err error, list []response.Product, total int64

func GetProductCardListByCollection(info request.CollectionProductCardList, uid uint) (err error, list []response2.ProductNew, total int64) {
	var level = 0
	var profitString = "profit"         //利润筛选字段
	var priceString = "agreement_price" //批发价筛选字段
	if uid != 0 {
		level = GetLevel(uid)
	}
	//根据等级赋值筛选字段
	if level != 0 {
		profitString = "level_" + strconv.Itoa(level) + "_profit"
		//priceString = "level_"+strconv.Itoa(level)+"_price" //不是筛选超级批发价暂时去掉这里
	}
	if info.PageSize == 0 {
		info.PageSize = 10
	}
	if info.Page == 0 {
		info.Page = 1
	}
	if info.Page > 100 {
		info.Page = 100
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	var collection model.Collection
	err = source.DB().First(&collection, info.CollectionID).Error
	if err != nil {
		return
	}
	es, err := source.ES()
	if err != nil {
		return
	}
	db := source.DB().Model(&model.Product{})
	//db = db.Where("`is_display` = ?", 1)
	db = db.Where("deleted_at is NULL")
	db = db.Where("freeze = 0")
	db = db.Where("is_plugin = 0")
	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
	}

	filterQ := elastic.NewBoolQuery()
	filterQ.Must(elastic.NewMatchQuery("is_plugin", 0))

	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
		db = db.Where("`is_display` = ?", &info.IsDisplay)
	} else {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		db = db.Where("`is_display` = ?", 1)
	}
	if info.SupplierID != 0 {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}

	if info.IsRecommend != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", info.IsRecommend))
	}
	if info.IsNew != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_new", info.IsNew))
	}
	if info.IsHot != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_hot", info.IsHot))
	}
	if info.IsPromotion != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", info.IsPromotion))
	}
	if info.MinPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice * 100))
	}
	if info.MaxPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice * 100))
	}

	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("min_origin_rate").Gte(Decimal(float64(*info.OriginRate.From))))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("max_origin_rate").Lte(Decimal(float64(*info.OriginRate.To))))
	}

	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}

	// 指定供应链
	if info.GatherSupplyID != nil {
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
	} else {
		if supply.ID != 0 {
			filterQ.MustNot(elastic.NewMatchQuery("gather_supplier_id", supply.ID))
		}
	}
	sort := "sort"
	asc := false

	if collection.Type == 1 {
		total = int64(collection.Num)
		if info.PageSize*info.Page >= int(total) {
			limit = int(total) - offset
		}
		var relations []uint
		err = source.DB().Model(&model.CollectionProduct{}).Where("collection_id = ?", info.CollectionID).Pluck("product_id", &relations).Error
		if err != nil {
			return
		}
		relationIds := make([]interface{}, len(relations))
		for index, value := range relations {
			relationIds[index] = value
		}
		filterQ.Must(elastic.NewTermsQuery("id", relationIds...))

	} else if collection.Type == 2 {
		total = int64(collection.Filter.CategoryProductNum)
		if info.PageSize*info.Page >= int(total) {
			limit = int(total) - offset
		}
		//err = source.DB().Model(&model.Product{}).Where("`category1_id` in ?", []uint(collection.Filter.Category1ID)).Where("`category2_id` in ?", []uint(collection.Filter.Category2ID)).Where("`category3_id` in ?", []uint(collection.Filter.Category3ID)).Where("`is_display` = 1").Limit(collection.Filter.CategoryProductNum).Limit(limit).Offset(offset).Find(&list).Error
		//if err != nil {
		//	return
		//}
		category1Ids := make([]interface{}, len([]uint(collection.Filter.Category1ID)))
		for index, value := range []uint(collection.Filter.Category1ID) {
			category1Ids[index] = value
		}
		filterQ.Must(elastic.NewTermsQuery("category_1_id", category1Ids...))

		category2Ids := make([]interface{}, len([]uint(collection.Filter.Category2ID)))
		for index, value := range []uint(collection.Filter.Category2ID) {
			category2Ids[index] = value
		}
		filterQ.Must(elastic.NewTermsQuery("category_2_id", category2Ids...))

		category3Ids := make([]interface{}, len([]uint(collection.Filter.Category3ID)))
		for index, value := range []uint(collection.Filter.Category3ID) {
			category3Ids[index] = value
		}
		filterQ.Must(elastic.NewTermsQuery("category_3_id", category3Ids...))

	} else if collection.Type == 3 {
		total = int64(collection.Filter.AttributeProductNum)
		if info.PageSize*info.Page >= int(total) {
			limit = int(total) - offset
		}
		switch collection.Filter.AttributeType {
		case 1:
			filterQ.Must(elastic.NewMatchQuery("is_hot", 1))
			break
		case 2:
			filterQ.Must(elastic.NewMatchQuery("is_recommend", 1))
			break
		case 3:
			filterQ.Must(elastic.NewMatchQuery("is_new", 1))
			break
		case 4:
			filterQ.Must(elastic.NewMatchQuery("is_promotion", 1))
			break
		}

	} else if collection.Type == 4 {
		total = int64(collection.Filter.StatisticProductNum)
		if info.PageSize*info.Page >= int(total) {
			limit = int(total) - offset
		}
		if collection.Filter.StatisticType == 1 {
			sort = "sales"
			asc = false
			db = db.Order("sales desc")

		} else if collection.Filter.StatisticType == 2 {
			sort = "created_at"
			asc = false
			db = db.Order("created_at desc")

		}
		if err != nil {
			return
		}
	}
	//var discount = 10000
	//if uid != 0 {
	//	var user User
	//	source.DB().Where("id = ?", uid).Preload("UserLevel").First(&user)
	//	if user.ID != 0 && user.UserLevel.ID != 0 {
	//		discount = user.UserLevel.Discount
	//	}
	//}
	//利润排序
	if info.ProfitForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Gte(info.ProfitForm))
	}
	if info.ProfitTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Lte(info.ProfitTo))
	}
	//批发价排序
	if info.PriceForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Gte(info.PriceForm))
	}
	if info.PriceTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Lte(info.PriceTo))
	}

	boolQ.Filter(filterQ)
	boolQ.Must(elastic.NewMatchQuery("is_plugin", 0))

	//if info.SortBy != 0 {
	//	switch info.SortBy {
	//	case 1:
	//		sort = "id"
	//		asc = false
	//		db = db.Order("id desc")
	//		break
	//	case 2:
	//		sort = "agreement_price"
	//		asc = true
	//		db = db.Order("price")
	//		break
	//	case 3:
	//		sort = "agreement_price"
	//		asc = false
	//		db = db.Order("price desc")
	//		break
	//	case 4:
	//		sort = "sales"
	//		asc = false
	//		db = db.Order("sales desc")
	//		break
	//	case 5:
	//		sort = "updated_at"
	//		asc = false
	//		db = db.Order("updated_at desc")
	//		break
	//	case 6:
	//		sort = "id"
	//		asc = true
	//		db = db.Order("id")
	//		break
	//	case 7:
	//		sort = "sales"
	//		asc = true
	//		db = db.Order("sales")
	//		break
	//	case 8:
	//		sort = "updated_at"
	//		asc = true
	//		db = db.Order("updated_at")
	//		break
	//	case 9:
	//		sort = "origin_rate"
	//		asc = false
	//		break
	//	case 10:
	//		sort = "origin_rate"
	//		asc = true
	//		break
	//	}
	//}
	if info.SortBy != 0 {
		switch info.SortBy {
		case 1:
			//sort = "id"
			//asc = false
			//db = db.Order("id desc")
		case 2:
			sort = "agreement_price"
			asc = true
		case 3:
			sort = "agreement_price"
			asc = false
		case 4:
			sort = "sales"
			asc = false
		case 5:
			sort = "sales"
			asc = true
		case 6:
			sort = "created_at"
			asc = false
		case 7:
			sort = "created_at"
			asc = true
		case 8:
			sort = profitString
			asc = false
		case 9:
			sort = profitString
			asc = true
			break
		case 10:
			sort = "origin_rate"
			asc = false
			break
		case 11:
			sort = "origin_rate"
			asc = true
			break
		}
	}
	var realTotal int64
	realTotal, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if realTotal <= total {
		total = realTotal
	}
	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(sort, asc).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	var ids []uint
	var productSearchs []ProductElasticSearch
	//获取es搜索结果
	var idsString string
	productSearchs, err = GetSearchResult(res)
	for _, v := range productSearchs {
		idsString += "," + strconv.Itoa(int(v.ID))
		ids = append(ids, v.ID)
	}
	err = db.Preload("Skus").Where("id in ?", ids).Order("FIND_IN_SET(id,'" + idsString + "')").Find(&list).Error

	var percent, isDefault int
	err, percent, isDefault = levelModel.GetLevelDiscountPercent(info.UserLevelID)
	if err != nil {
		return
	}

	for itemKey, item := range list {
		list[itemKey].NormalPrice = item.Price
		list[itemKey].Level = level
		if list[itemKey].UserPriceSwitch == 1 {
			var calculateRes bool
			list[itemKey].LevelPrice, _, calculateRes = list[itemKey].UserPrice.GetProductLevelDiscountPrice(item.Price, info.UserLevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, list[itemKey].LevelPrice = levelModel.GetLevelDiscountAmount(uint(item.Price), uint(item.ExecPrice), percent)
					if err != nil {
						return
					}
					list[itemKey].Price = list[itemKey].LevelPrice
				} else {
					list[itemKey].LevelPrice = item.Price
				}
			}
		} else {
			if isDefault == 0 {
				err, list[itemKey].LevelPrice = levelModel.GetLevelDiscountAmount(uint(item.Price), uint(item.ExecPrice), percent)
				if err != nil {
					return
				}
				list[itemKey].Price = list[itemKey].LevelPrice
			} else {
				list[itemKey].LevelPrice = item.Price
			}
		}

		list[itemKey].LevelProfit = item.GuidePrice - int(list[itemKey].LevelPrice)

		var maxOriginPrice, minOriginPrice, maxGuidePrice, maxActivityPrice, minPrice, maxPrice, minNormalPrice, maxNormalPrice uint
		for skuKey, sku := range item.Skus {
			item.Skus[skuKey].NormalPrice = sku.Price
			if minNormalPrice == 0 || item.Skus[skuKey].NormalPrice <= minNormalPrice {
				minNormalPrice = item.Skus[skuKey].NormalPrice
			}
			if item.Skus[skuKey].NormalPrice > maxNormalPrice {
				maxNormalPrice = item.Skus[skuKey].NormalPrice
			}
			if list[itemKey].UserPriceSwitch == 1 {
				var calculateRes bool
				item.Skus[skuKey].Price, _, calculateRes = list[itemKey].UserPrice.GetProductLevelDiscountPrice(item.Skus[skuKey].Price, info.UserLevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					if isDefault == 0 {
						err, item.Skus[skuKey].Price = levelModel.GetLevelDiscountAmount(item.Skus[skuKey].Price, item.Skus[skuKey].ExecPrice, percent)
						if err != nil {
							return
						}
					}
				}
			} else {
				if isDefault == 0 {
					err, item.Skus[skuKey].Price = levelModel.GetLevelDiscountAmount(item.Skus[skuKey].Price, item.Skus[skuKey].ExecPrice, percent)
					if err != nil {
						return
					}
				}
			}
			if minPrice == 0 || item.Skus[skuKey].Price <= minPrice {
				minPrice = item.Skus[skuKey].Price
			}
			if item.Skus[skuKey].Price > maxPrice {
				maxPrice = item.Skus[skuKey].Price
			}
			if item.Skus[skuKey].OriginPrice > maxOriginPrice {
				maxOriginPrice = item.Skus[skuKey].OriginPrice
			}
			if item.Skus[skuKey].GuidePrice > maxGuidePrice {
				maxGuidePrice = item.Skus[skuKey].GuidePrice
			}
			if item.Skus[skuKey].ActivityPrice > maxActivityPrice {
				maxActivityPrice = item.Skus[skuKey].ActivityPrice
			}
			if minOriginPrice == 0 || item.Skus[skuKey].OriginPrice <= minOriginPrice {
				minOriginPrice = item.Skus[skuKey].OriginPrice
			}
		}
		list[itemKey].MaxOriginPrice = maxOriginPrice
		list[itemKey].MinOriginPrice = minOriginPrice
		list[itemKey].MaxGuidePrice = maxGuidePrice
		list[itemKey].MaxActivityPrice = maxActivityPrice
		list[itemKey].MinPrice = int(minPrice)
		list[itemKey].MaxPrice = int(maxPrice)
		list[itemKey].MaxNormalPrice = maxNormalPrice
		list[itemKey].MinNormalPrice = minNormalPrice
	}
	return err, list, total

}

// GetProductSalesInfo
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProduct
// @description: 根据id获取Product销售信息
// @param: id uint
// @return: err error, product response.Product
func GetProductSalesInfo(info yzRequest.GetByIdAndUid) (err error, product response.Product) {
	err = source.DB().Where("id = ?", info.Id).Where("freeze = 0").Preload("Skus").First(&product).Error
	if err != nil {
		err = errors.New("商品不存在")
		return
	}
	product.NormalPrice = product.Price
	if info.UserID > 0 {
		var user User
		err = source.DB().Where("id = ?", info.UserID).First(&user).Error
		if err != nil {
			return
		}

		var discount, isDefault int
		err, discount, isDefault = levelModel.GetLevelDiscountPercent(user.LevelID)
		if err != nil {
			return
		}
		if product.UserPriceSwitch == 1 {
			var calculateRes bool
			product.Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Price, user.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, product.Price = levelModel.GetLevelDiscountAmount(product.Price, product.ExecPrice, discount)
					if err != nil {
						return
					}
				}
			}
		} else {
			if isDefault == 0 {
				err, product.Price = levelModel.GetLevelDiscountAmount(product.Price, product.ExecPrice, discount)
				if err != nil {
					return
				}
			}
		}

		var maxOriginPrice, minOriginPrice, maxGuidePrice, maxActivityPrice, minPrice, maxPrice, minNormalPrice, maxNormalPrice, minDiscount, maxDiscount, minDiscountRatio, maxDiscountRatio uint
		for optionKey, option := range product.SkuSelect.Options {
			for skuKey, sku := range option.Skus {
				product.SkuSelect.Options[optionKey].Skus[skuKey].NormalPrice = sku.Price

				if product.UserPriceSwitch == 1 {
					var calculateRes bool
					product.SkuSelect.Options[optionKey].Skus[skuKey].Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, user.LevelID)
					// 商品没有设置该等级，使用默认折扣
					if calculateRes == false {
						err, product.SkuSelect.Options[optionKey].Skus[skuKey].Price = levelModel.GetLevelDiscountAmount(sku.Price, sku.ExecPrice, discount)
						if err != nil {
							return
						}
					}
				} else {
					if isDefault == 0 {
						err, product.SkuSelect.Options[optionKey].Skus[skuKey].Price = levelModel.GetLevelDiscountAmount(sku.Price, sku.ExecPrice, discount)
						if err != nil {
							return
						}
					}
				}
				// 利润 = (市场价 - 批发价)
				// 利润率 = 利润 / 批发价
				var profit uint
				if product.SkuSelect.Options[optionKey].Skus[skuKey].OriginPrice > product.SkuSelect.Options[optionKey].Skus[skuKey].Price && product.SkuSelect.Options[optionKey].Skus[skuKey].Price > 0 && product.SkuSelect.Options[optionKey].Skus[skuKey].OriginPrice > 0 {
					profit = product.SkuSelect.Options[optionKey].Skus[skuKey].OriginPrice - product.SkuSelect.Options[optionKey].Skus[skuKey].Price
				} else {
					profit = 0
				}
				// 利润率 2000分 / 10000分 = 0.2 * 1000 = 2000 前端 / 100 = 20% = 0.2 * 100 = 20%
				var profitRate float64
				if profit > 0 && product.SkuSelect.Options[optionKey].Skus[skuKey].Price > 0 {
					profitRate = Decimal((float64(profit) / float64(product.SkuSelect.Options[optionKey].Skus[skuKey].Price)) * 100)
				} else {
					profitRate = 0
				}
				product.SkuSelect.Options[optionKey].Skus[skuKey].ProfitRate = profitRate
				// 毛利率 = 利润 / 市场价
				// 毛利率 2000分 / 10000分 = 0.2 * 1000 = 2000 前端 / 100 = 20% = 0.2 * 100 = 20%
				var grossProfitRate float64
				if product.SkuSelect.Options[optionKey].Skus[skuKey].OriginPrice > 0 && profit > 0 {
					grossProfitRate = Decimal((float64(profit) / float64(product.SkuSelect.Options[optionKey].Skus[skuKey].OriginPrice)) * 100)
				} else {
					grossProfitRate = 0
				}
				product.SkuSelect.Options[optionKey].Skus[skuKey].GrossProfitRate = grossProfitRate
				// 计算折扣
				var discountRow, discountRowRatio uint
				if product.SkuSelect.Options[optionKey].Skus[skuKey].Price > 0 && product.SkuSelect.Options[optionKey].Skus[skuKey].OriginPrice > 0 {
					discountRow = uint(Decimal((float64(product.SkuSelect.Options[optionKey].Skus[skuKey].Price) / float64(product.SkuSelect.Options[optionKey].Skus[skuKey].OriginPrice)) * 1000))
					discountRowRatio = uint(Decimal((float64(product.SkuSelect.Options[optionKey].Skus[skuKey].Price) / float64(product.SkuSelect.Options[optionKey].Skus[skuKey].OriginPrice)) * 10000))
				} else {
					discountRow = 0
					discountRowRatio = 0
				}
				product.SkuSelect.Options[optionKey].Skus[skuKey].MinDiscount = discountRow
				product.SkuSelect.Options[optionKey].Skus[skuKey].MaxDiscount = discountRow
				product.SkuSelect.Options[optionKey].Skus[skuKey].MinDiscountRatio = discountRowRatio
				product.SkuSelect.Options[optionKey].Skus[skuKey].MaxDiscountRatio = discountRowRatio
			}
		}
		for itemKey, sku := range product.Skus {
			product.Skus[itemKey].NormalPrice = sku.Price
			if minNormalPrice == 0 || product.Skus[itemKey].NormalPrice <= minNormalPrice {
				minNormalPrice = product.Skus[itemKey].NormalPrice
			}
			if product.Skus[itemKey].NormalPrice > maxNormalPrice {
				maxNormalPrice = product.Skus[itemKey].NormalPrice
			}
			if product.UserPriceSwitch == 1 {
				var calculateRes bool
				product.Skus[itemKey].Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Skus[itemKey].Price, user.LevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					if isDefault == 0 {
						err, product.Skus[itemKey].Price = levelModel.GetLevelDiscountAmount(product.Skus[itemKey].Price, product.Skus[itemKey].ExecPrice, discount)
						if err != nil {
							return
						}
					}
				}
			} else {
				if isDefault == 0 {
					err, product.Skus[itemKey].Price = levelModel.GetLevelDiscountAmount(product.Skus[itemKey].Price, product.Skus[itemKey].ExecPrice, discount)
					if err != nil {
						return
					}
				}
			}

			if minPrice == 0 || product.Skus[itemKey].Price <= minPrice {
				minPrice = product.Skus[itemKey].Price
			}
			if product.Skus[itemKey].Price > maxPrice {
				maxPrice = product.Skus[itemKey].Price
			}
			if product.Skus[itemKey].OriginPrice > maxOriginPrice {
				maxOriginPrice = product.Skus[itemKey].OriginPrice
			}
			if product.Skus[itemKey].GuidePrice > maxGuidePrice {
				maxGuidePrice = product.Skus[itemKey].GuidePrice
			}
			if product.Skus[itemKey].ActivityPrice > maxActivityPrice {
				maxActivityPrice = product.Skus[itemKey].ActivityPrice
			}
			if minOriginPrice == 0 || product.Skus[itemKey].OriginPrice <= minOriginPrice {
				minOriginPrice = product.Skus[itemKey].OriginPrice
			}

			// 利润 = (市场价 - 批发价)
			// 利润率 = 利润 / 批发价
			var profit uint
			if product.Skus[itemKey].OriginPrice > product.Skus[itemKey].Price && product.Skus[itemKey].Price > 0 && product.Skus[itemKey].OriginPrice > 0 {
				profit = product.Skus[itemKey].OriginPrice - product.Skus[itemKey].Price
			} else {
				profit = 0
			}
			// 利润率 2000分 / 10000分 = 0.2 * 1000 = 2000 前端 / 100 = 20% = 0.2 * 100 = 20%
			var profitRate float64
			if profit > 0 && product.Skus[itemKey].Price > 0 {
				profitRate = Decimal((float64(profit) / float64(product.Skus[itemKey].Price)) * 100)
			} else {
				profitRate = 0
			}
			product.Skus[itemKey].ProfitRate = profitRate
			// productElasticSearch.MarketPrice = product.OriginPrice
			// productElasticSearch.Level1Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level1Price
			// productElasticSearch.Level1ProfitRate = Decimal((float64(productElasticSearch.Level1Profit) / float64(productElasticSearch.Level1Price)))

			// 毛利率 = 利润 / 市场价
			// 毛利率 2000分 / 10000分 = 0.2 * 1000 = 2000 前端 / 100 = 20% = 0.2 * 100 = 20%
			var grossProfitRate float64
			if profit > 0 && product.Skus[itemKey].OriginPrice > 0 {
				grossProfitRate = Decimal((float64(profit) / float64(product.Skus[itemKey].OriginPrice)) * 100)
			} else {
				grossProfitRate = 0
			}
			product.Skus[itemKey].GrossProfitRate = grossProfitRate

			// 最大利润和最小利润
			if product.MinProfitRate == 0 || profitRate <= product.MinProfitRate {
				product.MinProfitRate = profitRate
			}
			if profitRate > product.MaxProfitRate {
				product.MaxProfitRate = profitRate
			}
			if product.MinGrossProfitRate == 0 || grossProfitRate <= product.MinGrossProfitRate {
				product.MinGrossProfitRate = grossProfitRate
			}
			if grossProfitRate > product.MaxGrossProfitRate {
				product.MaxGrossProfitRate = grossProfitRate
			}

			// 计算折扣
			var discountRow, discountRowRatio uint
			if product.Skus[itemKey].Price > 0 && product.Skus[itemKey].OriginPrice > 0 {
				discountRow = uint(Decimal((float64(product.Skus[itemKey].Price) / float64(product.Skus[itemKey].OriginPrice)) * 1000))
				discountRowRatio = uint(Decimal((float64(product.Skus[itemKey].Price) / float64(product.Skus[itemKey].OriginPrice)) * 10000))
			} else {
				discountRow = 0
				discountRowRatio = 0
			}
			product.Skus[itemKey].MinDiscount = discountRow
			product.Skus[itemKey].MaxDiscount = discountRow
			product.Skus[itemKey].MinDiscountRatio = discountRowRatio
			product.Skus[itemKey].MaxDiscountRatio = discountRowRatio
			// 最大折扣和最小折扣
			if minDiscount == 0 || discountRow <= minDiscount {
				minDiscount = discountRow
			}
			if discountRow > maxDiscount {
				maxDiscount = discountRow
			}
			if minDiscountRatio == 0 || discountRowRatio <= minDiscountRatio {
				minDiscountRatio = discountRowRatio
			}
			if discountRowRatio > maxDiscountRatio {
				maxDiscountRatio = discountRowRatio
			}
		}
		product.MaxOriginPrice = maxOriginPrice
		product.MinOriginPrice = minOriginPrice
		product.MaxGuidePrice = maxGuidePrice
		product.MaxActivityPrice = maxActivityPrice
		product.MinPrice = minPrice
		product.MaxPrice = maxPrice
		product.MaxNormalPrice = maxNormalPrice
		product.MinNormalPrice = minNormalPrice
		// 折扣
		product.MinDiscount = minDiscount
		product.MaxDiscount = maxDiscount
		product.MinDiscountRatio = minDiscountRatio
		product.MaxDiscountRatio = maxDiscountRatio
	}

	return
}

func GetSupplierData(supplier response.Supplier) (err error, supplierNew response.Supplier) {
	var goodsCount int64
	err = source.DB().Model(&response.Product{}).Where("supplier_id = ?", supplier.ID).Where("freeze = 0").Count(&goodsCount).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	supplier.GoodsCount = int(goodsCount)

	if supplier.GoodsCount > 0 {
		err = source.DB().Model(&response.Product{}).Select("SUM(sales) as hot_sale").Where("supplier_id = ?", supplier.ID).Where("freeze = 0").Pluck("hot_sale", &supplier.HotSale).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
	}

	var commentCounts int64
	err = source.DB().Model(&Comment{}).Where("supplier_id = ?", supplier.ID).Count(&commentCounts).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	if commentCounts > 0 {
		var comment Comment
		err = source.DB().Model(&Comment{}).Select("AVG(des_level) as describe_score,AVG(shop_level) as service_score,AVG(express_level) as shopping_score").Where("supplier_id = ?", supplier.ID).First(&comment).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		supplier.DescribeScore = strconv.FormatFloat(comment.DescribeScore, 'f', 1, 64)
		supplier.ServiceScore = strconv.FormatFloat(comment.ServiceScore, 'f', 1, 64)
		supplier.ShoppingScore = strconv.FormatFloat(comment.ShoppingScore, 'f', 1, 64)
	}

	return err, supplier
}

type Comment struct {
	ID            uint    `json:"id"`
	DescribeScore float64 `json:"describe_score"` // 描述相符（1位小数）
	ServiceScore  float64 `json:"service_score"`  // 卖家服务（1位小数）
	ShoppingScore float64 `json:"shopping_score"` // 物流服务（1位小数）
}

func (c Comment) TableName() string {
	return "comments"
}
func GetProductCommentLevel(id uint) (err error, level float64, desLevel float64, shopLevel float64, expressLevel float64) {
	err = source.DB().Model(&Comment{}).Where("product_id = ?", id).Pluck("AVG(level)", &level).Error
	err = source.DB().Model(&Comment{}).Where("product_id = ?", id).Pluck("AVG(des_level)", &desLevel).Error
	err = source.DB().Model(&Comment{}).Where("product_id = ?", id).Pluck("AVG(shop_level)", &shopLevel).Error
	err = source.DB().Model(&Comment{}).Where("product_id = ?", id).Pluck("AVG(express_level)", &expressLevel).Error
	return
}

func SetAttributeStatus(columnStatus request.ColumnStatus) (err error, enable int) {
	var product model.Product
	enable = 1
	err = source.DB().Model(&product).Where("`id` = ?", columnStatus.ID).First(&product).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return err, enable
	} else {
		if columnStatus.Column == "is_hot" {
			if product.IsHot == 1 {
				enable = 0
			}
		}
		if columnStatus.Column == "is_recommend" {
			if product.IsRecommend == 1 {
				enable = 0
			}
		}
		if columnStatus.Column == "is_display" {

			if product.SupplierID != 0 {
				var productVerify ProductVerify
				err = source.DB().Where("product_id = ?", product.ID).Where("supplier_id = ?", product.SupplierID).Order("created_at desc").First(&productVerify).Error
				if err == nil && (productVerify.Status == 0 || productVerify.IsDisplay == 0) {
					err = errors.New("商品还未审核或审核被驳回，请审核通过以后再进行上下架操作")
					return
				}
			}
			if product.IsDisplay == 1 {
				enable = 0
			}

		}
		if columnStatus.Column == "is_new" {
			if product.IsNew == 1 {
				enable = 0
			}
		}
		if columnStatus.Column == "is_promotion" {
			if product.IsPromotion == 1 {
				enable = 0
			}
		}
		if columnStatus.Column == "status_lock" {
			if product.StatusLock == 1 {
				enable = 0
			}
		}

		err = source.DB().Model(&product).Update(columnStatus.Column, enable).Error

		//var verify ProductVerify
		//if err = source.DB().Where("product_id = ?", product.ID).Where("status = 0").First(&verify).Error;err == nil{
		//	err = source.DB().Where("product_id = ?", product.ID).Where("status = 0").Updates(model.ProductVerify{IsDisplay: enable, Status: 1}).Error
		//	if err != nil {
		//		return
		//	}
		//}

		if columnStatus.Column == "is_display" {
			if product.IsDisplay == 0 {
				err = mq.PublishMessage(product.ID, mq.Undercarriage, 0)
			} else {
				err = mq.PublishMessage(product.ID, mq.OnSale, 0)
			}
		} else {
			err = mq.PublishMessage(product.ID, mq.Edit, 0)
		}
	}
	return err, enable
}

func GetSupplierOptionList() (err error, list []model.Supplier) {
	err = source.DB().Where("`deleted_at` is null").Find(&list).Error
	return err, list
}

func GetSearchResult(searchResult *elastic.SearchResult) (productSearch []ProductElasticSearch, err error) {

	if searchResult.Hits.TotalHits.Value > 0 {

		for _, hit := range searchResult.Hits.Hits {

			var t ProductElasticSearch
			err := json.Unmarshal(hit.Source, &t)
			if err != nil {
				// Deserialization failed
			}
			productSearch = append(productSearch, t)
		}
	}
	return
}

type Product struct {
	response.Product
	RecommendBrandStr    string `json:"recommend_brand_str" gorm:"-"`
	RecommendCategoryStr string `json:"recommend_category_str" gorm:"-"`
	AgreementPrice       uint   `json:"agreement_price" gorm:"-"` //协议价
	GuidePrice           uint   `json:"guide_price" gorm:"-"`     //指导价
	MinBuyQty            uint   `json:"min_buy_qty"`              // 最少起订量
	ActivityPrice        uint   `json:"activity_price" gorm:"-"`  //营销价
	MarketPrice          uint   `json:"market_price" gorm:"-"`    //市场价
	SalePrice            uint   `json:"sale_price" gorm:"-"`      //销售价
	CostPrice            uint   `json:"cost_price" gorm:"-"`      //成本价
	PromotionRate        string `json:"origin_rate" gorm:"-"`     //常规利润率
	ActivityRate         string `json:"activity_rate" gorm:"-"`   //营销利润率
	IsDisplay            uint   `json:"is_display"`
}
type GatherSupplyApplicationLevel struct {
	source.Model
	GatherSupplyID     uint `json:"gather_supply_id"`
	ApplicationLevelID uint `json:"application_level_id"`
	Source             uint `json:"source"`
}

type GatherSupply struct {
	source.Model
	CategoryID uint `json:"category_id" form:"category_id" gorm:"column:category_id;default:0;"`
}

func getSupplyIDs() (err error, supplyIDs []uint) {
	var categoryIDs []uint
	categoryIDs = append(categoryIDs, 98)
	categoryIDs = append(categoryIDs, 111)
	err = source.DB().Model(&GatherSupply{}).Unscoped().Where("category_id in ?", categoryIDs).Pluck("id", &supplyIDs).Error
	return
}

func GetSupplyIDs() (err error, supplyIDs []uint) {
	err, supplyIDs = getSupplyIDs()
	return
}

type ApplicationSetting struct {
	model3.SysSetting
	Value ApplicationValue `json:"value"`
}

func (i ApplicationSetting) TableName() string {
	return "sys_settings"
}

type ApplicationValue struct {
	IsOpenApply      int    `json:"is_open_apply"`     // 是否开启前台申请应用
	ApplyDesc        string `json:"apply_desc"`        // 申请说明
	IsOpenAgreement  int    `json:"is_open_agreement"` // 是否开启申请协议
	Agreement        string `json:"agreement"`
	EditPrice        int    `json:"edit_price"`         //0可以改价 1不可以改价
	ShowTechFee      int    `json:"show_tech_fee"`      //0隐藏 1显示
	MultiPetSupplier int    `json:"multi_pet_supplier"` //0单个 1多个
}

func (value ApplicationValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *ApplicationValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}
func GetApplicationSetting() (err error, sysSetting ApplicationSetting) {

	err = source.DB().Where("`key` = ?", "application_setting").First(&sysSetting).Error

	return
}

type ApplicationPetSupplier struct {
	ID            uint `json:"id" form:"id" gorm:"primarykey"`
	ApplicationID uint `json:"application_id" form:"application_id"`
	PetSupplierID uint `json:"pet_supplier_id" form:"pet_supplier_id"`
}
type Users struct {
	source.Model
	ThousandsPricesID uint            `json:"thousands_prices_id"`
	ThousandsPrices   ThousandsPrices `json:"thousands_prices"`
	LevelID           uint            `json:"level_id"`
}
type ThousandsPrices struct {
	source.Model
	Name                    string                    `json:"name"`
	Status                  int                       `json:"status"`
	IsFee                   int                       `json:"is_fee"`
	UnifyRatio              uint                      `json:"unify_ratio"`
	GuideRatio              uint                      `json:"guide_ratio" form:"guide_ratio"`
	OriginRatio             uint                      `json:"origin_ratio" form:"origin_ratio"`
	Products                []model.Product           `json:"products" gorm:"many2many:thousands_prices_products;"`
	ThousandsPricesProducts []ThousandsPricesProducts `json:"thousands_prices_products"`
	FilterImport            int                       `json:"filter_import" gorm:"column:filter_import;default:1"` //是否过滤导入商品，0不过滤 1过滤

}
type ThousandsPricesProducts struct {
	source.Model
	ThousandsPricesID   uint `json:"thousands_prices_id"`
	ProductID           uint `json:"product_id"`
	SkuID               uint `json:"sku_id"`
	Price               uint `json:"price"`
	SkuPrice            uint `json:"sku_price"`
	GuidePrice          uint `json:"guide_price"`
	SkuGuidePrice       uint `json:"sku_guide_price"`
	OriginPrice         uint `json:"origin_price"`
	SkuOriginPrice      uint `json:"sku_origin_price"`
	Strategy            uint `json:"strategy"`              //定价策略方式 1统一定价策略  2供应商定价策略  3分类
	StrategyRatio       uint `json:"strategy_ratio"`        //定价系数
	GuideStrategyRatio  uint `json:"guide_strategy_ratio"`  //定价系数
	OriginStrategyRatio uint `json:"origin_strategy_ratio"` //定价系数
}

func GetGuangdianProductStorageBoolQ(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (err error, boolQuery *elastic.BoolQuery) {

	boolQ := elastic.NewBoolQuery()

	//db := source.DB().Model(&model.Product{})
	//db = db.Where("deleted_at is NULL")
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		//boolQ.Must(elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*"))
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
	}

	if info.SupplierName != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		boolQ.Must(elastic.NewMatchPhraseQuery("supplier_name", info.SupplierName).Slop(2))
	}

	if info.IsImport != nil {
		if *info.IsImport == 1 {
			boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		} else {
			boolQ.MustNot(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		}

	}

	filterQ := elastic.NewBoolQuery()
	if appLevelId > 0 {
		var ids []uint
		err = source.DB().Model(&GatherSupplyApplicationLevel{}).Where("application_level_id = ?", appLevelId).Pluck("gather_supply_id", &ids).Error
		if err != nil {
			return
		}
		var sourceIds []uint
		err = source.DB().Model(&GatherSupplyApplicationLevel{}).Where("application_level_id = ?", appLevelId).Where("gather_supply_id in ?", ids).Pluck("source_id", &sourceIds).Error
		if err != nil {
			return
		}
		ids = append(ids, 0)
		status := make([]interface{}, len(ids))
		for index, value := range ids {
			status[index] = value
		}

		filterQ.Must(elastic.NewTermsQuery("gather_supplier_id", status...))
		//中台和云仓的商品不加限制
		sourceIds = append(sourceIds, 0)
		sourceIds = append(sourceIds, 98)
		statusS := make([]interface{}, len(sourceIds))
		for index, value := range sourceIds {
			statusS[index] = value
		}

		filterQ.Must(elastic.NewTermsQuery("source", statusS...))
		log.Log().Info("GetProductStorageInfoList ", zap.Any("info", sourceIds))
	}

	//千人千价 只查询指定商品
	var user Users
	err = source.DB().Preload("ThousandsPrices.Products").Preload("ThousandsPrices.ThousandsPricesProducts").First(&user, userID).Error
	if err != nil {
		return
	}
	var thousandsProductIds []uint
	if len(user.ThousandsPrices.Products) > 0 && user.ThousandsPrices.Status == 1 && user.ThousandsPrices.FilterImport == 1 {
		for _, product := range user.ThousandsPrices.Products {
			thousandsProductIds = append(thousandsProductIds, product.ID)
		}
	}
	if len(thousandsProductIds) > 0 {
		thousandsStatus := make([]interface{}, len(thousandsProductIds))
		for indext, valuet := range thousandsProductIds {
			thousandsStatus[indext] = valuet
		}
		filterQ.Must(elastic.NewTermsQuery("id", thousandsStatus...))
	}

	goodsIds := make([]interface{}, len(info.GoodsIds))
	for index, value := range info.GoodsIds {
		goodsIds[index] = value
	}
	if len(info.GoodsIds) > 0 {
		filterQ.Must(elastic.NewTermsQuery("id", goodsIds...))
	}

	filterQ.MustNot(elastic.NewMatchQuery("source", 109))

	var applicationSetting ApplicationSetting
	err, applicationSetting = GetApplicationSetting()
	if err != nil {
		return
	}
	if applicationSetting.Value.MultiPetSupplier == 1 {
		var petSupplierIDs []uint
		err = source.DB().Model(&ApplicationPetSupplier{}).Where("application_id = ?", info.AppID).Pluck("pet_supplier_id", &petSupplierIDs).Error
		if err != nil {
			return
		}
		if len(petSupplierIDs) > 0 {
			petSuppliers := make([]interface{}, len(petSupplierIDs))
			for index, value := range petSupplierIDs {
				petSuppliers[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("supplier_id", petSuppliers...))

		} else if info.SupplierID != nil {

			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))

		}
	} else {
		if PetSupplierID != 0 {
			filterQ.Must(elastic.NewMatchQuery("supplier_id", PetSupplierID))

		} else if info.SupplierID != nil {

			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))

			//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		}
	}

	var banSupplierIds []uint
	err = source.DB().Model(response.Supplier{}).Where("is_storage = 1").Pluck("id", &banSupplierIds).Error
	if err != nil {
		return
	}
	if len(banSupplierIds) > 0 {
		banSuppliers := make([]interface{}, len(banSupplierIds))
		for index, value := range banSupplierIds {
			banSuppliers[index] = value
		}
		filterQ.MustNot(elastic.NewTermsQuery("supplier_id", banSuppliers...))
	}
	if info.Category1ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}
	if info.Source != nil {
		filterQ.Must(elastic.NewMatchQuery("source", &info.Source))
	} else {
		//filterQ.MustNot(elastic.NewMatchQuery("source", 99))
	}
	if info.IsTaxLogo != nil {
		// 等于3的时候不用查询，没有‘未设置’状态（韦平原型设计的有‘未设置’状态）
		if *info.IsTaxLogo == 1 || *info.IsTaxLogo == 2 {
			filterQ.Must(elastic.NewMatchQuery("is_tax_logo", info.IsTaxLogo))
		}
	}
	if info.TaxRate != nil {
		orQuery := elastic.NewBoolQuery().Should(
			elastic.NewMatchQuery("tax_rate", info.TaxRate),
			elastic.NewTermQuery("sku_tax_rates", info.TaxRate),
		)
		filterQ.Must(orQuery)
	}
	if info.AlbumId != nil {
		filterQ.Must(elastic.NewTermQuery("album_ids", info.AlbumId))
	}
	if info.CollectId != nil {
		// 查询商品专辑
		var collectionModel model.CollectionModel
		source.DB().Where("id = ?", info.CollectId).First(&collectionModel)

		switch collectionModel.Type {
		// 根据商品专辑设置 筛选绑定商品
		case 1:
			filterQ.Must(elastic.NewTermQuery("collect_ids", info.CollectId))
		// 根据商品专辑设置 按分类筛选商品
		case 2:
			// 将 Category1ID 转换为 []interface{}
			category1ID := make([]interface{}, len(collectionModel.Filter.Category1ID))
			for i, id := range collectionModel.Filter.Category1ID {
				category1ID[i] = id
			}
			category2ID := make([]interface{}, len(collectionModel.Filter.Category2ID))
			for i, id := range collectionModel.Filter.Category2ID {
				category2ID[i] = id
			}
			category3ID := make([]interface{}, len(collectionModel.Filter.Category3ID))
			for i, id := range collectionModel.Filter.Category3ID {
				category3ID[i] = id
			}
			filterQ.Must(elastic.NewTermsQuery("category_1_id", category1ID...))
			filterQ.Must(elastic.NewTermsQuery("category_2_id", category2ID...))
			filterQ.Must(elastic.NewTermsQuery("category_3_id", category3ID...))
		// 根据商品专辑设置 按营销属性筛选商品
		case 3:
			switch collectionModel.Filter.AttributeType {
			case 1:
				filterQ.Must(elastic.NewMatchQuery("is_hot", 1))
			//case 2:
			//	filterQ.Must(elastic.NewMatchQuery("is_recommend", 1))
			case 3:
				filterQ.Must(elastic.NewMatchQuery("is_new", 1))
			case 4:
				filterQ.Must(elastic.NewMatchQuery("is_promotion", 1))
			}
		// 根据商品专辑设置 按商品数据筛选商品
		case 4:
		}
	}

	//if info.PluginId != nil {
	//	filterQ.Must(elastic.NewMatchQuery("plugin_id", info.PluginId))
	//}
	filterQ.Must(elastic.NewMatchQuery("source", 132))

	if info.IsRecommend != nil {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", &info.IsRecommend))
	}
	if info.IsBill != nil {
		filterQ.Must(elastic.NewMatchQuery("is_bill", &info.IsBill))
	}
	if info.IsNew != nil {
		filterQ.Must(elastic.NewMatchQuery("is_new", &info.IsNew))
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewMatchQuery("is_hot", &info.IsHot))
	}
	if info.IsPromotion != nil {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", &info.IsPromotion))
	}
	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
	}

	if info.FreightType == 2 {
		filterQ.MustNot(elastic.NewMatchQuery("freight_type", 3))
	}
	if info.FreightType == 1 {
		filterQ.Must(elastic.NewMatchQuery("freight_type", 3))
	}
	if info.AgreementPrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(*info.AgreementPrice.From * 100))
	}
	if info.AgreementPrice.To != nil && *info.AgreementPrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(*info.AgreementPrice.To * 100))
	}
	if info.ActivityPrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("activity_price").Gte(*info.ActivityPrice.From * 100))
	}
	if info.ActivityPrice.To != nil && *info.ActivityPrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("activity_price").Lte(*info.ActivityPrice.To * 100))
	}
	if info.GuidePrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("guide_price").Gte(*info.GuidePrice.From * 100))
	}
	if info.GuidePrice.To != nil && *info.GuidePrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("guide_price").Lte(*info.GuidePrice.To * 100))
	}
	var grossRateColumn string
	var marketRateColumn string
	switch userLevel {
	case 1:
		grossRateColumn = "level_1_gross_rate"
		marketRateColumn = "level_1_profit_rate"
		break
	case 2:
		grossRateColumn = "level_2_gross_rate"
		marketRateColumn = "level_2_profit_rate"
		break
	case 3:
		grossRateColumn = "level_3_gross_rate"
		marketRateColumn = "level_3_profit_rate"
		break
	case 4:
		grossRateColumn = "level_4_gross_rate"
		marketRateColumn = "level_4_profit_rate"
		break
	case 5:
		grossRateColumn = "level_5_gross_rate"
		marketRateColumn = "level_5_profit_rate"
		break
	case 6:
		grossRateColumn = "level_6_gross_rate"
		marketRateColumn = "level_6_profit_rate"
		break
	case 7:
		grossRateColumn = "level_7_gross_rate"
		marketRateColumn = "level_7_profit_rate"
		break
	case 8:
		grossRateColumn = "level_8_gross_rate"
		marketRateColumn = "level_8_profit_rate"
		break
	case 9:
		grossRateColumn = "level_9_gross_rate"
		marketRateColumn = "level_9_profit_rate"
		break
	case 10:
		grossRateColumn = "level_10_gross_rate"
		marketRateColumn = "level_10_profit_rate"
		break
	default:
		grossRateColumn = "gross_profit_rate"
		marketRateColumn = "market_rate"
		break
	}
	if info.ProfitRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("profit_rate").Gte(*info.ProfitRate.From))
	}
	if info.ProfitRate.To != nil && *info.ProfitRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("profit_rate").Lte(*info.ProfitRate.To))
	}

	if info.GrossProfitRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery(grossRateColumn).Gte(*info.GrossProfitRate.From))
	}
	if info.GrossProfitRate.To != nil && *info.GrossProfitRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery(grossRateColumn).Lte(*info.GrossProfitRate.To))
	}

	if info.MarketRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery(marketRateColumn).Gte(*info.MarketRate.From))
	}
	if info.MarketRate.To != nil && *info.MarketRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery(marketRateColumn).Lte(*info.MarketRate.To))
	}

	if info.Profit.From != nil {
		filterQ.Must(elastic.NewRangeQuery("profit").Gte(*info.Profit.From * 100))
	}
	if info.Profit.To != nil && *info.Profit.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("profit").Lte(*info.Profit.To * 100))
	}

	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("origin_rate").Gte(float64(*info.OriginRate.From)))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("origin_rate").Lte(float64(*info.OriginRate.To)))
	}
	if info.ActivityRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("activity_rate").Gte(float64(*info.ActivityRate.From)))
	}
	if info.ActivityRate.To != nil && *info.ActivityRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("activity_rate").Lte(float64(*info.ActivityRate.To)))

	}
	boolQ.Filter(filterQ)
	return err, boolQ
}

func GetCakeProductStorageBoolQ(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (err error, boolQuery *elastic.BoolQuery) {

	boolQ := elastic.NewBoolQuery()

	//db := source.DB().Model(&model.Product{})
	//db = db.Where("deleted_at is NULL")
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		//boolQ.Must(elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*"))
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
	}

	if info.IsImport != nil {
		if *info.IsImport == 1 {
			boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		} else {
			boolQ.MustNot(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		}

	}

	filterQ := elastic.NewBoolQuery()

	goodsIds := make([]interface{}, len(info.GoodsIds))
	for index, value := range info.GoodsIds {
		goodsIds[index] = value
	}
	if len(info.GoodsIds) > 0 {
		filterQ.Must(elastic.NewTermsQuery("id", goodsIds...))
	}

	filterQ.Must(elastic.NewMatchQuery("source", 110))
	filterQ.Must(elastic.NewMatchQuery("location_id", 5)) //只拉取零食蛋糕商品

	if info.IsRecommend != nil {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", &info.IsRecommend))
	}
	if info.IsBill != nil {
		filterQ.Must(elastic.NewMatchQuery("is_bill", &info.IsBill))
	}
	if info.IsNew != nil {
		filterQ.Must(elastic.NewMatchQuery("is_new", &info.IsNew))
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewMatchQuery("is_hot", &info.IsHot))
	}
	if info.IsPromotion != nil {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", &info.IsPromotion))
	}
	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
	}

	if info.AgreementPrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(*info.AgreementPrice.From * 100))
	}
	if info.AgreementPrice.To != nil && *info.AgreementPrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(*info.AgreementPrice.To * 100))
	}
	if info.ActivityPrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("activity_price").Gte(*info.ActivityPrice.From * 100))
	}
	if info.ActivityPrice.To != nil && *info.ActivityPrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("activity_price").Lte(*info.ActivityPrice.To * 100))
	}
	if info.GuidePrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("guide_price").Gte(*info.GuidePrice.From * 100))
	}
	if info.GuidePrice.To != nil && *info.GuidePrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("guide_price").Lte(*info.GuidePrice.To * 100))
	}
	var grossRateColumn string
	var marketRateColumn string
	switch userLevel {
	case 1:
		grossRateColumn = "level_1_gross_rate"
		marketRateColumn = "level_1_profit_rate"
		break
	case 2:
		grossRateColumn = "level_2_gross_rate"
		marketRateColumn = "level_2_profit_rate"
		break
	case 3:
		grossRateColumn = "level_3_gross_rate"
		marketRateColumn = "level_3_profit_rate"
		break
	case 4:
		grossRateColumn = "level_4_gross_rate"
		marketRateColumn = "level_4_profit_rate"
		break
	case 5:
		grossRateColumn = "level_5_gross_rate"
		marketRateColumn = "level_5_profit_rate"
		break
	case 6:
		grossRateColumn = "level_6_gross_rate"
		marketRateColumn = "level_6_profit_rate"
		break
	case 7:
		grossRateColumn = "level_7_gross_rate"
		marketRateColumn = "level_7_profit_rate"
		break
	case 8:
		grossRateColumn = "level_8_gross_rate"
		marketRateColumn = "level_8_profit_rate"
		break
	case 9:
		grossRateColumn = "level_9_gross_rate"
		marketRateColumn = "level_9_profit_rate"
		break
	case 10:
		grossRateColumn = "level_10_gross_rate"
		marketRateColumn = "level_10_profit_rate"
		break
	default:
		grossRateColumn = "gross_profit_rate"
		marketRateColumn = "market_rate"
		break
	}
	if info.ProfitRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("profit_rate").Gte(*info.ProfitRate.From))
	}
	if info.ProfitRate.To != nil && *info.ProfitRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("profit_rate").Lte(*info.ProfitRate.To))
	}

	if info.GrossProfitRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery(grossRateColumn).Gte(*info.GrossProfitRate.From))
	}
	if info.GrossProfitRate.To != nil && *info.GrossProfitRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery(grossRateColumn).Lte(*info.GrossProfitRate.To))
	}

	if info.MarketRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery(marketRateColumn).Gte(*info.MarketRate.From))
	}
	if info.MarketRate.To != nil && *info.MarketRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery(marketRateColumn).Lte(*info.MarketRate.To))
	}

	if info.Profit.From != nil {
		filterQ.Must(elastic.NewRangeQuery("profit").Gte(*info.Profit.From * 100))
	}
	if info.Profit.To != nil && *info.Profit.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("profit").Lte(*info.Profit.To * 100))
	}

	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("origin_rate").Gte(float64(*info.OriginRate.From)))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("origin_rate").Lte(float64(*info.OriginRate.To)))
	}
	if info.ActivityRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("activity_rate").Gte(float64(*info.ActivityRate.From)))
	}
	if info.ActivityRate.To != nil && *info.ActivityRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("activity_rate").Lte(float64(*info.ActivityRate.To)))

	}
	boolQ.Filter(filterQ)
	return err, boolQ
}

func GetMyCenterTotal(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (total int64) {
	info = request.ProductStorageSearch{AppID: info.AppID, IsDisplay: info.IsDisplay}
	var err error
	// 创建db
	var es *elastic.Client
	es, err = source.ES()
	if err != nil {
		return
	}
	//获取boolQ
	var boolQ *elastic.BoolQuery
	err, boolQ = GetProductStorageBoolQ(info, appLevelId, PetSupplierID, userID, userLevel)
	if err != nil {
		return
	}
	boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))

	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	return
}

// GetProductInfoList
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProductInfoList
// @description: 分页获取Product完整记录列表
// @param: info request.ProductSearch
// @return: err error, list []model.Product, total int64
func GetProductStorageInfoList(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (err error, list interface{}, total int64) {
	log.Log().Info("请求数据", zap.Any("params", info))
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	if info.Type == "" {
		info.Type = "sort"
	}

	// 创建db
	var es *elastic.Client
	if es, err = source.ES(); err != nil {
		return
	}

	//获取boolQ
	var boolQ *elastic.BoolQuery
	err, boolQ = GetProductStorageBoolQ(info, appLevelId, PetSupplierID, userID, userLevel)
	if err != nil {
		return
	}

	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	//es执行搜索
	log.Log().Info("导入数据的详情", zap.Any("info", boolQ))

	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	//log.Log().Info("导入数据的详情", zap.Any("info", res))
	if err != nil {
		return
	}
	//var ids []uint
	//var productSearchs []ProductElasticSearch
	//获取es搜索结果
	var listAll []ProductElasticSearch
	listAll, err = GetSearchResult(res)
	//for _,v := range productSearchs {
	//	ids = append(ids, v.ID)
	//}
	//db = db.Where("id in ?", ids)
	//err = db.Find(&list).Error
	//for k,product := range list {
	//	var productNewData ProductElasticSearch
	//	err,productNewData = HandleData(product)
	//}
	var listCut []ProductElasticSearchCut
	err, listCut = ProductTransAppPriceExclusive(listAll, userID, appLevelId, info.AppID, 1)

	return err, listCut, total
}

func GetGuangdianProductStorageInfoList(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (err error, list interface{}, total int64) {
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	if info.Type == "" {
		info.Type = "sort"
	}

	// 创建db
	var es *elastic.Client
	if es, err = source.ES(); err != nil {
		return
	}

	//获取boolQ
	var boolQ *elastic.BoolQuery
	err, boolQ = GetGuangdianProductStorageBoolQ(info, appLevelId, PetSupplierID, userID, userLevel)
	if err != nil {
		return
	}

	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	//es执行搜索
	log.Log().Info("GetGuangdianProductStorageInfoList导入数据的详情", zap.Any("info", boolQ))

	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	//log.Log().Info("导入数据的详情", zap.Any("info", res))
	if err != nil {
		return
	}
	//var ids []uint
	//var productSearchs []ProductElasticSearch
	//获取es搜索结果
	var listAll []ProductElasticSearch
	listAll, err = GetSearchResult(res)
	//for _,v := range productSearchs {
	//	ids = append(ids, v.ID)
	//}
	//db = db.Where("id in ?", ids)
	//err = db.Find(&list).Error
	//for k,product := range list {
	//	var productNewData ProductElasticSearch
	//	err,productNewData = HandleData(product)
	//}
	var listCut []ProductElasticSearchCut
	err, listCut = ProductTransAppPrice(listAll, userID, appLevelId, info.AppID, 1)

	return err, listCut, total
}

func GetCakeProductStorageInfoList(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (err error, list interface{}, total int64) {
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	if info.Type == "" {
		info.Type = "sort"
	}

	// 创建db
	var es *elastic.Client
	if es, err = source.ES(); err != nil {
		return
	}

	//获取boolQ
	var boolQ *elastic.BoolQuery
	err, boolQ = GetCakeProductStorageBoolQ(info, appLevelId, PetSupplierID, userID, userLevel)
	if err != nil {
		return
	}

	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	//es执行搜索
	log.Log().Info("GetcakeProductStorageInfoList导入数据的详情", zap.Any("info", boolQ))

	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	//log.Log().Info("导入数据的详情", zap.Any("info", res))
	if err != nil {
		return
	}
	//var ids []uint
	//var productSearchs []ProductElasticSearch
	//获取es搜索结果
	var listAll []ProductElasticSearch
	listAll, err = GetSearchResult(res)
	//for _,v := range productSearchs {
	//	ids = append(ids, v.ID)
	//}
	//db = db.Where("id in ?", ids)
	//err = db.Find(&list).Error
	//for k,product := range list {
	//	var productNewData ProductElasticSearch
	//	err,productNewData = HandleData(product)
	//}
	var listCut []ProductElasticSearchCut
	err, listCut = ProductTransAppPrice(listAll, userID, appLevelId, info.AppID, 1)

	return err, listCut, total
}

func GetMyProductStorageIdsList(info request.ProductStorageSearch, appID uint) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := limit * (info.Page - 1)
	var productIds []uint
	var application Application
	err = source.DB().First(&application, appID).Error
	if err != nil {
		return
	}
	if application.AppLevelID > 0 {
		var ids []uint
		err = source.DB().Model(&GatherSupplyApplicationLevel{}).Where("application_level_id = ?", application.AppLevelID).Pluck("gather_supply_id", &ids).Error
		if err != nil {
			return
		}
		var sourceIds []uint
		err = source.DB().Model(&GatherSupplyApplicationLevel{}).Where("application_level_id = ?", application.AppLevelID).Where("gather_supply_id in ?", ids).Pluck("source_id", &sourceIds).Error
		if err != nil {
			return
		}
		ids = append(ids, 0)
		status := make([]interface{}, len(ids))
		for index, value := range ids {
			status[index] = value
		}

		//中台和云仓的商品不加限制
		sourceIds = append(sourceIds, 0)
		sourceIds = append(sourceIds, 98)
		statusS := make([]interface{}, len(sourceIds))
		for index, value := range sourceIds {
			statusS[index] = value
		}

		err = source.DB().Model(&model.Storage{}).Joins("INNER join products on products.id = storages.product_id and products.is_display=1 and products.deleted_at is null and products.is_plugin=0 and products.gather_supply_id in ? and products.source in ?", status, statusS).Where("app_id = ?", appID).Count(&total).Error
		if err != nil {
			return
		}
		err = source.DB().Model(&model.Storage{}).Joins("INNER join products on products.id = storages.product_id and products.is_display=1 and products.deleted_at is null and products.is_plugin=0 and products.gather_supply_id in ? and products.source in ?", status, statusS).Where("app_id = ?", appID).Limit(limit).Offset(offset).Pluck("product_id", &productIds).Error
	} else {
		err = source.DB().Model(&model.Storage{}).Joins("INNER join products on products.id = storages.product_id and products.is_display=1 and products.deleted_at is null and products.is_plugin=0").Where("app_id = ?", appID).Count(&total).Error
		if err != nil {
			return
		}
		err = source.DB().Model(&model.Storage{}).Joins("INNER join products on products.id = storages.product_id and products.is_display=1 and products.deleted_at is null and products.is_plugin=0").Where("app_id = ?", appID).Limit(limit).Offset(offset).Pluck("product_id", &productIds).Error
	}

	return nil, productIds, total
}
func GetMyProductStorageCenterList(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel int) (err error, list interface{}, total int64) {
	log.Log().Info("请求数据", zap.Any("params", info))
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	if info.Type == "" {
		info.Type = "sort"
	}

	// 创建db
	var es *elastic.Client
	es, err = source.ES()
	if err != nil {
		return
	}
	//获取boolQ
	var boolQ *elastic.BoolQuery
	err, boolQ = GetProductStorageBoolQ(info, appLevelId, PetSupplierID, userID, userLevel)
	if err != nil {
		return
	}
	boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))

	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}

	//es执行搜索
	log.Log().Info("导入数据的详情", zap.Any("info", boolQ))

	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	//log.Log().Info("导入数据的详情", zap.Any("info", res))
	if err != nil {
		return
	}
	//var ids []uint
	//var productSearchs []ProductElasticSearch
	//获取es搜索结果
	var listAll []ProductElasticSearch
	listAll, err = GetSearchResult(res)
	//for _,v := range productSearchs {
	//	ids = append(ids, v.ID)
	//}
	//db = db.Where("id in ?", ids)
	//err = db.Find(&list).Error
	//for k,product := range list {
	//	var productNewData ProductElasticSearch
	//	err,productNewData = HandleData(product)
	//}
	var listCut []ProductElasticSearchCut
	err, listCut = ProductTransAppPrice(listAll, userID, appLevelId, info.AppID, 0)

	return err, listCut, total
}
func GetThousandsPriceStatus(tp ThousandsPrices) (status int) {
	if len(tp.ThousandsPricesProducts) > 0 && tp.Status == 1 {
		if tp.IsFee == 1 {
			status = 1
		} else {
			status = 2
		}

	}
	return
}
func GetProductStorageInfoListByFulu(info request.ProductStorageSearch) (err error, list []ProductElasticSearch, total int64) {
	log.Log().Info("请求数据", zap.Any("params", info))
	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Info("ddddddd", zap.Any("gather_supplier_id", "查询供应链失败1"))

		err = errors.New("查询供应链失败1")
		return
	}
	if supply.ID == 0 {
		log.Log().Info("ddddddd", zap.Any("gather_supplier_id", "查询供应链失败2"))

		err = errors.New("查询供应链失败2")
		return
	}
	limit := info.PageSize
	if limit > 50 {
		limit = 50
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	if info.Type == "" {
		info.Type = "sort"
	}

	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	//db := source.DB().Model(&model.Product{})
	//db = db.Where("deleted_at is NULL")
	// 如果有条件搜索 下方会自动创建搜索语句
	boolQ := elastic.NewBoolQuery()
	filterQ := elastic.NewBoolQuery()

	log.Log().Info("ddddddd", zap.Any("gather_supplier_id", supply.ID))
	filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", supply.ID))

	boolQ.Filter(filterQ)
	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	//es执行搜索
	log.Log().Info("导入数据的详情", zap.Any("info", boolQ))

	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	//log.Log().Info("导入数据的详情", zap.Any("info", res))
	if err != nil {
		return
	}
	//var ids []uint
	//var productSearchs []ProductElasticSearch
	//获取es搜索结果
	list, err = GetSearchResult(res)
	return err, list, total
}

/*
*
复制商品  copyProduct 商品id   supplierID 供应商id  isOut是否下架 1是 0跟着复制的商品走
*/
func CopyProduct(copyProduct request.CopyProduct, supplierID uint, isOut int) (err error, id uint) {
	if copyProduct.Id == 0 {
		err = errors.New("请选择要复制的商品")
		return
	}
	var product ProductForUpdate
	err = source.DB().Preload("Skus").Where("id = ?", copyProduct.Id).First(&product).Error
	if err != nil {
		err = errors.New("选择的商品不存在")
		return
	}
	product.ID = 0
	product.GatherSupplyID = 0
	product.SupplierID = supplierID
	product.Source = 0
	//如果是第三方运费 就变为统一运费 其他运费方式不变
	if product.FreightType == 2 {
		product.FreightType = 0
	}
	if isOut == 1 {
		product.IsDisplay = 0
	}
	for key, _ := range product.Skus {
		product.Skus[key].ID = 0
	}
	//直接走原创建方法 方便后续有调整
	err, id = CreateProduct(product)
	return
}

func GetProductByCursor(cursor int, pageSize int, userID uint, appLevelId uint, appID uint) (err error, list interface{}, total int64, cursorRes int) {
	if pageSize == 0 {
		pageSize = 50
	} else if pageSize > 100 {
		pageSize = 100
	}
	var products []ProductSync
	db := source.DB().Model(ProductSync{}).Where("is_plugin = 0").
		Preload("Storages").
		Preload("Skus").
		Preload("Category1").
		Preload("Category2").
		Preload("Category3").
		Preload("Brand").
		Preload("Supplier").
		Preload("GatherSupply").
		Preload("SmallShopProductSale").
		Preload("AlbumRelations").
		Preload("CollectionRelations").
		Preload("OrderItems", func(db *gorm.DB) *gorm.DB {
			return db.Joins("JOIN orders ON orders.id = order_items.order_id").Where("orders.status >= 1")
		}).
		Preload("OrderItems.Order").
		Omit("detail_images").Where("deleted_at is NULL")
	err = db.Count(&total).Error
	if cursor > 0 {
		db = db.Where("id > ?", cursor)
	}
	err = db.Limit(pageSize).Order("id ASC").Find(&products).Error
	var count = len(products)
	var productsElastic []ProductElasticSearch
	var productsElasticCut []ProductElasticSearchCut
	var userLevel []UserLevel
	var levels = make(map[int]int)
	err = source.DB().Order("level asc").Find(&userLevel).Error
	if err != nil {
		return
	}
	for k, l := range userLevel {
		levels[k] = l.Discount
	}
	if count > 0 {
		var user *model3.User
		user, err = cache.GetUserFromCache(userID)
		if err != nil {
			return
		}
		cursorRes = int(products[count-1].ID)
		for _, v := range products {
			var productElastic ProductElasticSearch
			v.UserLevelID = user.LevelID
			err, productElastic = HandleData(v)
			if err != nil {
				continue
			}
			productsElastic = append(productsElastic, productElastic)
		}

		err, productsElasticCut = ProductTransAppPriceExclusive(productsElastic, userID, appLevelId, appID, 1)
		if err != nil {
			return
		}

	}
	return err, productsElasticCut, total, cursorRes
}

func ProductTransAppPrice(productsElastic []ProductElasticSearch, userID uint, appLevelId uint, appID uint, hidePrice int) (err error, result []ProductElasticSearchCut) {
	var user User
	err = source.DB().Preload("ThousandsPrices.Products").Preload("ThousandsPrices.ThousandsPricesProducts").First(&user, userID).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	var application *model3.Application
	application, err = cache.GetApplicationFromCache(appID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			application.ApplicationLevel.ServerRadio = 0
		} else {
			return
		}
	}
	//整理供应商id一次性查询
	var supplierIds []uint
	for _, item := range productsElastic {
		if item.SupplierID > 0 {
			supplierIds = append(supplierIds, item.SupplierID)
		}
	}
	var suppliers []response.Supplier
	if len(supplierIds) > 0 {
		source.DB().Where("id in ?", supplierIds).Find(&suppliers) //供应商名称
	}
	var percent, isDefault int
	err, percent, isDefault = levelModel.GetLevelDiscountPercent(user.LevelID)
	if err != nil {
		return
	}
	for key, _ := range productsElastic {
		//对外供应商名称
		if productsElastic[key].SupplierID > 0 {
			for _, supplierItem := range suppliers {
				if supplierItem.ID == productsElastic[key].SupplierID {
					productsElastic[key].SupplierName = supplierItem.ShopName //对外名称
				}
			}
		}
		//对外供应链名称
		if productsElastic[key].GatherSupplyID > 0 {
			_, settingValue := setting.GetSetting("gatherSupply" + strconv.Itoa(int(productsElastic[key].GatherSupplyID)))
			var gettingData GettingData
			_ = json.Unmarshal([]byte(settingValue), &gettingData)
			productsElastic[key].GatherSupplyName = gettingData.BaseInfo.StoreName
		}

		productsElastic[key].NormalAgreementPrice = productsElastic[key].AgreementPrice
		var importApps []string
		importApps = strings.Split(productsElastic[key].ImportApps, " ")
		if collection.Collect(importApps).Contains(strconv.Itoa(int(appID))) == true {
			productsElastic[key].IsImport = 1
		}
		if isDefault == 0 {
			err, productsElastic[key].AgreementPrice = levelModel.GetLevelDiscountAmount(productsElastic[key].AgreementPrice, productsElastic[key].CostPrice, percent)
			if err != nil {
				return
			}
		}
		//技术服务费
		productsElastic[key].AgreementPrice = uint(Decimal(math.Floor(float64(productsElastic[key].AgreementPrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000))))
		//千人千价 价格覆盖
		if GetThousandsPriceStatus(user.ThousandsPrices) > 0 {
			for _, ttp := range user.ThousandsPrices.ThousandsPricesProducts {
				if productsElastic[key].ID == ttp.ProductID && ttp.Price > 0 {
					productsElastic[key].AgreementPrice = ttp.Price
				}

				if productsElastic[key].ID == ttp.ProductID && ttp.GuidePrice > 0 {
					productsElastic[key].GuidePrice = ttp.GuidePrice
				}
				if productsElastic[key].ID == ttp.ProductID && ttp.OriginPrice > 0 {
					productsElastic[key].MarketPrice = ttp.OriginPrice
				}
			}
		}
		if hidePrice == 1 {
			productsElastic[key].NormalAgreementPrice = productsElastic[key].AgreementPrice
		}
		if float64(productsElastic[key].MarketPrice)-float64(productsElastic[key].AgreementPrice) > 0 && float64(productsElastic[key].AgreementPrice) > 0 {
			productsElastic[key].MarketRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].AgreementPrice))
			productsElastic[key].GrossProfitRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].MarketPrice))
		} else {
			productsElastic[key].MarketRate = 0
			productsElastic[key].GrossProfitRate = 0
		}

		if float64(productsElastic[key].MarketPrice)-float64(productsElastic[key].NormalAgreementPrice) > 0 && float64(productsElastic[key].NormalAgreementPrice) > 0 {
			productsElastic[key].NormalMarketRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].NormalAgreementPrice))
			productsElastic[key].NormalGrossProfitRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].MarketPrice))
		} else {
			productsElastic[key].NormalMarketRate = 0
			productsElastic[key].NormalGrossProfitRate = 0
		}

		if productsElastic[key].AgreementPrice > 0 && productsElastic[key].GuidePrice > 0 && productsElastic[key].GuidePrice >= productsElastic[key].AgreementPrice {
			productsElastic[key].PromotionRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].AgreementPrice))
			productsElastic[key].ProfitRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].GuidePrice))
			productsElastic[key].Profit = int(productsElastic[key].GuidePrice - productsElastic[key].AgreementPrice)
			productsElastic[key].DiscountRate = Decimal((float64(productsElastic[key].AgreementPrice) / float64(productsElastic[key].MarketPrice)) * 10)

		} else {
			if productsElastic[key].AgreementPrice <= 0 {
				productsElastic[key].PromotionRate = 1
			}
			if productsElastic[key].GuidePrice <= 0 {
				productsElastic[key].PromotionRate = 0
			}
			if productsElastic[key].GuidePrice < productsElastic[key].AgreementPrice {
				productsElastic[key].PromotionRate = 0
			}
		}

		if productsElastic[key].NormalAgreementPrice > 0 && productsElastic[key].GuidePrice > 0 && productsElastic[key].GuidePrice >= productsElastic[key].NormalAgreementPrice {
			productsElastic[key].NormalPromotionRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].NormalAgreementPrice))
			productsElastic[key].NormalProfitRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].GuidePrice))
		} else {
			if productsElastic[key].NormalAgreementPrice <= 0 {
				productsElastic[key].NormalPromotionRate = 1
				productsElastic[key].NormalProfitRate = 1
			}
			if productsElastic[key].GuidePrice <= 0 {
				productsElastic[key].NormalPromotionRate = 0
				productsElastic[key].NormalProfitRate = 0
			}
			if productsElastic[key].GuidePrice < productsElastic[key].NormalAgreementPrice {
				productsElastic[key].NormalPromotionRate = 0
				productsElastic[key].NormalProfitRate = 0
			}
		}
		if productsElastic[key].ActivityPrice > 0 {
			if productsElastic[key].ActivityPrice > productsElastic[key].AgreementPrice && productsElastic[key].ActivityPrice > 0 && productsElastic[key].AgreementPrice > 0 {
				productsElastic[key].ActivityRate = Decimal((float64(productsElastic[key].ActivityPrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].AgreementPrice))
			} else {
				productsElastic[key].ActivityRate = 0
			}

			if productsElastic[key].ActivityPrice > productsElastic[key].NormalAgreementPrice && productsElastic[key].ActivityPrice > 0 && productsElastic[key].NormalAgreementPrice > 0 {
				productsElastic[key].NormalActivityRate = Decimal((float64(productsElastic[key].ActivityPrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].NormalAgreementPrice))
			} else {
				productsElastic[key].NormalActivityRate = 0
			}
			productsElastic[key].CostPrice = productsElastic[key].ActivityPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		} else {
			productsElastic[key].CostPrice = productsElastic[key].AgreementPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		}
		productsElastic[key].ImportApps = ""
		result = append(result, productsElastic[key].ProductElasticSearchCut)
	}
	return
}

func UpdateProductByExcel(link string) (err error) {
	var fullPath string
	if config.Config().Local.FullPath != "" {
		fullPath = config.Config().Local.FullPath
	} else {
		fullPath = "/data/goSupply/"
	}
	f, err := excelize.OpenFile(fullPath + link)
	if err != nil {
		return
	}
	//读取某个表单的所有数据
	rows := f.GetRows("Sheet1")

	var productMap = make(map[string][]map[string]string)
	for key, row := range rows {
		if key > 0 {
			if len(row) >= 26 {
				var skuMap = make(map[string]string)
				skuMap["skuId"] = row[27]
				skuMap["sn"] = row[13]
				skuMap["barcode"] = row[14]
				skuMap["productId"] = row[0]
				skuMap["guidePrice"] = row[8]
				skuMap["agreementPrice"] = row[9]
				skuMap["costPrice"] = row[10]
				skuMap["advicePrice"] = row[11]
				skuMap["activityPrice"] = row[12]
				skuMap["skuStock"] = row[6]
				skuMap["skuTitle"] = row[5]
				skuMap["sales"] = row[7]
				productMap[skuMap["productId"]] = append(productMap[row[0]], skuMap)
			}
		}
	}
	for _, value := range productMap {
		var valueString []byte
		valueString, err = json.Marshal(value)
		if err != nil {
			return
		}
		err = source.Redis().LPush(context.Background(), "excelUpdateProduct", string(valueString)).Err()
		if err != nil {
			return
		}
	}
	return
}
func PublishDeleteProductMqMessagesByDeletedAt() (err error) {
	go PublishMessage()
	return err
}

func PublishMessage() (err error) {
	var products []uint
	err = source.DB().Model(&model.Product{}).Unscoped().Where("deleted_at > '2024-06-01 15:39:18'").Pluck("id", &products).Error
	if err != nil {
		return
	}
	for _, product := range products {
		err = mq.PublishMessage(product, "goods.delete", 0)
		if err != nil {
			return
		}
	}
	return

}

type ProductStock struct {
	ID   uint       `json:"id"`
	Skus []SkuStock `json:"skus" gorm:"references:ID;foreignKey:ProductID;"`
}

func (ProductStock) TableName() string {
	return "products"
}

type SkuStock struct {
	ID        uint `json:"id"`
	ProductID uint `json:"product_id"`
	Stock     int  `json:"stock"`
}

func (SkuStock) TableName() string {
	return "skus"
}

func GetProductStock(ids []int) (err error, data []ProductStock) {
	if len(ids) > 100 {
		err = errors.New("最多只能获取100个商品的库存")
		return
	}
	err = source.DB().Preload("Skus").Where("id in ?", ids).Find(&data).Error
	return
}
