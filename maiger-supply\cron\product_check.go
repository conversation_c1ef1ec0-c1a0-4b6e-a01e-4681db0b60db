package cron

import (
	"go.uber.org/zap"
	"maiger-supply/component/goods"
	maigerPkgProduct "maiger-supply/maiger-pkg/product"
	"product/mq"
	productService "product/service"
	publicSupply "public-supply/model"
	"strconv"
	"sync"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 定时任务检测商品上架状态

func MaiGerProductCheckCron() {
	var gatherList []publicSupply.GatherSupply
	if err := source.DB().Where("`category_id` = ?", 131).Find(&gatherList).Error; err != nil {
		return
	}

	for _, gather := range gatherList {
		addMaiGerProductCheckCron(gather.ID)
	}
}

func addMaiGerProductCheckCron(gatherId uint) {
	task := cron.Task{
		Key:  "maiGerProductCheck" + strconv.Itoa(int(gatherId)),
		Name: "迈戈选品库自动更新" + strconv.Itoa(int(gatherId)),
		Spec: "0 0 */1 * * *",
		Handle: func(task cron.Task) {
			MaiGerProductCheck(gatherId)
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func MaiGerProductCheck(gatherId uint) {
	m := goods.MaiGerSupply{}
	if err := m.InitSetting(gatherId); err != nil {
		log.Log().Error("迈戈导入商品上架检测失败：", zap.Any("gatherId", gatherId), zap.Any("err", err))
		return
	}

	// 获取所有商品
	var products []productService.ProductForUpdate
	if err := source.DB().Where("is_display = 1 AND gather_supply_id = ?", gatherId).Find(&products).Error; err != nil {
		log.Log().Error("迈戈导入商品上架检测失败：", zap.Any("gatherId", gatherId), zap.Any("err", err))
		return
	}

	var wg sync.WaitGroup
	sem := make(chan struct{}, 100) // 控制并发数（最多100个并发）

	for _, product := range products {

		wg.Add(1)

		go func(p productService.ProductForUpdate) {
			defer wg.Done()
			sem <- struct{}{}

			defer func() { <-sem }()

			result, err := maigerPkgProduct.GoodsPriceStock(maigerPkgProduct.GoodsPriceStockParams{
				Domain:      m.Host,
				AccessToken: m.AccessToken,
				SkuIds:      []string{p.SourceGoodsIDString},
			})

			if err != nil {
				log.Log().Error("迈戈商品接口调用失败", zap.String("skuId", p.SourceGoodsIDString), zap.Error(err))
				return
			}

			if len(result) == 0 || result[0].SkuStatus != 2 {
				log.Log().Info("商品已下架，执行下架操作", zap.Uint("productId", p.ID), zap.String("skuId", p.SourceGoodsIDString))
				SetProductOffline(p.ID)
			}
		}(product)
	}

	wg.Wait()
}

func SetProductOffline(productId uint) {
	_ = source.DB().Model(&productService.ProductForUpdate{}).Where("id = ?", productId).Update("is_display", 0).Error

	_ = mq.PublishMessage(productId, mq.Undercarriage, 0)
}
