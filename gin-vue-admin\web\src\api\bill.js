import service from '@/utils/request';
/*
 *@Summary 获取发票设置
 *@Router  /bill/findTradeSetting
 *@Method  get
 *@Date  2022-03-09
 */
export const findTradeSetting = (params) => {
    return service({
        url: '/bill/findTradeSetting',
        method: 'get',
        params,
    });
};
/*
 *@Summary 修改发票设置
 *@Router  /bill/updateTradeSetting
 *@Method  post
 *@Date  2022-03-09
 */
export const updateTradeSetting = (data) => {
    return service({
        url: '/bill/updateTradeSetting',
        method: 'post',
        data,
    });
};

/*
 *@Summary 获取发票列表
 *@Router  /bill/getBillList
 *@Method  get
 *@Date  2022-03-10
 */
export const getBillList = (params) => {
    return service({
        url: '/bill/getBillList',
        method: 'get',
        params,
    });
};
/*
 *@Summary 获取发票详情
 *@Router  /bill/getBill
 *@Method  get
 *@Date  2022-03-25
 */
export const getBill = (params) => {
    return service({
        url: '/bill/getBill',
        method: 'get',
        params,
    });
};
/*
 *@Summary 发票发货
 *@Router  /bill/sendBill
 *@Method  post
 *@Date  2022-03-28
 */
export const sendBill = (data) => {
    return service({
        url: '/bill/sendBill',
        method: 'post',
        data,
    });
};

/*
 *@Summary 获取一键开票详情
 *@Router  /bill/confirmCheckout
 *@Method  get
 *@Date  2022-04-02
 */
export const confirmCheckout = (params) => {
    return service({
        url: '/bill/confirmCheckout',
        method: 'get',
        params,
    });
};
/*
 *@Summary 确认开票
 *@Router  /bill/billCheckout
 *@Method  post
 *@Date    2022-04-13
 */
export const billCheckout = (data) => {
    return service({
        url: '/bill/billCheckout',
        method: 'post',
        data,
    });
};
/*
 *@Summary 导出
 *@Router  /bill/exportBillList
 *@Method  get
 *@Date    2022-04-13
 */
export const exportBillList = (params) => {
    return service({
        url: '/bill/exportBillList',
        method: 'get',
        params,
    });
};
/*
 *@Summary 获取发票分类
 *@Router  /bill/getBillCategory
 *@Method  get
 *@Date    2022-04-13
 */
export const getBillCategory = (params) => {
    return service({
        url: '/bill/getBillCategory',
        method: 'get',
        params,
    });
};

export const getBillCheckoutStatus = (params) => {
    return service({
        url: '/bill/getBillCheckoutStatus',
        method: 'get',
        params,
    });
};

/*
 *@Summary 批量开票
 *@Router  /bill/batchMakeOutWithExcel
 *@Method  post
 *@Date    2023-03-18
 */
export const batchMakeOutWithExcel = (data) => {
    return service({
        url: '/bill/batchMakeOutWithExcel',
        method: 'post',
        data,
    });
};

/*
 *@Summary 确认开票
 *@Router  /bill/makeOutBatch
 *@Method  post
 *@Date    2023-03-23
 */
export const makeOutBatch = (data) => {
    return service({
        url: '/bill/makeOutBatch',
        method: 'post',
        data,
    });
};

/*
 *@Summary ai符码
 *@Router  /ai/chat/bill
 *@Method  post
 */
export const chatBill = (data) => {
    return service({
        url: '/ai/chat/bill',
        method: 'post',
        data,
    });
};
