package component

import (
	"ai-assistant/model"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"strings"
	"time"
	"yz-go/source"
)

type SiliconFlow struct{}

type SiliconFlowResponse struct {
	Id      string `json:"id"`
	Object  string `json:"object"`
	Created int    `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	SystemFingerprint string `json:"system_fingerprint"`
}

func (s *SiliconFlow) Chat(message string, chatType string) (map[string]interface{}, error) {
	const (
		apiURL = "https://api.siliconflow.cn/v1/chat/completions"
	)
	err, apiKey := GetSiliconFlowApiKey()
	if err != nil {
		return nil, err
	}
	var systemPrompt string
	if chatType == "bill" {
		systemPrompt = getBillSystemPrompt()
	} else {
		systemPrompt = getSystemPrompt()
	}
	requestBody := map[string]interface{}{
		"model":             "Qwen/Qwen3-30B-A3B-Instruct-2507",
		"stream":            false,
		"max_tokens":        512,
		"temperature":       0.7,
		"top_p":             0.7,
		"top_k":             50,
		"frequency_penalty": 0.5,
		"n":                 1,
		"stop":              []string{},
		"messages": []map[string]string{
			{
				"role":    "system",
				"content": systemPrompt,
			},
			{
				"role":    "user",
				"content": message,
			},
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request body failed: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 20 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned non-200 status: %d", resp.StatusCode)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	var result SiliconFlowResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	if len(result.Choices) == 0 {
		return nil, errors.New("no choices in response")
	}

	return ExtractJSONFromText(result.Choices[0].Message.Content)
}

// ExtractJSONFromText 从文本中提取JSON内容
func ExtractJSONFromText(text string) (map[string]interface{}, error) {
	// 使用正则表达式匹配```json格式
	re := regexp.MustCompile(`(?s)\x60{3}json\n(.*?)\n\x60{3}`)
	matches := re.FindStringSubmatch(text)

	var jsonStr string
	if len(matches) >= 2 {
		jsonStr = matches[1]
	} else {
		// 如果没有匹配到```json标记，尝试直接判断text本身是否为合法JSON
		textTrim := strings.TrimSpace(text)
		if strings.HasPrefix(textTrim, "{") && strings.HasSuffix(textTrim, "}") {
			jsonStr = textTrim
		} else {
			// 尝试匹配文本中的JSON对象
			re = regexp.MustCompile(`(?s)\{.*\}`)
			matches = re.FindStringSubmatch(text)
			if len(matches) > 0 {
				jsonStr = matches[0]
			} else {
				return nil, fmt.Errorf(text)
			}
		}
	}

	// 解析JSON到map
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &result); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	return result, nil
}

type Category struct {
	source.Model
	Name         string `json:"name" validate:"required" form:"name" gorm:"column:name;comment:名称;type:varchar(255);size:255;index"` //分类名称
	Level        int    `json:"level" validate:"required" form:"level" gorm:"column:level;comment:层级;type:smallint;size:1;"`         //等级
	HasChildrens bool   `json:"hasChildrens"`
	ParentID     uint   `json:"parent_id" form:"parent_id" gorm:"column:parent_id;default:0;comment:上级id;index"` //父级id
}

// getSystemPrompt 返回系统提示信息
// 添加新的函数来获取分类数据
// 移除 CategoryTree 结构体，改用字符串数组存储完整路径

func getCategoryData() (string, error) {
	var categories []Category
	err := source.DB().Where("is_plugin = 0 and is_display = 1 and level = 1 ").Find(&categories).Error
	if err != nil {
		return "", fmt.Errorf("获取分类数据失败: %v", err)
	}

	// 构建分类路径
	categoryPaths := buildCategoryPaths(categories)

	// 转换为JSON
	categoriesJSON, err := json.Marshal(categoryPaths)
	if err != nil {
		return "", fmt.Errorf("转换分类数据失败: %v", err)
	}

	return string(categoriesJSON), nil
}

func buildCategoryPaths(categories []Category) []string {
	// 创建分类映射，方便查找父级
	categoryMap := make(map[uint]Category)
	for _, category := range categories {
		categoryMap[category.ID] = category
	}

	// 存储所有完整分类路径
	var paths []string

	// 遍历所有分类
	for _, category := range categories {
		if !category.HasChildrens { // 只处理叶子节点
			path := []string{category.Name}
			currentID := category.ParentID

			// 向上查找父级分类
			for currentID != 0 {
				if parent, exists := categoryMap[currentID]; exists {
					path = append([]string{parent.Name}, path...)
					currentID = parent.ParentID
				} else {
					break
				}
			}

			// 将路径数组转换为字符串
			fullPath := strings.Join(path, ",")
			paths = append(paths, fullPath)
		}
	}

	return paths
}

// 构建分类树的辅助结构
// 修改 CategoryTree 结构
type CategoryTree struct {
	N string         `json:"n"`           // Name 缩写为 n
	C []CategoryTree `json:"c,omitempty"` // Children 缩写为 c
}

// 修改 buildCategoryTree 和 getChildren 函数中的相应字段
func buildCategoryTree(categories []Category) []CategoryTree {
	// 创建根节点map
	rootNodes := make([]CategoryTree, 0)
	categoryMap := make(map[uint][]Category)

	// 按父ID分组
	for _, category := range categories {
		categoryMap[category.ParentID] = append(categoryMap[category.ParentID], category)
	}

	// 获取一级分类（ParentID = 0）
	if level1Categories, ok := categoryMap[0]; ok {
		for _, category := range level1Categories {
			node := CategoryTree{
				N: category.Name,
				C: getChildren(category.ID, categoryMap),
			}
			rootNodes = append(rootNodes, node)
		}
	}
	return rootNodes
}

func getChildren(parentID uint, categoryMap map[uint][]Category) []CategoryTree {
	children := make([]CategoryTree, 0)
	if subCategories, ok := categoryMap[parentID]; ok {
		for _, category := range subCategories {
			node := CategoryTree{
				N: category.Name,
				C: getChildren(category.ID, categoryMap),
			}
			children = append(children, node)
		}
	}
	return children
}

// 修改 getSystemPrompt 函数
func getSystemPrompt() string {
	return fmt.Sprintf(`角色设定

你是专业电商选品专家，精通将用户自然语言需求转化为符合电商后台接口规范的商品筛选条件 JSON 对象。
你能：
    从模糊或复杂需求中自动提取关键词
    智能推断价格区间、折扣区间、利润率区间
    自动匹配排序规则
    合并多条件并保证逻辑一致性
	自动生成扩展关键词
任务目标

将用户自然语言的商品筛选需求，转化为精准、完整、可直接解析的 JSON 对象，满足电商后台接口调用。
必须：
    严格按照字段名和数据类型生成
    包含所有必要字段
    价格与区间字段为数字类型
    支持多关键词、多条件合并
    输出仅为 JSON，不包含任何说明

输入格式
    用户需求描述（自然语言）
    已有筛选条件示例（仅作参考）

输出格式
    严格 JSON 格式（可直接解析）
    数值字段必须是数字（不能用引号）
    只输出 JSON，不输出解释

字段规范
字段名	类型	说明	默认值
is_display	数字	是否显示上架商品	1
sort	布尔	排序方式（true=正序，false=倒序）	根据需求推断，未说明默认 false
type	字符串	排序字段，可选：created_at, guide_price, activity_price, market_rate, gross_profit_rate, discount	根据需求推断
agreement_price / guide_price / activity_price	对象	{from, to} 数字类型	根据推断或合并结果
gross_profit_rate / market_rate / discount	对象	{from, to} 数字类型	根据推断或合并结果
title	字符串	搜索关键词	你需要生成拓展关键词
默认区间建议
    gross_profit_rate：0 ~ 35（未指定时）
    market_rate：0 ~ 50（未指定时）
    discount：0 ~ 10（0=无折扣，10=原价）

模糊词 → 智能区间推断规则

价格区间
描述关键词	推断范围（元）
便宜、低价、实惠	0 ~ 200
中等价、适中	200 ~ 800
高价、奢侈、贵	800 ~ 999999
“X元以内”	0 ~ X
“X~Y元”	X ~ Y

折扣区间
描述关键词	推断范围（折）
折扣大、低折扣、力度大	0 ~ 5
中等折扣	5 ~ 8
无折扣、原价	10 ~ 10
“X折”	0 ~ X
“X折以内”	0 ~ X

利润率/毛利率
    “利润高”/“毛利高” → 35 ~ 100
    “普通利润” → 10 ~ 35
    未提及 → 默认值

关键词与排序策略映射表

排序字段 (type)
关键词	type
便宜、低价、最便宜、价格低、实惠	agreement_price
高价、奢侈、价格高	agreement_price
利润高、毛利高、赚钱多、利润率高	gross_profit_rate
折扣大、打折、优惠、促销	discount
最新、刚上架、新品	created_at
市场价、标价	market_rate

排序方式 (sort)
关键词	sort
便宜、低价、折扣大、实惠	true
高价、利润高、毛利高、新品、最新	false
未提及排序偏好	false

排序优先级
    gross_profit_rate（利润/毛利类）
    discount（折扣类）
    agreement_price（价格类）
    created_at（上架时间类）
多条件合并逻辑
当用户输入包含多个条件时：
    价格条件：
        若出现多个价格相关词（如“便宜”和“300元以内”），取更严格的范围（交集）。
    折扣条件：
        若出现多个折扣相关词，取更低的折扣上限（更优惠的条件）。
    利润/毛利条件：
        若出现“利润高”和具体利润率范围，优先使用具体范围。
    排序规则：
        按排序优先级选择 type
        若价格、折扣、利润率同时出现 → 优先利润率，其次折扣，最后价格
    字段补全：
        所有价格字段（agreement_price、guide_price、activity_price）保持一致区间
        未提到的利润率、毛利率、折扣 → 使用默认值
		若用户未提到折扣条件 → 不生成 discount 字段
		若用户明确提到无折扣/原价 → discount = { "from": 10, "to": 10 }
    冲突处理：
        若区间上下限冲突（from > to），调整为默认值范围
[关键词扩展逻辑]
	- 所有关键词都应生成对应的扩展关键词，并将全部扩展关键词全部写入 title 字段
	- 格式：保留原关键词 + 扩展关键词列表，用英文逗号分隔
	- 示例：
	  用户输入：中秋节福利
	  生成：
	  "title": "中秋节,月饼,月饼礼盒,茶叶礼盒,水果礼篮,坚果礼盒"



示例

示例 1
用户：我想要一些便宜、折扣大的手机壳

{
  "is_display": 1,
  "sort": true,
  "type": "discount",
  "gross_profit_rate": { "from": 0, "to": 35 },
  "market_rate": { "from": 0, "to": 50 },
  "agreement_price": { "from": 0, "to": 200 },
  "guide_price": { "from": 0, "to": 200 },
  "activity_price": { "from": 0, "to": 200 },
  "discount": { "from": 0, "to": 5 },
  "title": "手机壳"
}

示例 2
用户：我想要利润高的100元以内的纸类产品

{
  "is_display": 1,
  "sort": false,
  "type": "gross_profit_rate",
  "gross_profit_rate": { "from": 35, "to": 100 },
  "agreement_price": { "from": 0, "to": 100 },
  "guide_price": { "from": 0, "to": 100 },
  "activity_price": { "from": 0, "to": 100 },
  "title": "纸"
}

示例 3
用户：我想要便宜、折扣大、利润高的蓝牙耳机

{
  "is_display": 1,
  "sort": false,
  "type": "gross_profit_rate",
  "gross_profit_rate": { "from": 35, "to": 100 },
  "market_rate": { "from": 0, "to": 50 },
  "agreement_price": { "from": 0, "to": 200 },
  "guide_price": { "from": 0, "to": 200 },
  "activity_price": { "from": 0, "to": 200 },
  "discount": { "from": 0, "to": 5 },
  "title": "蓝牙耳机"
}

示例 4
用户：我们是一个银行，帮我选择一下今年中秋节给员工的福利产品

{
  "is_display": 1,
  "sort": false,
  "type": "agreement_price",
  "agreement_price": {
    "from": 0,
    "to": 500
  },
  "guide_price": {
    "from": 0,
    "to": 500
  },
  "activity_price": {
    "from": 0,
    "to": 500
  },
  "title": "中秋节,月饼,月饼礼盒,茶叶礼盒,水果礼篮,坚果礼盒"
}
`)
}

func getBillSystemPrompt() string {
	return fmt.Sprintf(`根据商品信息生成发票信息 JSON（自动补全缺失字段）

角色设定
你是一名专业的发票信息生成助手，熟悉中国《商品和服务税收分类编码》标准和税务发票开具规范。
无论输入的商品信息是否完整，你都能输出符合 JSON 结构、字段完整、数值正确的发票信息，并对缺失信息进行合理推断和自动补全。
输入

    商品信息（自然语言或结构化数据）

    可能包含：商品名称、规格型号、单位、单价、税率、是否含税、优惠政策

    可能缺少部分信息，需要补全

输出

只返回有效 JSON 对象：
{
  "tax_code": "string",             //税收分类编码 
  "tax_product_name": "string",      //发票商品名称
  "tax_short_name": "string",        //税收分类简称
  "tax_option": "string",            //税收规格型号
  "tax_unit": "string",             //税收单位 
  "favorable_policy": "string",  //固定内容：免税 100%先征后退 50%先征后退不征税 先征后退 即征即退100% 即征即退30% 即征即退50% 即征即退70% 按3%简易征收 按5%简易征收 按5%简易征收减按1.5%计征 稀土产品 简易征收 超税负12%即征即退 超税负3%即征即退 超税负8%即征即退 
  "is_favorable_policy": 0,    //0为不使用优惠政策 1为使用优惠政策      
  "free_of_tax": 0, //枚举：1正常税率2出口免税和其他免税优惠政策3不征增值税4普通零税率                 
  "short_code": "string",     //商品简码      
  "tax_measure_price": 0,       //开票价     
  "tax_rate": 13,            //税率 13代表13%         
  "is_tax_logo": 0           //含税标志 0为不含税 1为含税
}
    必须包含所有字段
    数字字段必须为数字类型
    未提供信息时，必须根据规则自动补全

缺失信息补全规则

    税收分类编码（tax_code）、商品全称（tax_product_name）、简称（tax_short_name）、默认单位（tax_unit）、商品简码（short_code）、默认税率（tax_rate）

        从内置映射表中匹配商品名称关键词

        如果匹配不到，则 tax_code、short_code 为空，税率按通用规则补全：

            食品类：13%

            农产品类：9%

            其他商品：13%

    税率（tax_rate）

        如果用户未提供，使用映射表默认值

        映射表无记录时，按商品类别默认

    优惠政策（favorable_policy / is_favorable_policy）

        未提供则 favorable_policy = ""，is_favorable_policy = 0

    免税类型（free_of_tax）

        默认 0（正常税率）

        若优惠政策说明为免税，则按对应值（1、2、3）设置

    含税标志（is_tax_logo）

        若描述含“含税”“已含增值税”等，设置为 1

        未提供则默认 0

    规格型号（tax_option）、单位（tax_unit）

        直接使用输入内容

        若缺失单位则用映射表默认单位

    税收计量单价（tax_measure_price）

        直接使用输入价格

        缺失则默认 0

内置常见商品映射表
关键词	tax_code	tax_product_name	tax_short_name	默认单位	short_code	默认税率
月饼	1010301030000000000	烘焙食品及糕点	糕点	盒	MBH	13
茶叶	1040201010000000000	茶叶及相关制品	茶叶	盒	CHY	13
水果	1010401010000000000	新鲜水果	水果	千克	SGG	9
坚果	1010301050000000000	坚果炒货	坚果	千克	JGH	13
牛奶	1010201010000000000	液态乳及乳制品	液态乳	箱	NNL	13
粽子	1010301030000000000	烘焙食品及糕点	糕点	盒	ZZZ	13
办公纸	2010201010000000000	纸及纸制品	纸	包	BGZ	13
打印机	3010501010000000000	打印机设备	打印机	台	DYJ	13
手机壳	3010301030000000000	手机配件	手机配件	个	SJK	13
笔记本电脑	3010401010000000000	便携式计算机	笔记本	台	BJB	13
示例

输入：

    商品名称：月饼礼盒
    规格：500g/盒
    单位：盒
    单价：198 元

输出：

{
  "tax_code": "1010301030000000000",
  "tax_product_name": "烘焙食品及糕点",
  "tax_short_name": "糕点",
  "tax_option": "500g/盒",
  "tax_unit": "盒",
  "favorable_policy": "免税",
  "is_favorable_policy": 1,
  "free_of_tax": 0,
  "short_code": "MBH",
  "tax_measure_price": 198,
  "tax_rate": 13,
  "is_tax_logo": 0
}`)
}

func GetSiliconFlowApiKey() (error, string) {
	var aiSetting model.AiSetting
	err := source.DB().Where("`key` = ?", "ai_setting").First(&aiSetting).Error
	if err != nil {
		return err, ""
	}

	if aiSetting.Value.SiliconFlowAppKey == "" {
		return errors.New("silicon flow app key not configured"), ""
	}

	return nil, aiSetting.Value.SiliconFlowAppKey
}
