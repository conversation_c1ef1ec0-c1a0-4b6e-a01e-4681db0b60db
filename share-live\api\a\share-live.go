package a

import (
	"bufio"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"os"
	"path/filepath"
	productService "product/service"
	"share-live/common"
	"share-live/model"
	"share-live/request"
	"share-live/service"
	"share-live/setting"
	"strings"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

// @Tags 共享直播API
// @Summary 获取共享直播配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取共享直播配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/getSysShareLiveSetting [get]
func GetSysShareLiveSetting(c *gin.Context) {
	var sys setting.SysSetting
	_ = c.ShouldBindJSON(&sys)
	err, shareLiveSetting := setting.GetSysShareLiveSetting()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var configData interface{}
	jsonConfig, _ := json.Marshal(&shareLiveSetting)
	configData = base64.StdEncoding.EncodeToString(jsonConfig)

	yzResponse.OkWithData(configData, c)

}

// @Tags 共享直播API
// @Summary 保存 共享直播配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "保存获取共享直播配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/saveSysShareLiveSetting [get]
func SaveSysShareLiveSetting(c *gin.Context) {
	var saveSaveSysShareLiveSetting request.SaveSaveSysShareLiveSetting
	_ = c.ShouldBindJSON(&saveSaveSysShareLiveSetting)

	var sys setting.SysSetting

	sysByte, err := base64.StdEncoding.DecodeString(saveSaveSysShareLiveSetting.Data)
	if err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	_ = json.Unmarshal(sysByte, &sys)

	sys.Key = "share_live"
	if sys.Value.ImAppKey != "" {
		if sys.Value.ImAppSecret == "" {
			yzResponse.FailWithMessage("请配置ImAppSecret", c)
			return
		}
		if sys.Value.SerialNumber == "" {
			yzResponse.FailWithMessage("请配置Im-序号", c)
			return
		}
		if sys.Value.AppId == "" {
			yzResponse.FailWithMessage("请配置通讯sdkappid号", c)
			return
		}
	}
	err = setting.SaveSysShareLiveSetting(sys)
	if err != nil {
		yzResponse.FailWithMessage("保存失败"+err.Error(), c)
		return
	}

	err, shareLiveSetting := setting.GetSysShareLiveSetting()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var isUpdate = 0 //是否需要修改  1是 0否

	//var url = "https://244b38ac.r12.cpolar.top/" //测试使用
	var url = c.Request.Header.Get("X-Forwarded-Proto") + "://" + c.Request.Host + "/supplyapi/" // --正式情况
	//创建回调
	if shareLiveSetting.Value.IsCallback == 1 {
		var TemplateName = "supply模板"        //模板名称
		var Description = "中台：" + url + "回调" //模板描述
		// 开播回调 URL，
		// 相关协议文档：[事件消息通知](/document/product/267/32744)。
		var StreamBeginNotifyUrl = url + "shareLive/begin"
		// 断流回调 URL，
		// 相关协议文档：[事件消息通知](/document/product/267/32744)。
		var StreamEndNotifyUrl = url + "shareLive/end"
		// 录制回调 URL，
		// 相关协议文档：[事件消息通知](/document/product/267/32744)。
		var RecordNotifyUrl = url + "shareLive/record"
		// 截图回调 URL，
		// 相关协议文档：[事件消息通知](/document/product/267/32744)。
		var SnapshotNotifyUrl = url + "shareLive/snapshot"
		// 鉴黄回调 URL，
		// 相关协议文档：[事件消息通知](/document/product/267/32741)。
		var PornCensorshipNotifyUrl = url + "shareLive/pornCensorship"
		// 回调 Key，回调 URL 公用，回调签名详见事件消息通知文档。
		// [事件消息通知](/document/product/267/32744)。
		var CallbackKey = common.RandString(16)
		if shareLiveSetting.Value.CallbackKey == "" {
			shareLiveSetting.Value.CallbackKey = CallbackKey
		} else {
			CallbackKey = shareLiveSetting.Value.CallbackKey
		}
		var requestData request.LiveCallbackTemplateRequest
		requestData.TemplateName = &TemplateName
		requestData.Description = &Description
		requestData.StreamBeginNotifyUrl = &StreamBeginNotifyUrl
		requestData.StreamEndNotifyUrl = &StreamEndNotifyUrl
		requestData.RecordNotifyUrl = &RecordNotifyUrl
		requestData.SnapshotNotifyUrl = &SnapshotNotifyUrl
		requestData.PornCensorshipNotifyUrl = &PornCensorshipNotifyUrl
		requestData.CallbackKey = &CallbackKey

		if shareLiveSetting.Value.TemplateId != 0 {
			requestData.TemplateId = &shareLiveSetting.Value.TemplateId
			err, _ = common.GetLiveCallbackTemplateRequest(requestData)
			if err != nil {
				if err.Error() == "FailedOperation.NotFound" {
					shareLiveSetting.Value.TemplateId = 0
				} else {
					log.Log().Error("获取回调模板失败", zap.Any("err", err))
					yzResponse.FailWithMessage("设置保存成功,获取回调模板失败"+err.Error(), c)
					return
				}
			}
		}

		if shareLiveSetting.Value.TemplateId == 0 {
			isUpdate = 1
			err, CallbackTemplateData := common.CreateLiveCallbackTemplateRequest(requestData)
			if err != nil {
				log.Log().Error("创建回调模板失败", zap.Any("err", err))
				yzResponse.FailWithMessage("设置保存成功,创建回调模板失败"+err.Error(), c)
				return
			}
			shareLiveSetting.Value.TemplateId = *CallbackTemplateData.Response.TemplateId
			shareLiveSetting.Value.IsCallbackRule = 0
		} else {
			isUpdate = 1
			requestData.TemplateId = &shareLiveSetting.Value.TemplateId
			err, _ := common.SaveLiveCallbackTemplateRequest(requestData)
			if err != nil {
				log.Log().Error("创建回调模板失败", zap.Any("err", err))
				yzResponse.FailWithMessage("设置保存成功,创建回调模板失败"+err.Error(), c)
				return
			}
		}
		if shareLiveSetting.Value.IsCallbackRule != 1 {
			isUpdate = 1
			err, CreateLiveCallbackRule := common.CreateLiveCallbackRule(shareLiveSetting.Value.PlugFlowUrl, shareLiveSetting.Value.TemplateId)
			if err != nil {
				log.Log().Error("创建回调规则失败", zap.Any("err", err), zap.Any("CreateLiveCallbackRule", CreateLiveCallbackRule))
				yzResponse.FailWithMessage("设置保存成功,创建回调模板成功,创建回调规则失败"+err.Error(), c)
				return
			}
			shareLiveSetting.Value.IsCallbackRule = 1
		}
	}

	//shareLiveSetting.Value.TemplateId = 426630
	//isUpdate = 1
	if isUpdate == 1 {
		err = setting.SaveSysShareLiveSetting(shareLiveSetting)
		if err != nil {
			yzResponse.FailWithMessage("保存回调模板id失败"+err.Error(), c)
			return
		}
	}
	yzResponse.OkWithData("保存成功", c)

}

// @Tags 共享直播
// @Summary 共享直播分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播分类列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveCategoryList [get]
func GetShareLiveCategoryList(c *gin.Context) {
	var pageInfo request.ShareLiveCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetShareLiveCategoryList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 共享直播
// @Summary 共享直播分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播分类列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveCategorys [get]
func GetShareLiveCategorys(c *gin.Context) {
	var pageInfo request.ShareLiveCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list := service.GetShareLiveCategorys(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(list, c)
	}
}

// createShareLiveCategoryList
// @Tags 共享直播
// @Summary 删除共享直播分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "删除共享直播分类"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/createShareLiveCategory [get]
func DeleteShareLiveCategory(c *gin.Context) {
	var shareLiveCategory model.ShareLiveCategory
	err := c.ShouldBindJSON(&shareLiveCategory)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.DeleteShareLiveCategory(shareLiveCategory); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 创建共享直播分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "创建共享直播分类"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/createShareLiveCategory [get]
func CreateShareLiveCategory(c *gin.Context) {
	var shareLiveCategory model.ShareLiveCategory
	err := c.ShouldBindJSON(&shareLiveCategory)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.CreateShareLiveCategory(shareLiveCategory); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("创建成功", c)
}

// saveShareLiveCategoryList
// @Tags 共享直播
// @Summary 保存共享直播分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "保存共享直播分类"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/saveShareLiveCategoryList [get]
func SaveShareLiveCategory(c *gin.Context) {
	var shareLiveCategory model.ShareLiveCategory
	err := c.ShouldBindJSON(&shareLiveCategory)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.SaveShareLiveCategory(shareLiveCategory); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// GetShareLiveCategoryById
// @Tags 共享直播
// @Summary 获取共享直播分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "获取共享直播分类"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveCategoryById [get]
func GetShareLiveCategoryById(c *gin.Context) {
	var shareLiveCategory model.ShareLiveCategory
	err := c.ShouldBindQuery(&shareLiveCategory)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, data := service.GetShareLiveCategoryById(shareLiveCategory.ID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 创建共享直播间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "创建共享直播间"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/createShareLiveRoom [get]
func CreateShareLiveRoom(c *gin.Context) {
	var createShareLiveRoomRequest request.CreateShareLiveRoomRequest
	err := c.ShouldBindJSON(&createShareLiveRoomRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.CreateShareLiveRoom(createShareLiveRoomRequest); err != nil {
		//如果不是创建错误则提示成功
		if strings.Index(err.Error(), "直播间创建成功") != -1 {
			yzResponse.OkWithMessage(err.Error(), c)
			return
		}
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("创建成功", c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 创建共享直播间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "创建共享直播间"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/saveShareLiveRoom [get]
func SaveShareLiveRoom(c *gin.Context) {
	var createShareLiveRoomRequest request.CreateShareLiveRoomRequest
	err := c.ShouldBindJSON(&createShareLiveRoomRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.SaveShareLiveRoom(createShareLiveRoomRequest); err != nil {
		//如果不是创建错误则提示成功
		if strings.Index(err.Error(), "直播间创建成功") != -1 {
			yzResponse.OkWithMessage(err.Error(), c)
			return
		}
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 开始直播
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "开始直播"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/saveShareLiveRoom [get]
func SaveShareLiveRoomStart(c *gin.Context) {
	var createShareLiveRoomRequest request.ShareLiveRoomIdRequest
	err := c.ShouldBindQuery(&createShareLiveRoomRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.SaveShareLiveRoomStatus(createShareLiveRoomRequest.Id, 1); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 结束直播
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "结束直播"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/saveShareLiveRoom [get]
func SaveShareLiveRoomEnd(c *gin.Context) {
	var createShareLiveRoomRequest request.ShareLiveRoomIdRequest
	err := c.ShouldBindQuery(&createShareLiveRoomRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.SaveShareLiveRoomStatus(createShareLiveRoomRequest.Id, 2); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 是否开启回放
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "是否开启回放"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/saveShareLiveRoom [get]
func SaveShareLiveRoomIsPlayBack(c *gin.Context) {
	var createShareLiveRoomRequest request.SaveShareLiveRoomIsPlayBack
	err := c.ShouldBindQuery(&createShareLiveRoomRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.SaveShareLiveRoomIsPlayBack(createShareLiveRoomRequest.Id, createShareLiveRoomRequest.IsPlayback); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 创建共享直播间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "创建共享直播间"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/saveShareLiveRoom [get]
func GetShareLiveRoomById(c *gin.Context) {
	var shareLiveRoomIdRequest request.ShareLiveRoomIdRequest
	err := c.ShouldBindQuery(&shareLiveRoomIdRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, data := service.GetShareLiveRoomById(shareLiveRoomIdRequest.Id)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 创建共享直播间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "创建共享直播间"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/saveShareLiveRoom [get]
func GetShareLiveRoomByIds(c *gin.Context) {
	var shareLiveRoomIdRequest request.ShareLiveRoomIdsRequest
	err := c.ShouldBindJSON(&shareLiveRoomIdRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, data := service.GetShareLiveRoomByIds(shareLiveRoomIdRequest.Ids)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}

// 修改直播间开关状态
func SaveShareLiveRoomIsOpen(c *gin.Context) {
	var req request.ShareLiveRoomIsOpenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err := service.SaveShareLiveRoomIsOpen(req.ID, req.IsOpen)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("修改成功", c)
}

// 修改直播间横屏状态
func SaveShareLiveRoomIsLandscape(c *gin.Context) {
	var req request.ShareLiveRoomIsLandscapeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err := service.SaveShareLiveRoomIsLandscape(req.ID, req.IsLandscape)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("修改成功", c)
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 通过id获取观看人数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "通过id获取观看人数"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/getShareLiveRoomNumById [get]
func GetShareLiveRoomNumById(c *gin.Context) {
	var shareLiveRoomIdRequest request.ShareLiveRoomIdRequest
	err := c.ShouldBindQuery(&shareLiveRoomIdRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, data := service.GetShareLiveRoomNumById(shareLiveRoomIdRequest.Id)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"num": data.TotalNum, "like_num": data.LikeNum}, c)
}

// @Tags 共享直播
// @Summary 共享直播直播间列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播直播间列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveRoomList [get]
func GetShareLiveRoomList(c *gin.Context) {
	var pageInfo request.ShareLiveRoomSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetShareLiveRoomList(pageInfo, 0); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 共享直播
// @Summary 共享直播直播间商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播直播间商品列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveRoomProductList [get]
func GetProductStorageInfoList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.ShareLiveRoomId == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}
	appID := utils.GetAppID(c)
	userID := utils.GetAppUserID(c)
	var app productService.Application
	err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.AppID = appID
	if err, list, total := service.GetProductStorageInfoList(pageInfo, app.AppLevelID, userID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 共享直播
// @Summary 共享直播直播间商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播直播间商品列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveRoomProductList [get]
func GetShareLiveRoomProductList(c *gin.Context) {
	var pageInfo request.GetShareLiveRoomProductListRequest
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.ShareLiveRoomId == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}
	appID := utils.GetAppID(c)
	if err, list, total := service.GetShareLiveRoomProductList(pageInfo, appID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 共享直播
// @Summary 共享直播直播间列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播直播间列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveRoomList [get]
func GetShareLiveRooms(c *gin.Context) {
	var pageInfo request.ShareLiveRoomSearch
	_ = c.ShouldBindQuery(&pageInfo)

	if err, list := service.GetShareLiveRooms(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(list, c)
	}
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 通过直播间id获取回放
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "通过直播间id获取回放"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/getShareLiveRoomRecordFileByRoomId [get]
func GetShareLiveRoomRecordFileByRoomId(c *gin.Context) {
	var shareLiveRoomIdRequest model.ShareLiveRoomRecordFile
	err := c.ShouldBindQuery(&shareLiveRoomIdRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, data := service.GetShareLiveRoomRecordFileByRoomId(shareLiveRoomIdRequest.ShareLiveRoomId)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}

// @Tags 共享直播
// @Summary 共享直播直播采购端列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播直播采购端列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveRoomList [get]
func GetShareLiveRoomApplicationList(c *gin.Context) {
	var pageInfo request.ShareLiveRoomApplicationSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetShareLiveRoomApplicationList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func countLinesInFile(filename string) (int, error) {
	file, err := os.Open(filename)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	count := 0
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "//") && !strings.HasPrefix(line, "/*") {
			count++
		}
	}

	return count, nil
}

func countLinesInDirectory(dir string) (int, error) {
	totalLines := 0
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".go") {
			lines, err := countLinesInFile(path)
			if err != nil {
				return err
			}
			totalLines += lines
		}
		return nil
	})

	return totalLines, err
}

func GetHang(c *gin.Context) {
	dir := "D:\\go\\project\\supply-chain\\service-provider-system"
	lines, err := countLinesInDirectory(dir)
	if err != nil {
		fmt.Println("Error counting lines:", err)
		return
	}
	fmt.Printf("Total lines of Go code in %s: %d\n", dir, lines)
}

// @Tags 共享直播API
// @Summary 修改直播间采购端设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SaveShareLiveRoomApplicationSetting true "修改直播间采购端设置参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/saveShareLiveRoomApplicationSetting [post]
func SaveShareLiveRoomApplicationSetting(c *gin.Context) {
	var req request.SaveShareLiveRoomApplicationSetting
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err = service.SaveShareLiveRoomApplicationSetting(req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("修改成功", c)
}
