<template>
    <el-dialog
            :title="`${title}直播间`"
            :visible="isShow"
            width="700px"
            :before-close="handleClose">
        <el-form :model="formData" label-width="120px" ref="form" :rules="rules">
            <el-row>
                <el-col :span="18">
                    <el-form-item label="排序:" prop="sort">
                        <m-num-input v-model="formData.sort"></m-num-input>
                    </el-form-item>
                    <el-form-item label="直播间标题:" prop="title">
                        <el-input v-model="formData.title"></el-input>
                    </el-form-item>
                    <el-form-item label="直播间分类:" prop="share_live_category_id">
                        <el-select v-model="formData.share_live_category_id" clearable class="w100">
                            <el-option v-for="item in categoryOption" :key="item.id" :label="item.title"
                                       :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开播时间:" prop="start_at">
                        <el-date-picker
                                v-model="formData.start_at"
                                type="datetime"
                                value-format="timestamp"
                                placeholder="开始时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="主播会员:" prop="user_id">
                        <div v-if="formData.user_id && userData.username" class="f fac">
                            <p class="mr10">手机号: {{ userData.username }}</p>
                            <p v-if="userData.nickname">昵称: {{ userData.nickname }}</p>
                            <p v-else class="color-red">昵称: 未完善会员昵称</p>
                        </div>
                        <el-button type="text" @click="openSelectUserDialog">选择会员</el-button>
                        <p class="color-grap">请注意在会员列表中完善会员昵称，否则观看端将无法显示主播名称！</p>
                    </el-form-item>
                    <el-form-item label="直播间封面:" prop="image_url">
                        <MUpload :imageUrl="formData.image_url" :maxWH="640" :fileSize="10240" @change="resUpload"/>
                        <p class="color-grap">宽度建议：640px,图片大小限制10M以内</p>
                    </el-form-item>

                  <!-- 添加是否指定采购端选项 -->
                  <el-form-item label="指定采购端:">
                    <el-switch v-model="formData.is_specify_application"   :active-value="1"
                               :inactive-value="0"   @change="handleApplicationSwitch"></el-switch>
                  </el-form-item>

                  <!-- 采购端多选框组 (当开关打开时显示) -->
                  <!-- 改进的采购端选择器 -->
                  <el-form-item v-if="formData.is_specify_application == 1" label="选择采购端:" prop="application_ids">
                    <el-select
                        v-model="formData.application_ids"
                        multiple
                        collapse-tags
                        placeholder="请选择采购端"
                        style="width: 100%"
                        filterable
                    >
                      <el-option
                          v-for="item in applicationOption"
                          :key="item.id"
                          :label="item.app_name"
                          :value="item.id"
                      ></el-option>
                    </el-select>
                    <p class="color-grap" v-if="applicationOption.length === 0">暂无可用采购端，请先配置采购端信息</p>
                  </el-form-item>
                    <el-form-item label="直播间商品:">
                        <template v-if="selectedProduct">
                            <div class="f fw">
                                <div v-for="item in selectedProduct" :key="item.id" class="select-product-item">
                                    <m-image style="width: 90px;height: 90px" :src="item.image_url"></m-image>
                                    <p class="hiddenText2">{{ item.title }}</p>
                                </div>
                            </div>
                        </template>
                        <el-button type="primary" @click="openGoods">选择商品</el-button>
                    </el-form-item>
                    <el-form-item label="是否横屏:">
                        <el-switch 
                            v-model="formData.is_landscape" 
                            :active-value="1"
                            :inactive-value="0">
                        </el-switch>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer">
            <el-button type="primary" @click="confirm">确 定</el-button>
            <el-button @click="handleClose">取 消</el-button>
        </div>
        <ProductDrawer ref="productDrawer" :whetherToDisableRepeatedly="false" :checkGoods="selectedProduct"
                       @getSelectedProduct="getSelectedProduct"/>
        <SelectUserDialog ref="selectUserDialog" @getUserData="getUserData"/>
    </el-dialog>
</template>
<script>
import MUpload from "@/components/upload/image.vue"
import {getShareLiveCategorys, createShareLiveRoom, saveShareLiveRoom, getShareLiveRoomById,getApplicationOption} from "@/api/shareLive";
import ProductDrawer from "@/components/productDrawer/index.vue";
import SelectUserDialog from "./selectUserDialog.vue";

export default {
    components: {MUpload, ProductDrawer, SelectUserDialog},
    data() {
        return {
            // 选中的商品数组
            selectedProduct: [],
            title: "创建",
            isShow: false,
            categoryOption: [],
            userData: {},
            applicationOption: [], // 采购端选项
            formData: {
                sort: null, // 排序
                title: "",
                share_live_category_id: null, // 直播间分类
                start_at: "", // 开播时间
                user_id: null, // 主播会员
                image_url: "", // 直播间封面
                status: 0, // 状态0等待直播1直播中2已结束
                is_specify_application: 0, // 是否指定采购端
                application_ids: [], // 选中的采购端ID数组
                is_landscape: 0 // 是否横屏
            },
            rules: {
                title: {
                    required: true,
                    message: "请输入直播间标题",
                    trigger: "blur"
                },
                share_live_category_id: {
                    required: true,
                    message: "请选择直播间分类",
                    trigger: "change"
                },
                start_at: {
                    required: true,
                    message: "请选择开播时间",
                    trigger: "change"
                },
                image_url: {
                    required: true,
                    message: "请上传直播间封面",
                    trigger: "change"
                }
            }
        }
    },
    methods: {
        openSelectUserDialog() {
            let data = {}
            if (this.formData.user_id) {
                data.id = this.formData.user_id
                data.username = this.userData.username
                data.nickname = this.userData.nickname
            }
            this.$refs.selectUserDialog.init(data)
        },
        getUserData(user) {
            this.formData.user_id = user.id
            this.userData.username = user.username
            this.userData.nickname = user.nickname
        },
        init(id = null) {
            this.isShow = true
            this.getCategorys()
            this.getApplicationOptions(); // 加载采购端选项
            if (id) {
                this.title = "编辑"
                this.getInfo(id)
            }
        },
      // 开关切换处理
      handleApplicationSwitch(value) {
        if (!value) {
          this.formData.application_ids = [];
        } else {
          this.getApplicationOptions();
        }
      },
      // 获取采购端选项
      async getApplicationOptions() {
        const {code, data} = await getApplicationOption();
        if (code === 0) {
          this.applicationOption = data.list;
        }
      },

        // 获取直播间信息
        async getInfo(id) {
            const {code, data} = await getShareLiveRoomById({id})
            if (code === 0) {
                this.formData.id = data.id
                this.formData.start_at = data.start_at ? data.start_at * 1000 : null
                this.formData.image_url = data.image_url
                this.formData.status = data.status
                this.formData.title = data.title
                this.formData.sort = data.sort
                this.formData.user_id = data.user_id
                this.userData.username = data.user.username
                this.userData.nickname = data.user.nickname
                this.formData.share_live_category_id = data.share_live_category_id
                this.formData.is_specify_application = data.is_specify_application
                this.formData.application_ids = data.application_ids
                this.formData.is_landscape = data.is_landscape
                if (data.share_live_room_products && data.share_live_room_products.length) {
                    this.selectedProduct = data.share_live_room_products.map(item => {
                        return {...item.product}
                    })
                }
            }
        },
        // 选中的商品回调
        getSelectedProduct(products) {
            this.selectedProduct = []
            if (this.selectedProduct && this.selectedProduct.length) {
                this.selectedProduct = this.$fn.removeRep(this.selectedProduct, products)
            } else {
                this.selectedProduct = products
            }
        },
        // 打开选择商品
        openGoods() {
            this.$refs.productDrawer.isShow = true
            this.$refs.productDrawer.init()
        },
        // 获取直播间分类
        async getCategorys() {
            const {code, data} = await getShareLiveCategorys()
            if (code === 0) {
                this.categoryOption = data
            }
        },
        // 图片上传回调
        resUpload(url) {
            this.formData.image_url = url
        },
        handleClose() {
            this.isShow = false
            this.$refs.form.resetFields();
            this.selectedProduct = []
            this.title = "创建"
            this.isShow = false
            this.categoryOption = []
            this.userData = {}
            this.formData = {
                sort: null, // 排序
                title: "",
                share_live_category_id: null, // 直播间分类
                start_at: "", // 开播时间
                user_id: null, // 主播会员
                image_url: "", // 直播间封面
                application_ids:[],
                is_specify_application: 0,
                is_landscape: 0, // 是否横屏
                status: 0 // 状态0等待直播1直播中2已结束
            }
        },
        confirm() {
            this.$refs.form.validate(async valid => {
                if (!valid) return;
                let params = {
                    share_live_room: {
                        sort: this.formData.sort,
                        title: this.formData.title,
                        share_live_category_id: this.formData.share_live_category_id,
                        start_at: this.formData.start_at ? this.formData.start_at / 1000 : null,
                        user_id: this.formData.user_id,
                        image_url: this.formData.image_url,
                        status: this.formData.status,
                        is_specify_application: this.formData.is_specify_application,
                        application_ids: this.formData.application_ids,
                        is_landscape: this.formData.is_landscape

                    },
                    product_ids: []
                }
                if (this.selectedProduct && this.selectedProduct.length) {
                    params.product_ids = this.selectedProduct.map(item => item.id)
                }
                let resCode, resMsg;
                if (this.formData.id) {
                    params.share_live_room.id = this.formData.id
                    const saveRes = await saveShareLiveRoom(params)
                    resCode = saveRes.code
                    resMsg = saveRes.msg
                } else {
                    const createRes = await createShareLiveRoom(params)
                    resCode = createRes.code
                    resMsg = createRes.msg
                }
                if (resCode === 0) {
                    this.$message.success(resMsg);
                    this.handleClose();
                    this.$emit('reLoad')
                }
            })
        }
    }
}
</script>
<style scoped lang="scss">
.select-product-item {
  width: 90px;
  margin-right: 10px;
  margin-bottom: 20px;

  p {
    line-height: 20px;
  }
}
</style>