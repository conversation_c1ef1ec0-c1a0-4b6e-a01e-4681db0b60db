package service

import (
	joinModel "convergence/model"
	joinpay_res "convergence/response"
	joinService "convergence/service"
	"encoding/json"
	"errors"
	gmq "finance/gongmallMq"
	"finance/model"
	"finance/request"
	"finance/response"
	"fmt"
	model2 "gin-vue-admin/admin/model"
	log2 "log"
	orderModel "order/model"
	"os"
	pmodel "payment/model"
	"strconv"
	"strings"
	"time"
	model3 "user/model"
	"yz-go/component/log"
	"yz-go/config"
	model5 "yz-go/model"
	request2 "yz-go/request"
	"yz-go/source"
	"yz-go/utils"

	"github.com/360EntSecGroup-Skylar/excelize"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func BalanceRecharge(param model.UpdateBalance) (err error) {
	parama := model.UpdateBalance{
		Uid:           param.Uid,
		Amount:        param.Amount,
		Type:          param.Type,
		OperationType: param.OperationType,
		Action:        param.Action,
	}
	err = UpdateSettlementBalance(parama)
	log2.Println("充值开始")
	return
}

// 余额记录检测
func BalanceCheck(param model.UpdateBalance) (err error) {
	err = ExistUserBalance(param)
	return
}

// StrToUInt string 转int
func StrToUInt(str string) uint {
	i, e := strconv.Atoi(str)
	if e != nil {
		return 0
	}
	return uint(i)
}
func JoinBalanceRecharge(param model.UserTopUp) (err error, res map[string]interface{}) {
	if param.PayType <= 0 {
		err = errors.New("支付类型不能为空")
		return
	}
	var paySN = strconv.Itoa(int(param.Uid)) + strconv.Itoa(int(time.Now().Unix()))
	var count int64
	source.DB().Model(model.UserTopUp{}).Debug().Where("pay_sn", paySN).Count(&count)

	fmt.Println("查询数量", count)
	if count > 0 {
		err = errors.New("请勿使用重复支付单")
		return
	}

	param.PaySN = StrToUInt(paySN)
	param.RemainingAmount = param.Amount
	var uniPay joinModel.UniPay
	uniPay.P2_OrderNo = paySN
	uniPay.P3_Amount = Fen2Yuan(param.Amount)
	uniPay.P7_Mp = param.PayType
	log.Log().Info("开始充值")
	err = source.DB().Create(&param).Error
	if err == nil {
		err, res = joinService.GetPayQrCode(uniPay)

	}
	return
}

func CheckBalance(withdraw request.WithdrawalApply) (err error) {

	if withdraw.WithdrawalAmount <= 0 {
		err = errors.New("提现金额不能小于0")
		return
	}

	if withdraw.WithdrawalType == 1 {

		err = source.DB().Model(model.SupplierSettlement{}).Where("status=1 and settlement_amount >0 and  withdrawal_status<2 and supplier_id=?", withdraw.SupplierId).Pluck("id", &withdraw.IDS).Error
		if err != nil || len(withdraw.IDS) <= 0 {
			err = errors.New("未查询到可提现数据")
			return
		}

		var totalSettlementAmount uint
		err = source.DB().Model(model.SupplierSettlement{}).Select("sum(settlement_amount) as total_settlement_amount").Where("id in (?)", withdraw.IDS).First(&totalSettlementAmount).Error
		if err != nil {
			return
		}

		if totalSettlementAmount < withdraw.WithdrawalAmount {
			err = errors.New("提现金额大于可结算金额")
			return
		}

		if totalSettlementAmount != withdraw.WithdrawalAmount {
			err = errors.New("提现金额不等于结算金额")
			return
		}

		var balance model.AccountBalance
		err = source.DB().Model(model.AccountBalance{}).Where("uid=? and type=2", withdraw.UserId).First(&balance).Error
		if err != nil {
			return
		}
		if balance.SettlementBalance < withdraw.WithdrawalAmount {
			err = errors.New("可提现余额不足")
			return
		}
	} else if withdraw.WithdrawalType == 2 {
		var income model.UserIncome
		err = source.DB().Where("user_id=?", withdraw.UserId).First(&income).Error
		if err != nil {
			return
		}
		if income.IncomeAmount < withdraw.WithdrawalAmount {
			err = errors.New("可提收入不足")
			return
		}
	}

	return

}

func GetWithdrawList(userId uint, info request2.WithdrawListPageInfo) (err error, data []model.Withdrawal, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(model.Withdrawal{})
	if info.RemitStatus != nil {
		db.Where("remit_status = ?", info.RemitStatus)
	}
	if info.WithdrawalStatus != nil {
		db.Where("withdrawal_status = ?", info.WithdrawalStatus)
	}
	// 待审核
	if info.Status == 1 {
		db.Where("withdrawal_status=0")
	}
	// 代打款
	if info.Status == 2 {
		db.Where("withdrawal_status=1 and remit_status=0")
	}
	// 打款中
	if info.Status == 3 {
		db.Where("withdrawal_status=1 and remit_status=3")
	}
	// 已完成
	if info.Status == 4 {
		db.Where("withdrawal_status=1 and remit_status=1")
	}
	// 已驳回
	if info.Status == 5 {
		db.Where("withdrawal_status=3")
	}
	// 已失效
	if info.Status == 6 {
		db.Where("withdrawal_status=2")
	}
	db.Where("user_id=?", userId).Count(&total)
	err = db.Limit(limit).Offset(offset).Order("id desc").Preload("WithdrawalOperation").Find(&data).Error
	return
}

func CreateWithSett(tx *gorm.DB, withdraw request.WithdrawalApply) (err error) {
	var settBalance model.SettlementBalance
	var withDetail []model.WithdrawalDetail
	settBalance.WithdrawalID = withdraw.ID

	err = source.DB().Model(model.SupplierSettlement{}).Where("status=1 and settlement_amount >0 and  withdrawal_status<2 and supplier_id=?", withdraw.SupplierId).Pluck("id", &withdraw.IDS).Error
	if err != nil || len(withdraw.IDS) <= 0 {
		err = errors.New("未查询到可提现数据")
		return
	}

	for _, item := range withdraw.IDS {
		withDetail = append(withDetail, model.WithdrawalDetail{
			WithdrawalID:         withdraw.ID,
			SupplierSettlementID: uint(item),
		})
	}
	err = tx.Create(&withDetail).Error
	if err != nil {
		log.Log().Error("err", zap.Any("插入提现详情记录失败", err))

		return
	}

	err = tx.Model(model.SupplierSettlement{}).Where("id in ?", withdraw.IDS).Update("withdrawal_status", 2).Error
	if err != nil {
		log.Log().Error("err", zap.Any("err", err))
		return

	}

	return
}

func DeductServiceCharges(withdrawalAmount uint) (charges uint, err error) {

	var withSetting model.WithdrawalSetting
	err, withSetting = GetWithdrawalSetting()
	if err != nil {
		return
	}
	fee := float64(withSetting.WithdrawalCharge) / 10000
	Amount := float64(withdrawalAmount)
	charges = uint(Amount * fee)

	return

}

func SupplierSettlement(info request.SettlementSearch) (ssettlement []model.SupplierSettlement, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(model.SupplierSettlement{})

	db.Where("supplier_settlements.status=1 and supplier_settlements.supplier_id=? and withdrawal_status<2", info.SupplierID).Count(&total)
	err = db.Preload(clause.Associations).Limit(limit).Offset(offset).Order("id desc").Find(&ssettlement).Error

	return

}

func GongMallGetSetting() (err error, sysSetting model5.SysSetting) {
	err = source.DB().Table("sys_settings").Where("`key` = ?", "gongmall").First(&sysSetting).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先保存设置")
	}
	return
}

func GongMall() (err error) {
	var setting model.Setting
	_, setttingData := GongMallGetSetting()
	err = json.Unmarshal([]byte(setttingData.Value), &setting)
	if err != nil {
		err = errors.New("提现插件配置异常")
		return
	}

	if setting.Enable != "1" {
		log.Log().Info("GongMall  not Enable：", zap.Any("", setting))

		err = errors.New("未开启此提现方式")
		return
	}

	return
}

func Withdraw(withdraw request.WithdrawalApply) (err error) {

	if withdraw.WithdrawalType == 2 { //收入提现
		if withdraw.WithdrawalMode > 2 && withdraw.WithdrawalMode < 7 { //工猫插件提现
			err = GongMall()
			if err != nil {
				return
			}
		}

	}

	var supplier model.Supplier
	source.DB().Where("uid=?", withdraw.UserId).First(&supplier)
	withdraw.SupplierId = supplier.ID

	if err = CheckBalance(withdraw); err != nil { //检测收入 余额 是否可提现
		return
	}
	withdraw.OrderSn = GetOrderNo()
	if withdraw.WithdrawalType == 1 {
		err = source.DB().Transaction(func(tx *gorm.DB) error {
			withdraw.IncomeAmount = withdraw.WithdrawalAmount
			var withdrawal = model.Withdrawal{
				OrderSn:          withdraw.OrderSn,
				IncomeAmount:     withdraw.WithdrawalAmount,
				WithdrawalAmount: withdraw.WithdrawalAmount,
				UserId:           withdraw.UserId,
				UserBankID:       withdraw.UserBankID,
				WithdrawalType:   withdraw.WithdrawalType,
				WithdrawalMode:   withdraw.WithdrawalMode,
				SupplierId:       withdraw.SupplierId,
			}
			if withdraw.WithdrawalType == 1 {
				settingErr, setting := GetSupplierWithdrawal()
				if settingErr != nil {
					log.Log().Error("Withdraw err ", zap.Any("err", settingErr))
				}
				if setting.IsInvoice == 1 {
					withdrawal.InvoiceStatus = 1
				}
			}

			err = tx.Create(&withdrawal).Error
			if err != nil {
				return err
			}
			err = tx.Model(model.AccountBalance{}).Where("uid = ?", withdraw.UserId).Where("type = ?", 2).Update("settlement_balance", gorm.Expr("settlement_balance - ?", withdraw.WithdrawalAmount)).Error
			if err != nil {
				return err
			}
			tx.Model(model.SettlementBalance{}).Where("withdrawal_id=?", 0).Where("uid=?", withdraw.UserId).Update("withdrawal_id", withdrawal.ID)
			if err != nil {
				return err
			}
			withdraw.ID = withdrawal.ID
			err = CreateWithSett(tx, withdraw)
			if err != nil {
				return err
			}
			// 返回 nil 提交事务
			return nil
		})
		if err != nil {
			return err
		}

	}

	/*收入提现处理*/
	if withdraw.WithdrawalType == 2 {
		var IncomeSetting model.IncomeWithdrawalSetting
		err, IncomeSetting = GetIncomeWithdrawalSetting()
		if err != nil {
			return
		}
		fee := float64(IncomeSetting.WithdrawalCharge) / 10000

		var countGradsList = len(IncomeSetting.GradsList)
		//劳务费计算
		var serviceTax float64
		if countGradsList == 0 {
			serviceTax = float64(IncomeSetting.WithdrawalServiceTax) / 10000
		} else {
			for i, item := range IncomeSetting.GradsList {
				if i == 0 {
					if withdraw.WithdrawalAmount >= 0 && withdraw.WithdrawalAmount < item.Amount {
						serviceTax = float64(item.Ratio) / 10000
						break
					}
				} else {
					if countGradsList > i+1 {
						if withdraw.WithdrawalAmount >= IncomeSetting.GradsList[i-1].Amount && withdraw.WithdrawalAmount < item.Amount {
							serviceTax = float64(item.Ratio) / 10000
							break
						}
					} else {
						if withdraw.WithdrawalAmount >= item.Amount {
							serviceTax = float64(item.Ratio) / 10000
							break
						} else {
							row := IncomeSetting.GradsList[i-1]
							if withdraw.WithdrawalAmount >= row.Amount {
								serviceTax = float64(row.Ratio) / 10000
								break
							}
						}
					}
				}
			}
		}

		withdrawalAmount := float64(withdraw.WithdrawalAmount)
		withdraw.PoundageAmount = uint(withdrawalAmount * fee)
		withdraw.ServiceTax = uint(withdrawalAmount * serviceTax)
		withdraw.IncomeAmount = withdraw.WithdrawalAmount - withdraw.PoundageAmount - withdraw.ServiceTax
		if err != nil {
			return
		}

		err = source.DB().Transaction(func(tx *gorm.DB) error {
			var withdrawal = model.Withdrawal{
				OrderSn:          withdraw.OrderSn,
				IncomeAmount:     withdraw.WithdrawalAmount - (withdraw.PoundageAmount + withdraw.ServiceTax),
				WithdrawalAmount: withdraw.WithdrawalAmount,
				UserId:           withdraw.UserId,
				UserBankID:       withdraw.UserBankID,
				WithdrawalType:   withdraw.WithdrawalType,
				WithdrawalMode:   withdraw.WithdrawalMode,
				SupplierId:       withdraw.SupplierId,
				PoundageAmount:   withdraw.PoundageAmount,
				ServiceTax:       withdraw.ServiceTax,
			}
			err = tx.Create(&withdrawal).Error
			if err != nil {
				return err
			}
			var income model.UserIncome
			err = tx.Where("user_id=?", withdraw.UserId).First(&income).Error
			if err != nil {
				return err
			}
			income.IncomeAmount = income.IncomeAmount - withdraw.WithdrawalAmount
			err = tx.Save(&income).Error
			if err != nil {
				return err
			}

			//var userIncomeDetail model.UserIncomeDetails
			var IDS []uint
			err = tx.Model(model.UserIncomeDetails{}).Where("withdrawal_status<2 and user_id=?", withdraw.UserId).Pluck("id", &IDS).Error
			if err != nil {
				return err
			}
			//var withDetail []model.WithdrawalDetail
			//for _, item := range IDS {
			//	withDetail = append(withDetail, model.WithdrawalDetail{
			//		WithdrawalID:        withdraw.ID,
			//		UserIncomeDetailsID: item,
			//	})
			//}
			//err = tx.Create(&withDetail).Error
			if err != nil {
				log.Log().Error("err", zap.Any("插入提现详情记录失败", err))
				return err
			}
			//"withdrawal_status", 2
			err = tx.Model(model.UserIncomeDetails{}).Where("id in ?", IDS).UpdateColumns(map[string]interface{}{"withdrawal_status": 2, "withdrawal_id": withdrawal.ID}).Error
			if err != nil {
				log.Log().Error("err", zap.Any("err", err))
				return err

			}

			return nil
		})
		if err != nil {
			return
		}

	}
	return
}

func GetUserIncomeBalance(uid uint) (err error, income model.UserIncome) {
	err = source.DB().Preload("User").Where("user_id=?", uid).First(&income).Error
	if err != nil {
		return
	}
	return

}
func GetUserBankList(uid uint) (err error, bank []model3.UserBank) {
	err = source.DB().Where("user_id=? ", uid).Find(&bank).Error
	return

}
func DeleteUserBank(id, uid uint) (err error) {
	err = source.DB().Where("id=? and user_id=?", id, uid).Delete(&model3.UserBank{}).Error
	return

}
func SaveUserBank(bank model3.UserBank) (err error) {
	if bank.IsDefault == 1 {
		source.DB().Model(&model3.UserBank{}).Where("user_id = ?", bank.UserID).Update("is_default", 0)
	} else {
		source.DB().Model(&model3.UserBank{}).Where("id = ?", bank.ID).Update("is_default", 0)

	}
	// 手动调用钩子
	if err = bank.BeforeSave(source.DB()); err != nil {
		return err
	}
	err = source.DB().Updates(&bank).Error
	return

}

func GetIncomeFee(withdraw model.Withdrawal) (err error, fee, serviceTax float64) {
	var IncomeSetting model.IncomeWithdrawalSetting
	err, IncomeSetting = GetIncomeWithdrawalSetting()
	if err != nil {
		return
	}
	fee = float64(IncomeSetting.WithdrawalCharge) / 10000

	var countGradsList = len(IncomeSetting.GradsList)
	//劳务费计算
	//var serviceTax float64
	if countGradsList == 0 {
		serviceTax = float64(IncomeSetting.WithdrawalServiceTax) / 10000
	} else {
		for i, item := range IncomeSetting.GradsList {
			if i == 0 {
				if withdraw.WithdrawalAmount >= 0 && withdraw.WithdrawalAmount < item.Amount {
					serviceTax = float64(item.Ratio) / 10000
					break
				}
			} else {
				if countGradsList > i+1 {
					if withdraw.WithdrawalAmount >= IncomeSetting.GradsList[i-1].Amount && withdraw.WithdrawalAmount < item.Amount {
						serviceTax = float64(item.Ratio) / 10000
						break
					}
				} else {
					if withdraw.WithdrawalAmount >= item.Amount {
						serviceTax = float64(item.Ratio) / 10000
						break
					} else {
						row := IncomeSetting.GradsList[i-1]
						if withdraw.WithdrawalAmount >= row.Amount {
							serviceTax = float64(row.Ratio) / 10000
							break
						}
					}
				}
			}
		}
	}

	return
}
func WithdrawStatus(withdraw request.WithdrawalExamination, uid uint, ip string) (err error) {

	var withdrawModel model.Withdrawal
	err = source.DB().Where("id=?", withdraw.ID).First(&withdrawModel).Error
	if err != nil {
		return
	}

	// 批量更新提现状态
	if len(withdraw.ListStatus) > 0 {
		if withdrawModel.WithdrawalType == 1 {
			// 批量更新供应商结算记录
			batchUpdateWithdrawalStatus("supplier_settlements", withdraw.ListStatus)
		} else if withdrawModel.WithdrawalType == 2 {
			// 批量更新用户收入明细记录
			batchUpdateWithdrawalStatus("user_income_details", withdraw.ListStatus)
		}
	}

	if withdrawModel.WithdrawalType == 2 && withdraw.RejectedAmount > 0 {
		source.DB().Model(model.UserIncome{}).Where("user_id=?", withdrawModel.UserId).Update("income_amount", gorm.Expr("income_amount + ?", withdraw.RejectedAmount))
	}

	if withdrawModel.WithdrawalAmount < withdraw.InvalidAmount+withdraw.RejectedAmount {
		err = errors.New("扣除金额不能大于提现申请金额")
		return
	}

	if withdraw.RejectedAmount > 0 && withdrawModel.WithdrawalType == 1 {
		source.DB().Model(model.AccountBalance{}).Where("uid=? and type=2", withdrawModel.UserId).Update("settlement_balance", gorm.Expr("settlement_balance + ?", withdraw.RejectedAmount))
	}
	// 通过withdraw.ListStatus里面各个状态来判断提现状态(全部通过-通过，部分通过or部分驳回or部分无效-通过，全部无效-无效，全部驳回-驳回，部分驳回or部分无效-驳回。提现状态：0待审核，1通过，2无效，3驳回)
	// 如果其中有一个是通过的，那么提现状态就是通过
	// 如果其中没有一个是通过的，且有一个是驳回的，那么提现状态就是驳回；否则提现状态就是无效
	// 定义提现状态常量 0  未提现    1 驳回     2 已申请  3 通过  4  无效
	const (
		StatusApproved = 3 // 通过
		StatusInvalid  = 4 // 无效
		StatusRejected = 1 // 驳回
	)
	// 通过
	Approved := false
	// 驳回
	Rejected := false
	// 无效
	Invalid := false
	for _, item := range withdraw.ListStatus {
		if item.Status == StatusApproved {
			Approved = true
		} else if item.Status == StatusRejected {
			Rejected = true
		} else if item.Status == StatusInvalid {
			Invalid = true
		}
	}
	// 如果其中有一个是通过的，那么提现状态就是通过
	// else
	// 如果其中没有一个是通过的，且有一个是驳回的，那么提现状态就是驳回；否则提现状态就是无效
	// 通过withdraw.ListStatus里面各个状态来判断提现状态(全部通过-通过，部分通过or部分驳回or部分无效-通过，全部无效-无效，全部驳回-驳回，部分驳回or部分无效-驳回。提现状态：0待审核，1通过，2无效，3驳回)
	if Approved {
		// 通过
		withdrawModel.WithdrawalStatus = 1
	} else {
		// 驳回
		if Rejected {
			withdrawModel.WithdrawalStatus = 3
		} else {
			// 无效
			if Invalid {
				withdrawModel.WithdrawalStatus = 2
			}
			// 无效
			withdrawModel.WithdrawalStatus = 2
		}
	}
	// 通过withdraw.ListStatus里面各个状态来判断提现状态(全部通过-通过，部分通过or部分驳回or部分无效-通过，全部无效-无效，全部驳回-驳回，部分驳回or部分无效-驳回 0待审核，1通过，2无效，3驳回)
	// ListStatus.status 4-无效，3-通过，1-驳回，0-未提现，2-已申请
	// 通过提现状态来判断打款状态(通过-待打款，无效-无效，驳回-驳回 0待打款，1已打款,3打款中，4无需打款)
	// withdrawModel.WithdrawalStatus = withdraw.WithdrawalStatus
	if withdrawModel.WithdrawalStatus == 1 {
		withdrawModel.RemitStatus = 0
	} else {
		withdrawModel.RemitStatus = 4
	}
	IncomeAmount := withdrawModel.WithdrawalAmount - withdraw.InvalidAmount - withdraw.RejectedAmount //应收入金额
	if IncomeAmount > 0 {                                                                             //提现金额实际大于0  才会计算
		withdrawModel.PoundageAmount, err = DeductServiceCharges(withdrawModel.IncomeAmount) //计算手续费

	}
	if withdrawModel.PoundageAmount < 0 {
		withdrawModel.PoundageAmount = 0
	}
	log.Log().Info("withdrawModel", zap.Any("info", withdrawModel), zap.Any("info", withdraw))
	if withdrawModel.WithdrawalType == 2 {
		_, fee, serviceTax := GetIncomeFee(withdrawModel)
		withdrawModel.PoundageAmount = uint(float64(IncomeAmount) * fee)
		withdrawModel.ServiceTax = uint(float64(IncomeAmount) * serviceTax)
		log.Log().Info("fee", zap.Any("fee", fee), zap.Any("serviceTax", serviceTax))

	}

	withdrawModel.IncomeAmount = IncomeAmount - withdrawModel.PoundageAmount - withdrawModel.ServiceTax //收入金额扣除手续费

	if withdraw.WithdrawalStatus == 2 {
		withdrawModel.IncomeAmount = 0
		//withdrawModel.PoundageAmount = 0
		//withdrawModel.ServiceTax = 0
	}

	err = source.DB().Save(&withdrawModel).Error
	if err != nil {
		log.Log().Error("withdrawModel save err", zap.Any("err", err))
	}
	var operationRecord model.WithdrawalOperationRecord
	var user model2.SysUser
	err = source.DB().Where("id=?", uid).First(&user).Error
	if err != nil {
		log.Log().Error("user First err", zap.Any("err", err))
	}
	operationRecord.WithdrawalID = withdrawModel.ID
	operationRecord.OperatorName = user.Username
	operationRecord.OperatorType = "确认审核"
	operationRecord.OperatorIP = ip
	operationRecord.Remarks = withdraw.Remarks
	operationRecord.InvalidAmount = withdraw.InvalidAmount
	operationRecord.RejectedAmount = withdraw.RejectedAmount
	operationRecord.WithdrawalStatus = withdraw.WithdrawalStatus
	err = source.DB().Create(&operationRecord).Error
	if err != nil {
		log.Log().Error("operationRecord Create err", zap.Any("err", err))
	}
	return

}

func InvoiceStatus(withdraw model.Withdrawal, uid uint, ip string) (err error) {
	var withdrawSelect model.Withdrawal

	err = source.DB().Where("id=?", withdraw.ID).First(&withdrawSelect).Error
	if err != nil {
		return
	}
	withdraw.InvoiceStatus = 2
	err = source.DB().Updates(&withdraw).Error
	var operationRecord model.WithdrawalOperationRecord
	var user model2.SysUser
	err = source.DB().Where("id=?", uid).First(&user).Error
	if err != nil {
		log.Log().Error("user First err", zap.Any("err", err))
	}
	operationRecord.WithdrawalID = withdraw.ID
	operationRecord.OperatorName = user.Username
	operationRecord.OperatorType = "确认开票"
	operationRecord.OperatorIP = ip

	err = source.DB().Create(&operationRecord).Error
	if err != nil {
		log.Log().Error("operationRecord Create err", zap.Any("err", err))
		return
	}

	return

}

func RemitStatus(withdraw model.Withdrawal, uid uint, ip string) (err error) {

	var Ids []uint
	withdrawalDetailErr := source.DB().Model(&model.WithdrawalDetailModel{}).Where("withdrawal_id=?", withdraw.ID).Pluck("supplier_settlement_id", &Ids).Error

	if withdrawalDetailErr != nil {
		log.Log().Error("RemitStatus withdrawalDetailErr", zap.Any("err", withdrawalDetailErr))
	}

	if len(Ids) > 0 {
		supplierSettlementUpdateErr := source.DB().Model(&model.SupplierSettlement{}).Where("id in?", Ids).Update("remit_status", 1).Error
		if supplierSettlementUpdateErr != nil {
			log.Log().Error("RemitStatus supplierSettlementUpdateErr", zap.Any("err", supplierSettlementUpdateErr))

		}

	}

	var withdrawSelect model.Withdrawal

	err = source.DB().Where("id=?", withdraw.ID).First(&withdrawSelect).Error
	if err != nil {
		return
	}

	withdraw.PayTime = &source.LocalTime{Time: time.Now()}
	err = source.DB().Updates(&withdraw).Error

	var operationRecord, shoperationRecord model.WithdrawalOperationRecord
	source.DB().Where("withdrawal_id=? and operator_type=?", withdraw.ID, "确认审核").First(&shoperationRecord)
	var user model2.SysUser
	err = source.DB().Where("id=?", uid).First(&user).Error
	if err != nil {
		log.Log().Error("user First err", zap.Any("err", err))
	}
	operationRecord.WithdrawalID = withdraw.ID
	operationRecord.OperatorName = user.Username
	operationRecord.OperatorType = "确认打款"
	operationRecord.OperatorIP = ip
	operationRecord.Remarks = withdraw.Remarks
	operationRecord.InvalidAmount = shoperationRecord.InvalidAmount
	operationRecord.RejectedAmount = shoperationRecord.RejectedAmount
	operationRecord.WithdrawalStatus = 1
	err = source.DB().Create(&operationRecord).Error
	if err != nil {
		log.Log().Error("operationRecord Create err", zap.Any("err", err))
		return
	}

	if withdrawSelect.WithdrawalType == 2 {
		if withdrawSelect.WithdrawalMode > 2 && withdrawSelect.WithdrawalMode < 7 {
			var data model.MqData
			data.UserBankId = int(withdrawSelect.UserBankID)
			data.Uid = withdrawSelect.UserId
			data.Amount = withdrawSelect.IncomeAmount
			data.Type = int(withdrawSelect.WithdrawalMode)
			data.OrderSN = withdrawSelect.OrderSn
			err = gmq.PublishMessage(data)
			if err != nil {
				log.Log().Error("PublishMessage MqData ", zap.Any("err", err))
				return
			}
		} else if withdrawSelect.WithdrawalMode == 7 { //提现到站内余额 ,站内余额更新增加余额操作

			var account model.AccountBalance
			err = source.DB().Where("uid=? and type=2", withdrawSelect.UserId).First(&account).Error
			log.Log().Debug("收入提现到站内余额", zap.Any("info", account))

			if err != nil {
				log.Log().Error("PublishMessage MqData ", zap.Any("err", err))
				return
			}
			account.PurchasingBalance = account.PurchasingBalance + withdrawSelect.IncomeAmount
			log.Log().Debug("收入提现到站内余额增加余额", zap.Any("info", account.PurchasingBalance))

			err = source.DB().Save(&account).Error
			if err != nil {
				log.Log().Error("WithdrawalMode 7 Save AccountBalance err ", zap.Any("err", err))
				return

			}

		}
	}

	return

}
func WithdrawList(info request.WithdrawSearch) (err error, withdraw []model.Withdrawal, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(model.Withdrawal{})
	// 供应商名称搜索
	if info.SupplierName != "" {
		var supplierIDs []uint
		err = source.DB().Model(model.Supplier{}).Where("name like ?", "%"+info.SupplierName+"%").Pluck("id", &supplierIDs).Error
		if err != nil {
			db = db.Where("supplier_id = ?", 0)
		}
		db.Where("supplier_id in (?)", supplierIDs)
	}
	// 会员搜索 0-请选择，1-会员ID，2-会员昵称，3-会员手机号
	if info.UserType != 0 && info.UserKey != "" {
		if info.UserType == 1 {
			db.Where("user_id=?", info.UserKey)
		} else if info.UserType == 2 {
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+info.UserKey+"%").Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("user_id = ?", 0)
			}
			db.Where("user_id in (?)", uids)
		} else {
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("username like ?", "%"+info.UserKey+"%").Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("user_id = ?", 0)
			}
			db.Where("user_id in (?)", uids)
		}
	}
	// 提现单号
	if info.OrderSn != "" {
		db.Where("order_sn=?", info.OrderSn)
	}
	// 类型
	if info.WithdrawalType > 0 {
		db.Where("withdrawal_type=?", info.WithdrawalType)
	}
	// 提现方式
	if info.WithdrawalMode > 0 {
		db.Where("withdrawal_mode=?", info.WithdrawalMode)
	}
	// 供应商
	if info.SupplierId > 0 {
		db.Where("supplier_id=?", info.SupplierId)
	}
	// 申请时间
	if info.StartTime != "" {
		db = db.Where("created_at >= ?", info.StartTime)
	}
	if info.EndTime != "" {
		db = db.Where("created_at <= ?", info.EndTime)
	}

	if info.UserId > 0 {
		db.Where("user_id=?", info.UserId)
	}

	if info.Status != nil {
		// 待审核
		if *info.Status == 1 {
			db.Where("withdrawal_status=0")
		}
		// 代打款
		if *info.Status == 2 {
			db.Where("withdrawal_status=1 and remit_status=0")
		}
		// 打款中
		if *info.Status == 3 {
			db.Where("withdrawal_status=1 and remit_status=3")
		}
		// 已完成
		if *info.Status == 4 {
			db.Where("withdrawal_status=1 and remit_status=1")
		}
		// 已驳回
		if *info.Status == 5 {
			db.Where("withdrawal_status=3")
		}
		// 已失效
		if *info.Status == 6 {
			db.Where("withdrawal_status=2")
		}
	}

	db.Count(&total)
	err = db.Preload("SingPay").Preload("Supplier").Preload("WithdrawalOperation").Preload("User").Preload("Bank").Limit(limit).Offset(offset).Order("id desc").Find(&withdraw).Error

	return

}

func WithdrawUnCompleteCount() (total int64, err error) {
	db := source.DB().Model(model.Withdrawal{})

	db.Where("withdrawal_status=0")

	err = db.Count(&total).Error
	return
}
func WithdrawDetail(info request.WithdrawSearch) (err error, withdraw model.Withdrawal) {

	err = source.DB().Preload("User.UserLevelInfo").Preload("WithdrawalDetail.UserIncomeDetails").Preload("WithdrawalDetail.SupplierSettlement").Preload("WithdrawalDetail.SupplierSettlement.Order").Preload(clause.Associations).Where("id=?", info.ID).First(&withdraw).Error

	return

}

func BillDetail(info request.BillDetailSearch) (err error, list interface{}) {

	var ids []uint

	err = source.DB().Model(&model.WithdrawalDetail{}).Where("withdrawal_id=?", info.ID).Pluck("supplier_settlement_id", &ids).Error
	if err != nil {
		return
	}
	if len(ids) == 0 {
		return
	}

	db := source.DB().Model(&model.SupplierSettlement{})

	db.Where("id in (?)", ids)
	var supplierSettlement []model.SupplierSettlement
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Select("supplier_settlements.*").Preload("Order").Preload("Supplier").Order("supplier_settlements.id desc").Find(&supplierSettlement).Error
	return err, supplierSettlement

}

func GetPayStatus(param model.PayStatus) (err error, data interface{}) {
	var payInfo = pmodel.PayInfo{}
	data = 0
	err = source.DB().Where("pay_sn = ?", param.PaySN).First(&payInfo).Error
	if payInfo.Status == 1 {
		data = 1
	}
	return

}

func GetRechargeStatus(param model.PayStatus) (err error, data interface{}) {
	var payInfo = model.UserTopUp{}
	data = 0
	err = source.DB().Where("pay_sn = ?", param.PaySN).First(&payInfo).Error
	if payInfo.PayStatus == 1 {
		data = 1
	}
	return

}

func GetStationRechargeStatus(param model.PayStatus) (err error, data interface{}) {
	var payInfo = pmodel.RechargeBalance{}
	data = 0
	err = source.DB().Where("pay_sn = ?", param.PaySN).First(&payInfo).Error
	if payInfo.Status == 1 {
		data = 1
	}
	return

}

func UserBalance(param model.AccountBalance) (err error, resBalance []model.AccountBalance) {

	source.DB().Where("uid = ?", param.Uid).Find(&resBalance)
	return

}
func UserRecharge(param model.UserTopUp) (err error, total int64, resRecharge []model.UserTopUp) {
	db := source.DB().Model(model.UserTopUp{}).Where("uid = ?", param.Uid).Where("pay_status = ?", 1).Count(&total)

	db.Order("id desc").Find(&resRecharge)
	return

}

func UserRecharges(param model.UserTopUp) (err error, total int64, resRecharge []model.PurchasingBalance) {
	db := source.DB().Model(&resRecharge).Preload("User").Where("uid = ? and pay_type=2", param.Uid).Where("business_type = ?", 4).Count(&total)

	db.Find(&resRecharge)
	return

}

// TransferAccounts 转账分账service
func TransferAccounts(transfer model.TransferAccounts, orderID uint) (err error) {

	if transfer.Amount <= 0 {
		err = errors.New("分账金额为0")
		return
	}
	//var balance model.AccountBalance
	//err = source.DB().Where("uid = ?", transfer.TransferUid).Where("type = ? ", 1).First(&balance).Error
	//if balance.ID <= 0 {
	//	err=errors.New("TransferAccounts查询余额失败")
	//	log.Log().Error("TransferAccounts查询余额失败!", zap.Any("err", err))
	//	return
	//}

	var altMchNo string
	var supplierAccount model.AccountApply

	err = source.DB().Where("member_id = ?", transfer.CollectUid).First(&supplierAccount).Error //查询供应商汇聚分帐编号
	if supplierAccount.ID <= 0 && transfer.CollectUid > 0 {                                     //判断是供应商的， 切供应商开户id未查询到
		log.Log().Error("TransferAccounts——supplierAccount查询供应商开户失败!", zap.Any("err", err))
		err = errors.New("查询供应商开户失败")
		return err
	}

	if transfer.CollectUid > 0 { //供应商
		altMchNo = supplierAccount.AltMchNo
	} else { //平台
		altMchNo = config.Config().Join.AltMchNo

	}

	var userTopUp []model.UserTopUp
	var countAmount = transfer.Amount
	var deductionAmount uint
	source.DB().Where("pay_status = ?", 1).Where("remaining_amount > ?", 0).Where("pay_type =  ?", 1).Where("uid = ?", transfer.TransferUid).Order("remaining_amount asc").Find(&userTopUp)

	if len(userTopUp) <= 0 {
		err = errors.New("没有可分账充值记录")
		return
	}

	var joinData = joinModel.MoreSeparateData{}
	for _, item := range userTopUp {

		if countAmount == 0 {
			break
		}

		orderNo := GetOrderNo()
		var amount string
		if item.RemainingAmount < countAmount {
			amount = Fen2Yuan(item.RemainingAmount)
			countAmount = countAmount - item.RemainingAmount
			deductionAmount = item.RemainingAmount
		} else {
			amount = Fen2Yuan(countAmount)
			deductionAmount = countAmount
			countAmount = 0
		}

		infoData := []joinModel.AltInfo{
			{AltMchNo: altMchNo, AltAmount: amount},
		}
		joinData = joinModel.MoreSeparateData{
			AltOrderNo:    orderNo,
			MchOrderNo:    strconv.Itoa(int(item.PaySN)),
			AltThisAmount: amount,
			AltInfo:       infoData,
		}

		var res joinpay_res.Response
		err, res = joinService.MoreSeparateAccount(joinData)
		jsonData, _ := json.Marshal(res)
		postData, _ := json.Marshal(joinData)

		var sSettle model.SupplierSettlement
		log2.Println("金额扣除", deductionAmount)

		var separate joinModel.SeparateAccountingRecords
		var order orderModel.Order
		source.DB().Where("id= ?", orderID).First(&order)
		separate.Info = res.Data.BizMsg
		separate.ReqData = string(postData)
		separate.ResData = string(jsonData)
		separate.Amount = deductionAmount
		separate.OrderSN = order.OrderSN
		separate.SupplierID = transfer.CollectUid

		if "B100000" == res.Data.BizCode {
			separate.Status = 1
			source.DB().Model(model.UserTopUp{}).Where("id = ?", item.ID).Update("remaining_amount", gorm.Expr("remaining_amount - ?", deductionAmount))
			source.DB().Model(model.AccountBalance{}).Where("uid = ?", item.Uid).Where("type = ?", 1).Update("purchasing_balance", gorm.Expr("purchasing_balance - ?", deductionAmount))
			sSettle.Status = 1
			sSettle.SettlementTime = &source.LocalTime{Time: time.Now()}
			source.DB().Where("order_id = ?", orderID).Updates(&sSettle)
		}
		source.DB().Create(&separate)
	}

	return
}

func ExportSettlementBalance(info request.UserBalanceSearch) (err error, link string) {

	// 创建db
	db := source.DB().Model(&response.SettlementBalance{}).Preload("Order").Preload("Order.OrderItems").Preload("User").Preload("Supplier")

	if info.WithdrawalID > 0 {
		db.Where("settlement_balances.withdrawal_id=?", info.WithdrawalID)
	}

	if info.SupplierID > 0 {
		db.Where("settlement_balances.supplier_id=?", info.SupplierID)
	}
	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere string
	joinWhere = "INNER join suppliers on suppliers.id = settlement_balances.supplier_id"
	if info.Type == 1 && info.Username != "" {
		db.Where("settlement_balances.supplier_id=?", info.Username)
		//joinWhere = "INNER join suppliers on suppliers.id = settlement_balances.uid and suppliers.id=" + info.Username
	}
	if info.Type == 2 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = settlement_balances.supplier_id and suppliers.name LIKE " + "'%" + info.Username + "%'"

	}

	if info.MinBalance > 0 && info.MaxBalance > 0 {
		var min, max uint
		min = info.MinBalance
		max = info.MaxBalance

		db = db.Where("settlement_balances.amount BETWEEN ? AND ?", min, max)

	}
	if info.BusinessType > 0 {
		db = db.Where("settlement_balances.business_type = ?", info.BusinessType)

	}
	if info.OrderSn > 0 {
		db = db.Where("settlement_balances.order_sn = ?", info.OrderSn)

	}
	if info.TimeS != "" && info.TimeE != "" {
		db = db.Where("settlement_balances.created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}

	var balanceList []response.SettlementBalance

	err = db.Joins(joinWhere).Order("created_at DESC").Find(&balanceList).Error
	if err != nil {
		return
	}
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "id")
	f.SetCellValue("Sheet1", "B1", "账户名称")
	f.SetCellValue("Sheet1", "C1", "供应商ID")
	f.SetCellValue("Sheet1", "D1", "真实姓名")
	f.SetCellValue("Sheet1", "E1", "手机号")
	f.SetCellValue("Sheet1", "F1", "日期")
	f.SetCellValue("Sheet1", "G1", "订单编号")
	f.SetCellValue("Sheet1", "H1", "子订单id")
	f.SetCellValue("Sheet1", "I1", "商品名称")
	f.SetCellValue("Sheet1", "J1", "规格")
	f.SetCellValue("Sheet1", "K1", "单价(元)")
	f.SetCellValue("Sheet1", "L1", "数量")
	f.SetCellValue("Sheet1", "M1", "总价(元)")
	f.SetCellValue("Sheet1", "N1", "运费(元)")
	f.SetCellValue("Sheet1", "O1", "结算金额(元)")

	//f.SetCellValue("Sheet1", "H1", "收入/支出")

	i := 2
	var start int
	for _, v := range balanceList {
		//Fen2Yuan(v.Amount)
		start = i
		for k, orderItem := range v.Order.OrderItems {
			i = i + k
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), strconv.Itoa(int(orderItem.ID)))
			f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), orderItem.Title)
			f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), orderItem.SkuTitle)
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), Fen2Yuan(orderItem.Amount/orderItem.Qty))
			f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), orderItem.Qty)
			f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), Fen2Yuan(orderItem.Amount))
		}

		if len(v.Order.OrderItems) > 1 {
			f.MergeCell("Sheet1", "A"+strconv.Itoa(start), "A"+strconv.Itoa(i))
			f.MergeCell("Sheet1", "B"+strconv.Itoa(start), "B"+strconv.Itoa(i))
			f.MergeCell("Sheet1", "C"+strconv.Itoa(start), "C"+strconv.Itoa(i))
			f.MergeCell("Sheet1", "D"+strconv.Itoa(start), "D"+strconv.Itoa(i))
			f.MergeCell("Sheet1", "E"+strconv.Itoa(start), "E"+strconv.Itoa(i))
			f.MergeCell("Sheet1", "F"+strconv.Itoa(start), "F"+strconv.Itoa(i))

			f.MergeCell("Sheet1", "G"+strconv.Itoa(start), "G"+strconv.Itoa(i))
			f.MergeCell("Sheet1", "N"+strconv.Itoa(start), "N"+strconv.Itoa(i))
			f.MergeCell("Sheet1", "O"+strconv.Itoa(start), "O"+strconv.Itoa(i))
		}

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.Supplier.Name)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.SupplierID)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.Supplier.Realname)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Supplier.Mobile)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.CreatedAt.Format("2006-01-02 15:04:05"))

		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), strconv.Itoa(int(v.OrderSN)))
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), Fen2Yuan(v.Order.Freight))
		f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), Fen2Yuan(v.Amount))
		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("**************")
	path := config.Config().Local.Path + "/export_settlement"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "结算余额导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return
}

func ExportWithdrawal(info request.WithdrawSearch) (err error, link string) {

	db := source.DB().Model(model.Withdrawal{})

	// 供应商名称搜索
	if info.SupplierName != "" {
		var supplierIDs []uint
		err = source.DB().Model(model.Supplier{}).Where("name like ?", "%"+info.SupplierName+"%").Pluck("id", &supplierIDs).Error
		if err != nil {
			db = db.Where("supplier_id = ?", 0)
		}
		db.Where("supplier_id in (?)", supplierIDs)
	}
	// 会员搜索 0-请选择，1-会员ID，2-会员昵称，3-会员手机号
	if info.UserType != 0 && info.UserKey != "" {
		if info.UserType == 1 {
			db.Where("user_id=?", info.UserKey)
		} else if info.UserType == 2 {
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("nickname like ?", "%"+info.UserKey+"%").Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("user_id = ?", 0)
			}
			db.Where("user_id in (?)", uids)
		} else {
			var uids []uint
			err = source.DB().Model(&model.User{}).Where("username like ?", "%"+info.UserKey+"%").Pluck("id", &uids).Error
			if err != nil {
				db = db.Where("user_id = ?", 0)
			}
			db.Where("user_id in (?)", uids)
		}
	}
	// 提现单号
	if info.OrderSn != "" {
		db.Where("order_sn=?", info.OrderSn)
	}
	// 类型
	if info.WithdrawalType > 0 {
		db.Where("withdrawal_type=?", info.WithdrawalType)
	}
	// 提现方式
	if info.WithdrawalMode > 0 {
		db.Where("withdrawal_mode=?", info.WithdrawalMode)
	}
	// 供应商
	if info.SupplierId > 0 {
		db.Where("supplier_id=?", info.SupplierId)
	}
	// 申请时间
	if info.StartTime != "" {
		db = db.Where("created_at >= ?", info.StartTime)
	}
	if info.EndTime != "" {
		db = db.Where("created_at <= ?", info.EndTime)
	}

	if info.UserId > 0 {
		db.Where("user_id=?", info.UserId)
	}

	if info.Status != nil {
		// 待审核
		if *info.Status == 1 {
			db.Where("withdrawal_status=0")
		}
		// 代打款
		if *info.Status == 2 {
			db.Where("withdrawal_status=1 and remit_status=0")
		}
		// 打款中
		if *info.Status == 3 {
			db.Where("withdrawal_status=1 and remit_status=3")
		}
		// 已完成
		if *info.Status == 4 {
			db.Where("withdrawal_status=1 and remit_status=1")
		}
		// 已驳回
		if *info.Status == 5 {
			db.Where("withdrawal_status=3")
		}
		// 已失效
		if *info.Status == 6 {
			db.Where("withdrawal_status=2")
		}

	}

	var List []model.Withdrawal

	err = db.Preload(clause.Associations).Order("created_at DESC").Find(&List).Error
	if err != nil {
		return
	}
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "id")
	f.SetCellValue("Sheet1", "B1", "申请时间")
	f.SetCellValue("Sheet1", "C1", "提现编号")
	f.SetCellValue("Sheet1", "D1", "用户名")
	f.SetCellValue("Sheet1", "E1", "供应商")
	f.SetCellValue("Sheet1", "F1", "提现类型")
	f.SetCellValue("Sheet1", "G1", "提现方式")
	f.SetCellValue("Sheet1", "H1", "申请金额(元)")
	f.SetCellValue("Sheet1", "I1", "手续费(元)")
	f.SetCellValue("Sheet1", "J1", "劳务税(元)")
	f.SetCellValue("Sheet1", "K1", "驳回金额(元)")
	f.SetCellValue("Sheet1", "L1", "无效金额(元)")
	f.SetCellValue("Sheet1", "M1", "实际打款金额(元)")
	f.SetCellValue("Sheet1", "N1", "审核状态")
	f.SetCellValue("Sheet1", "O1", "打款状态")

	i := 2
	var withdrawalMode = func(withdrawalMode uint) string {
		if withdrawalMode == 1 {
			return "手动"
		} else if withdrawalMode == 1 {
			return "汇聚"
		} else {
			return ""
		}

	}

	var WithdrawalType = func(WithdrawalType uint) string {
		if WithdrawalType == 1 {
			return "余额"
		} else {
			return "收入"
		}

	}
	// 0待审核，1通过，2无效，3驳回
	var WithdrawalStatus = func(WithdrawalType uint) string {
		if WithdrawalType == 1 {
			return "已审核"
		} else if WithdrawalType == 2 {
			return "无效"
		} else if WithdrawalType == 3 {
			return "驳回"
		} else {
			return "未审核"
		}

	}
	// 0待打款，1已打款,2打款中，4无需打款
	var RemitStatusName = func(WithdrawalType uint) string {
		if WithdrawalType == 1 {
			return "已打款"
		} else if WithdrawalType == 2 {
			return "打款中"
		} else if WithdrawalType == 4 {
			return "无需打款"
		} else {
			return "未打款"
		}

	}
	for _, v := range List {

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.CreatedAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.OrderSn)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.User.Username)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Supplier.Name)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), WithdrawalType(v.WithdrawalType))
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), withdrawalMode(v.WithdrawalMode))
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), Fen2Yuan(v.WithdrawalAmount))
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), Fen2Yuan(v.PoundageAmount))
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), Fen2Yuan(v.ServiceTax))
		var invalidAmount uint
		var rejectedAmount uint
		invalidAmount = 0
		rejectedAmount = 0
		if len(v.WithdrawalOperation) > 0 {
			if v.WithdrawalOperation[0].InvalidAmount > 0 {
				invalidAmount = v.WithdrawalOperation[0].InvalidAmount
			}

			if v.WithdrawalOperation[0].RejectedAmount > 0 {
				rejectedAmount = v.WithdrawalOperation[0].RejectedAmount
			}
		}

		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), Fen2Yuan(rejectedAmount))
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), Fen2Yuan(invalidAmount))
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), Fen2Yuan(v.IncomeAmount))
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), WithdrawalStatus(v.WithdrawalStatus))
		f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), RemitStatusName(v.RemitStatus))

		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("**************")
	path := config.Config().Local.Path + "/export_settlement"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "提现导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return
}

func GetSettlementStatus(status uint) string {

	if status == 1 {
		return "已结算"
	} else {
		return "未结算"
	}

}

func GetPayName(pay int) string {

	if pay == 1 {
		return "汇聚余额"
	} else if pay == 2 {
		return "站内余额"
	} else if pay == 5 {
		return "汇聚微信支付"
	}

	return ""
}

func ExportSettlement(info request.AccountApplySearch) (err error, link string) {

	// 创建db
	db := source.DB().Model(&model.SupplierSettlement{})

	// 如果有条件搜索 下方会自动创建搜索语句

	var joinWhere string
	joinWhere = "left join suppliers on suppliers.id = supplier_settlements.supplier_id"
	if info.Type == 1 && info.Username != "" {

		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.id=" + info.Username
	}

	if info.Type == 2 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.name LIKE " + "'%" + info.Username + "%'"

	}
	if info.Type == 3 && info.Username != "" {
		joinWhere = "INNER join suppliers on suppliers.id = supplier_settlements.supplier_id and suppliers.mobile=" + info.Username

	}
	if info.SettID > 0 {
		db = db.Where("supplier_settlements.id = ?", info.SettID)
	}
	if info.OrderSn > 0 {
		db = db.Where("supplier_settlements.order_sn = ?", info.OrderSn)
	}
	if info.Status > 0 {
		db = db.Where("supplier_settlements.status = ?", info.Status-1)
	}
	if info.PayType != nil {
		db = db.Where("supplier_settlements.pay_type = ?", info.PayType)
	}

	//if info.TimeS != "" && info.TimeE != "" {
	//	db = db.Where("supplier_settlements.settlement_time BETWEEN ? AND ?", info.TimeS, info.TimeE)
	//
	//}
	if info.TimeS != "" && info.TimeE != "" {
		joinWhere = "INNER join orders on orders.id = supplier_settlements.order_id"
		db = db.Where("orders.created_at BETWEEN ? AND ?", info.TimeS, info.TimeE)

	}

	var balanceList []model.SupplierSettlement

	err = db.Joins(joinWhere).Preload(clause.Associations).Order("created_at DESC").Find(&balanceList).Error
	if err != nil {
		return
	}
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "id")
	f.SetCellValue("Sheet1", "B1", "订单编号")
	f.SetCellValue("Sheet1", "C1", "下单时间")
	f.SetCellValue("Sheet1", "D1", "供应商id")
	f.SetCellValue("Sheet1", "E1", "供应商")
	f.SetCellValue("Sheet1", "F1", "支付方式")
	f.SetCellValue("Sheet1", "G1", "支付时间")
	f.SetCellValue("Sheet1", "H1", "订单商品金额")
	f.SetCellValue("Sheet1", "I1", "订单商品供货价")
	f.SetCellValue("Sheet1", "J1", "运费金额")
	f.SetCellValue("Sheet1", "K1", "提现手续费")
	f.SetCellValue("Sheet1", "L1", "技术服务费")
	f.SetCellValue("Sheet1", "M1", "结算金额")
	f.SetCellValue("Sheet1", "N1", "状态")

	i := 2

	for _, v := range balanceList {

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), strconv.Itoa(int(v.Order.OrderSN)))
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.Order.CreatedAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.SupplierID)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Supplier.Name)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), GetPayName(v.PayType))
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.Order.PaidAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), Fen2Yuan(v.Order.Amount))
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), Fen2Yuan(v.Order.SupplyAmount))
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), Fen2Yuan(v.Order.Freight))
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), Fen2Yuan(v.WithdrawalFee))
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), Fen2Yuan(v.TechnicalServiceCost))
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), Fen2Yuan(v.SettlementAmount))
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), GetSettlementStatus(v.Status))
		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("**************")
	path := config.Config().Local.Path + "/export_settlement"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "结算流水导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return
}

func GetBalanceByUserID(uid uint) (err error, balance interface{}) {
	var accountBalance model.AccountBalance
	err = source.DB().Where("type = 1").Where("uid = ?", uid).First(&accountBalance).Error
	if err != nil {
		return
	}
	return err, accountBalance.PurchasingBalance
}

// batchUpdateWithdrawalStatus 批量更新提现状态
func batchUpdateWithdrawalStatus(tableName string, listStatus []request.KeyValues) {
	if len(listStatus) == 0 {
		return
	}

	// 使用CASE WHEN语句进行批量更新
	var ids []uint
	var cases []string
	var args []interface{}

	for _, item := range listStatus {
		ids = append(ids, item.ID)
		cases = append(cases, "WHEN ? THEN ?")
		args = append(args, item.ID, item.Status)
	}

	// 构建 IN 占位符并逐个追加 id 参数
	placeholders := make([]string, 0, len(ids))
	for _, id := range ids {
		placeholders = append(placeholders, "?")
		args = append(args, id)
	}

	// 执行批量更新
	query := "UPDATE " + tableName + " SET withdrawal_status = CASE id " + strings.Join(cases, " ") + " END WHERE id IN (" + strings.Join(placeholders, ",") + ")"
	source.DB().Exec(query, args...)
}
