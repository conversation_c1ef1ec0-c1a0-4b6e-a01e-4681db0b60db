//新增收货人地址
import RecipientsDialog from '@/views/personalCenter/recipients/components/dialog';
//选择收货地址
import SelectAddress from '../components/selectAddress';
import skuPopup from '../../againOrder/components/skuPopup';

export default {
    name: 'buyerCartIndex',
    components: { RecipientsDialog, SelectAddress, skuPopup },
    data() {
        return {
            errorOrders: [],
            fullscreenLoading: false,
            checkAll: false,
            isIndeterminate: false,

            // 配送方式
            disModeList: [
                { label: '快递', value: 1 },
                { label: '物流到付', value: 2 },
                { label: '自提', value: 3 },
            ],
            formData: {
                // 配送方式
                disMode: 1,
            },
            addressGlobalTemp: {},
            address: {},
            addressStr: '请选择地址',

            checkedCities: [],
            shopping_carts: [],
            shippingMethodsGlobalTag: 0,
            shipping_methods: [],
            amount: 0,
            goods_count: 0,
            tempId: -1,
        };
    },
    mounted() {
        window.addEventListener('scroll', this.handleScroll, true);
        this.initCart();
    },
    methods: {
        // 切换商品id
        onOpenSkuPopup(obj) {
            let that = this;
            let para = {
                id: obj.id,
                sku_id: obj.sku_id,
            };
            that.$post('/shoppingcart/update', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        updateSku(item) {
            this.$refs.skuPopup.info(item.product.id, item.id);
        },
        addAddress() {
            console.log(11111);
            this.openRecipientsDialogById(this.tempId);
        },
        handleScroll() {
            let scrollTop =
                document.body.scrollTop || document.documentElement.scrollTop;
            /*if (scrollTop > 1200) {
                this.headIshow = true
            } else {
                this.headIshow = false
            }*/
        },

        //全选
        handleCheckAllChange(val) {
            this.checkedAll(val);
            //this.checkedCities = val ? this.checkedCities : [];
            //this.isIndeterminate = false;
        },

        //单选
        handleCheckedCitiesChange(item) {
            let isChecked = item.checked == 1 ? 0 : 1;
            this.checkedById(item, isChecked);
        },

        //更新全选数据
        handleCheckedCities() {
            let checkedCount = this.checkedCities.length;
            // 排除已经失效的商品
            const validItems = this.shopping_carts.filter(
                (item) => item.is_expired === 0,
            );
            // this.checkAll = checkedCount === this.shopping_carts.length;
            // this.isIndeterminate = checkedCount > 0 && checkedCount < this.shopping_carts.length;
            this.checkAll = checkedCount === validItems.length;
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < validItems.length;
        },

        // 重新选择
        resetChoose(item) {
            this.handleCheckedCitiesChange(item);
        },

        // 打开新增收货人
        openRecipientsDialog() {
            this.$refs.recipientsDialog.dialogIsShow = true;
            this.$refs.recipientsDialog.isEditAddress = false;
            this.$refs.recipientsDialog.title = '添加收货地址';
            this.$refs.recipientsDialog.initFormData();
        },
        // 打开选择收货地址
        openSelectAddress() {
            this.$refs.selectAddress.isShow = true;
            if (this.address) {
                this.$refs.selectAddress.initAddress(this.address.id);
            } else {
                this.$refs.selectAddress.initAddress();
            }
        },
        // 打开确认框
        openQrBox(item) {
            let that = this;
            this.$refs.qrBox.isShow = true;
            that.addressGlobalTemp = item;
        },

        //新增地址回调
        callBackNewAddress(item) {
            this.openQrBox(item);
        },

        //确认回调
        qrBoxHandleClose(isSubmit) {
            let that = this;
            if (isSubmit) {
                console.log(that.addressGlobalTemp, '?????');
                that.address = that.addressGlobalTemp;
                that.addressStr = that.assemblyAddress(that.address);
                that.addressGlobal();
            }
        },

        //更新购物view
        setCartView(data) {
            let that = this;
            that.shopping_carts = data.shopping_carts;
            that.shipping_methods = data.shipping_methods;
            that.amount = data.amount;
            that.goods_count = data.goods_count;

            //初始化选中
            that.checkedCities = [];
            that.shopping_carts.forEach((item) => {
                if (item.checked === 1) {
                    that.checkedCities.push(item.id);
                }
            });
            that.handleCheckedCities();
        },

        //初始化购物车
        initCart() {
            let that = this;
            let para = {};
            that.$post('/shoppingcart/list', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //增加item
        orderQtyChange(item) {
            let that = this;
            let para = {
                id: item.id,
                qty: item.qty > item.stock ? item.stock : item.qty,
            };
            that.$post('/shoppingcart/update', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //删除item
        deleteOrderDialog(item) {
            let that = this;
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    that.deleteOrderById(item);
                })
                .catch(() => {});
        },
        deleteOrderById(item) {
            let that = this;
            let para = {
                id: item.id,
            };
            that.$post('/shoppingcart/delete', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                        that.$message({
                            message: res.msg,
                            type: 'success',
                        });
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //批量删除
        deleteBatchDialog() {
            let that = this;
            if (that.checkedCities == 0) {
                that.$message.error('请选择要删除的商品');
                return;
            }

            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    that.deleteBatchByIds();
                })
                .catch(() => {});
        },
        deleteBatchByIds() {
            let that = this;
            let para = {
                ids: that.checkedCities,
            };
            that.$post('/shoppingcart/delete/batch', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //物流类型切换
        shippingMethodsChange(item) {
            let that = this;
            let para = {
                id: item.id,
                shipping_method_id: item.shipping_method_id,
            };
            that.$post('/shoppingcart/update', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //物流类型全局切换
        shippingMethodsGlobalChange() {
            let that = this;
            let para = {
                shipping_method_id: that.shippingMethodsGlobalTag,
            };
            that.$post('/shoppingcart/update/batch', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //选中处理
        checkedById(item, isChecked) {
            let that = this;
            let para = {
                id: item.id,
                checked: isChecked,
            };
            that.$post('/shoppingcart/update', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //全选处理
        checkedAll(isChecked) {
            let that = this;
            let para = {
                checked: isChecked ? 1 : 0,
            };
            that.$post('shoppingcart/update/batch', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //全局处理地址
        addressGlobal() {
            let that = this;
            let para = {
                address_id: that.address.id,
            };

            that.$post('shoppingcart/update/batch', para)
                .then(function (res) {
                    if (res.code == 0) {
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        //组装地址
        assemblyAddress(address) {
            let addressStr = '';
            if (!address) {
                return '请选择收货地址';
            }
            addressStr =
                address.realname +
                '  ' +
                address.mobile +
                '  ' +
                address.province +
                '  ' +
                address.city +
                '  ' +
                address.county +
                '  ' +
                address.town +
                '  ' +
                address.detail;
            return addressStr;
        },

        // 打开新增收货人
        openRecipientsDialogById(id) {
            this.tempId = id;
            this.$refs.recipientsDialogById.dialogIsShow = true;
            this.$refs.recipientsDialogById.isEditAddress = false;
            this.$refs.recipientsDialogById.title = '添加收货地址';
            this.$refs.recipientsDialogById.initFormData();
        },
        // 打开选择收货地址
        openSelectAddressById(item) {
            this.tempId = item.id;
            this.$refs.selectAddressById.isShow = true;
            if (item.address) {
                this.$refs.selectAddressById.initAddress(item.address.id);
            } else {
                this.$refs.selectAddressById.initAddress();
            }
        },

        //新增地址ById
        callBackNewAddressById(item) {
            this.openQrBoxById(item);
        },

        // 打开确认框
        openQrBoxById(item) {
            let that = this;
            that.orderAddressChange(item);
        },

        //修改地址
        orderAddressChange(item) {
            let that = this;
            if (that.tempId < 0) {
                return;
            }
            let para = {
                id: that.tempId,
                address_id: item.id,
            };
            that.$post('/shoppingcart/update', para)
                .then(function (res) {
                    if (res.code == 0) {
                        //复位
                        that.tempId = -1;
                        that.setCartView(res.data);
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },

        // 结算
        cartSubmit() {
            let that = this;
            if (that.checkedCities.length == 0) {
                that.$message.error('请选择结算商品');
                return;
            }
            that.fullscreenLoading = true;

            let para = {
                buy_id: 0,
            };
            that.$post('/trade/checkout', para)
                .then(function (res) {
                    if (res.code == 0) {
                        /* that.$_blank("settlementAmount",{
                        "buy_id":0
                    }); */

                        that.$router.push({
                            path: 'settlementAmount',
                            query: {
                                buy_id: 0,
                            },
                        });
                    } else if (res.code == 2) {
                        that.$confirm('当前等级暂无购买权限', '提示', {
                            confirmButtonText: '去升级',
                            cancelButtonText: '取消',
                        }).then(() => {
                            that.$router.push({
                                path: '/personalCenter/memberRights',
                            });
                        });
                    } else {
                        that.initCart();
                        that.errorOrders = res.data || [];
                        that.$message.error(res.msg);
                    }
                    that.fullscreenLoading = false;
                })
                .catch(function (res) {
                    that.fullscreenLoading = false;
                    console.log(res);
                });
        },
    },
};
