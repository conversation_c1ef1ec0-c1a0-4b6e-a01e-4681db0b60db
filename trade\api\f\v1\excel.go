package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"trade/checkout"
	ufv1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

func ExcelCartCheckout(c *gin.Context) {
	userID := ufv1.GetUserID(c)

	// 选中的购物车记录
	err, shoppingCarts := checkout.GetCheckedExcelCarts(checkout.ShoppingCart{UserID: userID, BuyID: 0})
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if len(shoppingCarts) == 0 {
		//  没有购物车记录
		log.Log().Error("请选择要结算的商品", zap.Any("err", err))
		yzResponse.FailWithMessage("请选择要结算的商品", c)
		return
	}

	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(userID, shoppingCarts)

	if err != nil {
		log.Log().Error("结算失败", zap.Any("err", err))
		yzResponse.FailWithDetailed(checkoutInfo.FailedShoppingCart, err.Error(), c)
		return
	}
	checkoutInfo.BuyID = 0
	yzResponse.OkWithDetailed(checkoutInfo, "获取成功", c)
	return
}
