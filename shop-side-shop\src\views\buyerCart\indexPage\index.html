<div class="mt20">
    <div class="inner cart-box">
        <!-- 批量选择配送信息 -->
        <div class="distribution-box bgw">
            <p>批量选择配送信息</p>
            <el-divider class="mtb"></el-divider>
            <el-form :model="formData" label-width="80px" label-position="left">
                <el-form-item label="配送方式">
                    <el-radio-group
                        v-model="shippingMethodsGlobalTag"
                        class="cart-radio-group"
                        @change="shippingMethodsGlobalChange()"
                    >
                        <el-radio
                            v-for="item in shipping_methods"
                            :key="item.id"
                            :label="item.id"
                        >
                            {{item.name}}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="收货地址">
                    <el-button size="small" @click="openSelectAddress"
                        >选择收货人及地址</el-button
                    >
                    <el-button size="small" @click="openRecipientsDialog"
                        >新增收货人及地址</el-button
                    >
                </el-form-item>
                <el-form-item label="已选地址">
                    <p>{{addressStr}}</p>
                </el-form-item>
            </el-form>
        </div>
        <!-- 购物车列表 -->
        <div class="mt20"></div>
        <el-row class="bgw cart-table-head">
            <el-col :span="2">
                <el-checkbox
                    :indeterminate="isIndeterminate"
                    v-model="checkAll"
                    @change="handleCheckAllChange"
                    >全选
                </el-checkbox>
            </el-col>
            <el-col :span="13">
                <p class="text-center">商品信息</p>
            </el-col>
            <el-col :span="2">
                <p class="text-center">单价</p>
            </el-col>
            <el-col :span="3">
                <p class="text-center">数量</p>
            </el-col>
            <el-col :span="2">
                <p class="text-center">小计</p>
            </el-col>
            <el-col :span="2">
                <p class="text-center">操作</p>
            </el-col>
            <!-- <el-col :span="2" class="text-center">
                <el-button type="text" class="del-text-ben">删除</el-button>
            </el-col> -->
        </el-row>
        <!-- 商家 -->
        <el-row class="bgw mt20 cart-table-con" v-for="item in shopping_carts" :class="errorOrders.some(order => order.id === item.id) ? 'border-red' : ''">
            <el-col>
                <el-checkbox
                    v-if="item.is_expired === 0"
                    class="order-checkbox"
                    @change="handleCheckedCitiesChange(item)"
                    v-model="checkedCities"
                    :label="item.id"
                    :value="item.id"
                >
                    <!--好像是不可以写两个参数-->
                    <div
                        v-if="item.supplier.id"
                        class="f fac"
                        @click.stop.prevent="$_blank('/store',{sid:item.supplier.id,type:0})"
                    >
                        <!--                        <div v-if="item.supplier.id" class="f fac"   @click="$router.push({path:'/store', query:{sid:item.supplier.id, type:0}})">-->

                        <el-avatar
                            size="large"
                            :src="item.supplier.shop_logo"
                        ></el-avatar>
                        <p>{{item.supplier.name}}</p>
                    </div>
                    <div v-else class="f fac">
                        <el-avatar
                            size="large"
                            :src="item.supplier.shop_logo"
                        ></el-avatar>
                        <p>{{item.supplier.name}}</p>
                    </div>
                </el-checkbox>
                <!-- 商品失效显示 -->
                <div v-if="item.is_expired === 1" class="order-checkbox">
                    <!--好像是不可以写两个参数-->
                    <div
                        v-if="item.supplier.id"
                        class="f fac"
                        @click.stop.prevent="$_blank('/store',{sid:item.supplier.id,type:0})"
                    >
                        <!--                        <div v-if="item.supplier.id" class="f fac"   @click="$router.push({path:'/store', query:{sid:item.supplier.id, type:0}})">-->

                        <el-avatar
                            size="large"
                            :src="item.supplier.shop_logo"
                        ></el-avatar>
                        <p class="ml10">{{item.supplier.name}}</p>
                    </div>
                    <div v-else class="f fac">
                        <el-avatar
                            size="large"
                            :src="item.supplier.shop_logo"
                        ></el-avatar>
                        <p class="ml10">{{item.supplier.name}}</p>
                    </div>
                </div>
            </el-col>
            <el-col>
                <el-divider class="mtb"></el-divider>
            </el-col>
            <!-- 商品 -->
            <el-row v-if="item.is_expired === 0" class="order-goods-list-box">
                <el-col :span="15">
                    <div
                        class="f fac"
                        @click.stop="$_blank('/goodsDetail',{goods_id:item.product.id})"
                    >
                        <!-- <img :src="item.product.image_url" class="order-goods-img"> -->
                        <m-image
                            :src="item.sku.image_url ? item.sku.image_url : item.product.image_url"
                            class="order-goods-img"
                            :size="['120px','120px']"
                        ></m-image>
                        <div class="order-goods-text">
                            <p>{{item.product.title}}</p>
                            <p class="mt20">
                                规格:{{item.sku.title}}
                                <span
                                    class="icon-icon_edit iconfont"
                                    style="color: #aeaeae; font-size: 14px"
                                    @click.stop="updateSku(item)"
                                ></span>
                            </p>
                        </div>
                    </div>
                </el-col>
                <el-col :span="2">
                    <p class="text-center table-jc">
                        ￥{{item.sku.price|formatF2Y}}
                    </p>
                </el-col>
                <el-col :span="3" class="table-jc">
                    <el-input-number
                        :disabled="item.is_expired === 1"
                        size="small"
                        :min="1"
                        :max="item.stock"
                        v-model="item.qty"
                        @change="orderQtyChange(item)"
                    >
                    </el-input-number>
                </el-col>
                <el-col :span="2">
                    <p class="text-center table-jc">
                        ￥{{item.sku.price*item.qty|formatF2Y}}
                    </p>
                </el-col>
                <el-col :span="2" class="table-jc text-center">
                    <a href="javascript:;" @click="deleteOrderDialog(item)">
                        <i class="iconfont icon-pc_line_delete"></i>
                    </a>
                </el-col>
            </el-row>
            <!-- 失效时显示 -->
            <el-row
                v-if="item.is_expired === 1"
                class="order-goods-list-box"
                style="color: #999999"
            >
                <el-col :span="15">
                    <div
                        class="f fac"
                        @click.stop="$_blank('/goodsDetail',{goods_id:item.product.id})"
                    >
                        <!-- <img :src="item.product.image_url" class="order-goods-img"> -->
                        <div class="order-goods-expired">失效</div>
                        <m-image
                            :src="item.product.image_url"
                            class="order-goods-img"
                            :size="['120px','120px']"
                        ></m-image>
                        <div class="order-goods-text">
                            <p>{{item.product.title}}</p>
                            <p class="mt20">
                                规格:{{item.sku.title}}
                                <span
                                    class="icon-icon_edit iconfont"
                                    style="color: #aeaeae; font-size: 14px"
                                    @click.stop="updateSku(item)"
                                ></span>
                            </p>
                        </div>
                    </div>
                </el-col>
                <el-col :span="2">
                    <p class="text-center table-jc">￥0.00</p>
                </el-col>
                <el-col :span="3" class="table-jc">
                    <el-input-number
                        :disabled="item.is_expired === 1"
                        size="small"
                        :min="1"
                        v-model="item.qty"
                        @change="orderQtyChange(item)"
                    >
                    </el-input-number>
                </el-col>
                <el-col :span="2">
                    <p class="text-center table-jc">￥0.00</p>
                </el-col>
                <el-col :span="2" class="table-jc text-center">
                    <a href="javascript:;" @click="deleteOrderDialog(item)">
                        <i class="iconfont icon-pc_line_delete"></i>
                    </a>
                </el-col>
            </el-row>
            <!-- 配送信息 -->
            <el-col>
                <div v-if="item.is_expired === 0" class="distribution-box bgw">
                    <p>选择配送信息</p>
                    <el-divider class="mtb"></el-divider>
                    <el-form
                        :model="formData"
                        label-width="80px"
                        label-position="left"
                    >
                        <el-form-item label="配送方式">
                            <el-radio-group
                                v-model="item.shipping_method_id"
                                class="cart-radio-group"
                            >
                                <el-radio
                                    v-for="shippingMethodsItem in item.shipping_methods"
                                    :key="shippingMethodsItem.id"
                                    :label="shippingMethodsItem.id"
                                    @change="shippingMethodsChange(item)"
                                >
                                    {{shippingMethodsItem.name}}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="收货地址">
                            <el-button
                                size="small"
                                @click="openSelectAddressById(item)"
                                >选择收货人及地址</el-button
                            >
                            <el-button
                                size="small"
                                @click="openRecipientsDialogById(item.id)"
                                >新增收货人及地址</el-button
                            >
                        </el-form-item>
                        <el-form-item label="已选地址">
                            <p>{{assemblyAddress(item.address)}}</p>
                        </el-form-item>
                        <p class="color-red" v-if="errorOrders.some(order => order.id === item.id)">{{ errorOrders.find(order => order.id === item.id).err_message }}</p>
                    </el-form>
                </div>
                <!-- 商品失效显示 -->
                <div v-if="item.is_expired === 1" class="distribution-box bgw">
                    <el-divider class="mtb"></el-divider>
                    <el-form
                        :model="formData"
                        label-width="80px"
                        label-position="left"
                    >
                        <el-form-item label="失效原因:">
                            <div class="f fac fjsb">
                                <p style="width: 900px">
                                    {{ item.expired_message }}
                                </p>
                                <el-button
                                    style="width: 82px; height: 32px"
                                    size="mini"
                                    @click="resetChoose(item)"
                                    >重新选择</el-button
                                >
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
            </el-col>
        </el-row>
    </div>
    <!-- 底部结算 -->
    <div class="footer-account-box bgw">
        <div class="inner f fac fjsb">
            <div class="left-box f fac">
                <el-checkbox
                    :indeterminate="isIndeterminate"
                    v-model="checkAll"
                    @change="handleCheckAllChange"
                    >全选
                </el-checkbox>
                <p class="ppp" @click="deleteBatchDialog">删除选中商品</p>
                <p class="red-p">已选{{goods_count}}件商品</p>
            </div>
            <div class="right-box f fac">
                <span>总价(含快递)</span>
                <span>{{amount|formatF2Y}}</span>
                <el-button
                    @click="cartSubmit"
                    v-loading.fullscreen.lock="fullscreenLoading"
                    >结算</el-button
                >
            </div>
        </div>
    </div>
    <!-- 新增收货人及地址 -->
    <RecipientsDialog
        ref="recipientsDialog"
        @callBackAddressList="callBackNewAddress"
    ></RecipientsDialog>
    <!-- 选择收货人及地址 -->
    <select-address
        ref="selectAddress"
        @openQrBox="openQrBox"
        @addAddress="addAddress"
    ></select-address>
    <!-- 确认框 -->
    <qr-box
        ref="qrBox"
        title="提示信息"
        @handleClose="qrBoxHandleClose"
        content="您确定用批量选择的配送信息，作为下面全部订单的配送信息吗"
    ></qr-box>

    <!-- 新增收货人及地址 -->
    <RecipientsDialog
        ref="recipientsDialogById"
        @callBackAddressList="callBackNewAddressById"
    ></RecipientsDialog>
    <!-- 选择收货人及地址 -->
    <select-address
        ref="selectAddressById"
        @openQrBox="openQrBoxById"
        @addAddress="addAddress"
    ></select-address>

    <skuPopup ref="skuPopup" @onOpenSkuPopup="onOpenSkuPopup"></skuPopup>
</div>
