import service from "@/utils/request";
/*
 *@Summary 获取基础设置
 *@Router  /shareLive/getSysShareLiveSetting
 *@Method  get
 *@Date    2023-07-04
*/
export const getSysShareLiveSetting = params => {
    return service({
        url: "/shareLive/getSysShareLiveSetting",
        method: "get",
        params
    })
}
/*
 *@Summary 保存基础设置
 *@Router  /shareLive/saveSysShareLiveSetting
 *@Method  post
 *@Date    2023-07-04
*/
export const saveSysShareLiveSetting = data => {
    return service({
        url: "/shareLive/saveSysShareLiveSetting",
        method: "post",
        data
    })
}

/*
 *@Summary 共享直播分类列表
 *@Router  /shareLive/getShareLiveCategoryList
 *@Method  get
 *@Date    2023-07-04
*/
export const getShareLiveCategoryList = params => {
    return service({
        url: "/shareLive/getShareLiveCategoryList",
        method: "get",
        params
    })
}
/*
 *@Summary 获取所有共享直播分类
 *@Router  /shareLive/getShareLiveCategorys
 *@Method  get
 *@Date    2023-07-04
*/
export const getShareLiveCategorys = params => {
    return service({
        url: "/shareLive/getShareLiveCategorys",
        method: "get",
        params
    })
}
/*
 *@Summary 删除共享直播分类
 *@Router  /shareLive/deleteShareLiveCategory
 *@Method  delete
 *@Date    2023-07-04
*/
export const deleteShareLiveCategory = data => {
    return service({
        url: "/shareLive/deleteShareLiveCategory",
        method: "delete",
        data
    })
}
/*
 *@Summary 创建分类
 *@Router  /shareLive/createShareLiveCategory
 *@Method  post
 *@Date    2023-07-04
*/
export const createShareLiveCategory = data => {
    return service({
        url: "/shareLive/createShareLiveCategory",
        method: "post",
        data
    })
}
/*
 *@Summary 修改分类
 *@Router  /shareLive/saveShareLiveCategory
 *@Method  post
 *@Date    2023-07-04
*/
export const saveShareLiveCategory = data => {
    return service({
        url: "/shareLive/saveShareLiveCategory",
        method: "post",
        data
    })
}
/*
 *@Summary 通过id直播分类详情
 *@Router  /shareLive/getShareLiveCategoryById
 *@Method  get
 *@Date    2023-07-04
*/
export const getShareLiveCategoryById = params => {
    return service({
        url: "/shareLive/getShareLiveCategoryById",
        method: "get",
        params
    })
}

/*
 *@Summary 创建直播间
 *@Router  /shareLive/createShareLiveRoom
 *@Method  post
 *@Date    2023-07-04
*/
export const createShareLiveRoom = data => {
    return service({
        url: "/shareLive/createShareLiveRoom",
        method: "post",
        data
    })
}
/*
 *@Summary 修改直播间
 *@Router  /shareLive/saveShareLiveRoom
 *@Method  post
 *@Date    2023-07-04
*/
export const saveShareLiveRoom = data => {
    return service({
        url: "/shareLive/saveShareLiveRoom",
        method: "post",
        data
    })
}
/*
 *@Summary 修改直播间指定采购端
 *@Router  /shareLive/saveShareLiveRoom
 *@Method  post
 *@Date    2023-07-04
*/
export const saveShareLiveRoomApplicationSetting = data => {
    return service({
        url: "/shareLive/saveShareLiveRoomApplicationSetting",
        method: "post",
        data
    })
}
/*
 *@Summary 通过id获取直播详情
 *@Router  /shareLive/getShareLiveRoomById
 *@Method  get
 *@Date    2023-07-04
*/
export const getShareLiveRoomById = params => {
    return service({
        url: "/shareLive/getShareLiveRoomById",
        method: "get",
        params
    })
}
//采购端
export const getApplicationOption = (params) => {
    return service({
        url: "/application/getApplicationOption",
        method: 'get',
        params
    })
}
/*
 *@Summary 直播间列表
 *@Router  /shareLive/getShareLiveRoomList
 *@Method  get
 *@Date    2023-07-04
*/
export const getShareLiveRoomList = params => {
    return service({
        url: "/shareLive/getShareLiveRoomList",
        method: "get",
        params
    })
}
/*
 *@Summary 通过直播间id获取直播回放文件
 *@Router  /shareLive/getShareLiveRoomRecordFileByRoomId
 *@Method  get
 *@Date    2023-07-04
*/
export const getShareLiveRoomRecordFileByRoomId = params => {
    return service({
        url: "/shareLive/getShareLiveRoomRecordFileByRoomId",
        method: "get",
        params
    })
}
/*
 *@Summary 开始直播
 *@Router  /shareLive/saveShareLiveRoomStart
 *@Method  get
 *@Date    2023-07-04
*/
export const saveShareLiveRoomStart = params => {
    return service({
        url: "/shareLive/saveShareLiveRoomStart",
        method: "get",
        params
    })
}
/*
 *@Summary 结束直播
 *@Router  /shareLive/saveShareLiveRoomEnd
 *@Method  get
 *@Date    2023-07-04
*/
export const saveShareLiveRoomEnd = params => {
    return service({
        url: "/shareLive/saveShareLiveRoomEnd",
        method: "get",
        params
    })
}
/*
 *@Summary 修改是否开启回放
 *@Router  /shareLive/saveShareLiveRoomEnd
 *@Method  get
 *@Date    2023-07-04
*/
export const SaveShareLiveRoomIsPlayBack = params => {
    return service({
        url: "/shareLive/SaveShareLiveRoomIsPlayBack",
        method: "get",
        params
    })
}
/*
 *@Summary 采购端数据列表
 *@Router  /shareLive/getShareLiveRoomApplicationList
 *@Method  get
 *@Date    2023-07-04
*/
export const getShareLiveRoomApplicationList = params => {
    return service({
        url: "/shareLive/getShareLiveRoomApplicationList",
        method: "get",
        params
    })
}
/*
 *@Summary 获取所有直播间
 *@Router  /shareLive/getShareLiveRooms
 *@Method  get
 *@Date    2023-07-04
*/
export const getShareLiveRooms = params => {
    return service({
        url: "/shareLive/getShareLiveRooms",
        method: "get",
        params
    })
}

/*
 *@Summary 修改直播间开关状态
 *@Router  /shareLive/saveShareLiveRoomIsOpen
 *@Method  post
 *@Date    2023-08-07
*/
export const saveShareLiveRoomIsOpen = data => {
    return service({
        url: "/shareLive/saveShareLiveRoomIsOpen",
        method: "post",
        data
    })
}

/*
 *@Summary 修改直播间横屏状态
 *@Router  /shareLive/saveShareLiveRoomIsLandscape
 *@Method  post
 *@Date    2023-08-07
*/
export const saveShareLiveRoomIsLandscape = data => {
    return service({
        url: "/shareLive/saveShareLiveRoomIsLandscape",
        method: "post",
        data
    })
}
