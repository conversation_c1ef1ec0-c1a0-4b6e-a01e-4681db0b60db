<template>
    <m-card>
        <el-form
            :model="searchInfo"
            class="search-term"
            label-width="90px"
            inline
        >
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.title"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>商品状态</span>
                    </div>
                    <el-select v-model="searchInfo.filter" class="w100">
                        <el-option
                            v-for="item in goodsStatusList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>供应商</span>
                    </div>
                    <el-select
                        v-model="searchInfo.supplier_id"
                        class="w100"
                        filterable
                        clearable
                    >
                        <el-option label="全部" value=""></el-option>
                        <el-option label="平台自营" value="0"></el-option>
                        <el-option
                            label="全部供应商"
                            value="999999"
                        ></el-option>
                        <el-option
                            v-for="item in supplierList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>供应链</span>
                    </div>
                    <el-select
                        v-model="searchInfo.gather_supply_id"
                        class="w100"
                        filterable
                        clearable
                    >
                        <el-option label="全部" value=""></el-option>
                        <el-option label="平台自营" value="0"></el-option>
                        <el-option
                            :label="item.name"
                            :value="item.id"
                            v-for="item in supplyOptions"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>一级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category1_id"
                        placeholder="请选择一级分类"
                        class="w100"
                        filterable
                        clearable
                        @change="getClassifyOptions(2, searchInfo.category1_id)"
                    >
                        <el-option
                            v-for="item in categoryList1"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>二级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category2_id"
                        placeholder="请选择二级分类"
                        filterable
                        clearable
                        class="w100"
                        @change="getClassifyOptions(3, searchInfo.category2_id)"
                    >
                        <el-option
                            v-for="item in categoryList2"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>三级分类</span>
                    </div>
                    <el-select
                        v-model="searchInfo.category3_id"
                        placeholder="请选择三级分类"
                        filterable
                        clearable
                        class="w100"
                    >
                        <el-option
                            v-for="item in categoryList3"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.sn"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品条形码</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>品牌</span>
                    </div>
                    <el-select
                        v-model="searchInfo.brand_id"
                        class="w100"
                        clearable
                        filterable
                        remote
                        :remote-method="remoteMethod"
                        :loading="brandsOptiosData.loading"
                    >
                        <el-option
                            v-for="item in brandsOptiosData.brandsOptios"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                        <div class="text-center">
                            <el-pagination
                                background
                                small
                                class="pagination"
                                style="
                                    padding-top: 10px !important;
                                    padding-bottom: 0 !important;
                                "
                                :current-page="brandsOptiosData.page"
                                :page-size="brandsOptiosData.pageSize"
                                :total="brandsOptiosData.total"
                                @current-change="handleBrandPage"
                                layout="prev,pager, next"
                            />
                        </div>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.id"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商品id</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>赋码状态</span>
                    </div>
                    <el-select
                        v-model="searchInfo.is_bill"
                        class="w100"
                        clearable
                    >
                        <el-option :value="1" label="已赋码"></el-option>
                        <el-option :value="0" label="未赋码"></el-option>
                    </el-select>
                </div>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reSearch"
                    >重置搜索条件</el-button
                >
            </el-form-item>
        </el-form>
        <el-table :data="tableData" :span-method="arraySpanMethod">
            <el-table-column
                label="ID"
                width="50"
                align="center"
                prop="id"
            ></el-table-column>
            <el-table-column
                label="图片"
                width="140"
                align="center"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    <el-popover placement="right" title="" trigger="hover">
                        <m-image
                            :src="scope.row.image_url"
                            :style="{ width: '160px', height: '160px' }"
                        ></m-image>
                        <m-image
                            slot="reference"
                            :src="scope.row.image_url"
                            :alt="scope.row.image_url"
                            :style="{ width: '60px', height: '60px' }"
                        ></m-image>
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column label="商品" width="250">
                <template slot-scope="scope">
                    <p>{{ scope.row.title }}</p>
                </template>
            </el-table-column>
            <el-table-column
                label="品牌"
                align="center"
                prop="brand.name"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                label="供应渠道"
                align="center"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    <span v-if="scope.row.supplier_id > 0">{{
                        scope.row.supplier.name
                    }}</span>
                    <span v-else-if="scope.row.gather_supply_id > 0">{{
                        scope.row.gather_supply.name
                    }}</span>
                    <span
                        v-else-if="
                            scope.row.supplier_id === 0 &&
                            scope.row.gather_supply_id === 0
                        "
                        >自营</span
                    >
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>上下架状态</p>
                    <p>锁定状态</p>
                </template>
                <template slot-scope="scope">
                    <p>
                        <el-switch
                            v-model="scope.row.is_display"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleStatus(scope.row, 'is_display')"
                        >
                        </el-switch>
                    </p>
                    <p>
                        <el-switch
                            v-model="scope.row.status_lock"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleStatus(scope.row, 'status_lock')"
                        >
                        </el-switch>
                    </p>
                </template>
            </el-table-column>
            <el-table-column label="规格标题" align="center">
                <template slot-scope="scope">
                    <el-tooltip
                        v-for="item in scope.row.skus"
                        effect="dark"
                        :content="item.title"
                        placement="top-start"
                    >
                        <p
                            class="hiddenText1"
                            :class="scope.row.skus.length > 0 ? 'h60' : ''"
                        >
                            {{ item.title }}
                        </p>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                label="赋码状态"
                align="center"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    <template v-if="scope.row.bill_position == 2">
                        <p v-for="item in scope.row.skus" class="h60">
                            {{ item.tax_code != '' ? '已赋码' : '未赋码' }}
                        </p>
                    </template>
                    <p v-else>
                        {{ scope.row.tax_code != '' ? '已赋码' : '未赋码' }}
                    </p>
                </template>
            </el-table-column>
            <el-table-column
                label="税收分类编码"
                align="center"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    <template v-if="scope.row.bill_position == 2">
                        <p v-for="item in scope.row.skus" class="h60">
                            {{ item.tax_code || '---' }}
                        </p>
                    </template>
                    <p v-else>{{ scope.row.tax_code || '---' }}</p>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                    <p
                        v-for="(item, index) in scope.row.skus"
                        :class="scope.row.skus.length > 0 ? 'h60' : ''"
                    >
                        <el-button
                            @click="
                                openCodeTypeDialog(
                                    index,
                                    scope.$index,
                                    scope.row,
                                )
                            "
                            type="text"
                            >赋码</el-button
                        >
                    </p>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100, 200]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
        <CodeTypeDialog
            ref="codeTypeDialog"
            @resSkus="resSkus"
        ></CodeTypeDialog>
    </m-card>
</template>

<script>
import { getProductList, getSupplierOptionList, upStatus } from '@/api/goods';
import { getSupplyList } from '@/api/order';
import { getClassify } from '@/api/classify';
import { getBrandsList } from '@/api/brands';
import CodeTypeDialog from '@/view/goods/manage/components/codeTypeDialog';
import { updateProduct, findProduct } from '@/api/product';
import infoList from '@/mixins/infoList';

export default {
    name: 'billAssignmentCodeList',
    components: { CodeTypeDialog },
    mixins: [infoList],
    data() {
        return {
            listApi: getProductList,
            searchInfo: {
                filter: '0',
                supplier_id: '',
                gather_supply_id: '',
                category1_id: '',
                category2_id: '',
                category3_id: '',
                brand_id: '',
            },
            // 商品状态
            goodsStatusList: [
                { name: '全部', value: '0' },
                { name: '上架', value: '1' },
                { name: '下架', value: '2' },
                { name: '售罄', value: '3' },
            ],
            // 供应商
            supplierList: [],
            // 供应链
            supplyOptions: [],
            // 分类
            categoryList1: [],
            categoryList2: [],
            categoryList3: [],
            // 品牌
            brandsOptiosData: {
                name: '',
                brandsOptios: [],
                loading: false,
                page: 1,
                pageSize: 10,
                total: 0,
            },
            spanArr: [],
            tableIndex: null,
        };
    },
    mounted() {
        this.initOptions();
        this.getList();
    },
    methods: {
        resSkus(skus, bill_position) {
            let tableIndex = this.tableIndex;
            this.tableIndex = null;
            findProduct({ id: this.tableData[tableIndex].id }).then((res) => {
                if (res.code === 0) {
                    let tableData = res.data.reproduct;
                    if (bill_position === 2) {
                        tableData.skus = skus;
                    } else {
                        tableData.tax_product_name = skus[0].tax_product_name;
                        tableData.tax_code = skus[0].tax_code;
                        tableData.tax_short_name = skus[0].tax_short_name;
                        tableData.tax_unit = skus[0].tax_unit;
                        tableData.favorable_policy = skus[0].favorable_policy;
                        tableData.is_favorable_policy =
                            skus[0].is_favorable_policy;
                        tableData.free_of_tax = skus[0].free_of_tax;
                        tableData.short_code = skus[0].short_code;
                        tableData.tax_measure_price = skus[0].tax_measure_price;
                        tableData.tax_rate = skus[0].tax_rate;
                        tableData.is_tax_logo = skus[0].is_tax_logo;
                    }
                    if ('brand' in tableData) {
                        delete tableData.brand;
                    }
                    updateProduct(tableData).then((res) => {
                        if (res.code === 0) {
                            this.$message.success(res.msg);
                            this.getList();
                        }
                    });
                }
            });

            /*this.skus = skus
      skus.forEach((item, index) => {
        this.$set(this.skus, index, {
          ...skus[index]
        })
      })*/
        },
        openCodeTypeDialog(type = '', index, row) {
            this.tableIndex = index;
            this.$refs.codeTypeDialog.isShow = true;
            this.$refs.codeTypeDialog.getBillClassify();
            this.$nextTick(async () => {
                this.$refs.codeTypeDialog.goodsTitle = row.title;
                this.$refs.codeTypeDialog.bill_position = row.bill_position;
                const { data } = await findProduct({ id: row.id });
                this.$refs.codeTypeDialog.productInfo = data.reproduct;
                if (row.bill_position == 2) {
                    this.$refs.codeTypeDialog.skus = data.reproduct.skus;
                } else {
                    let skus = [
                        {
                            ...row.skus[0],
                            tax_product_name: row.tax_product_name,
                            tax_code: row.tax_code,
                            tax_short_name: row.tax_short_name,
                            tax_unit: row.tax_unit,
                            favorable_policy: row.favorable_policy,
                            is_favorable_policy: row.is_favorable_policy,
                            free_of_tax: row.free_of_tax,
                            short_code: row.short_code,
                            tax_measure_price: row.tax_measure_price,
                            tax_rate: row.tax_rate,
                            is_tax_logo: row.is_tax_logo,
                            is_single: true,
                        },
                    ];
                    this.$refs.codeTypeDialog.skus = skus;
                }

                this.$refs.codeTypeDialog.type = type;
                if (typeof type === 'number') {
                    this.$refs.codeTypeDialog.setForm();
                }
            });
        },
        handleCurrentChange(val) {
            this.page = val;
            this.getList();
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.getList();
        },
        async getList(page = this.page, pageSize = this.pageSize) {
            for (let k in this.searchInfo) {
                if (
                    typeof this.searchInfo[k] !== 'number' &&
                    !this.searchInfo[k]
                ) {
                    delete this.searchInfo[k];
                }
            }
            const table = await this.listApi({
                page,
                pageSize,
                ...this.searchInfo,
            });
            /*this.tableData = []
      this.spanArr = []*/
            if (table.code == 0) {
                let list = table.data.list;
                this.tableData = list;
                /*list.forEach(item => {
          item.skus.forEach(item2 => {
            this.tableData.push({...item})
          })
        })
        if (this.tableData && this.tableData.length) {
          this.getSpanArr(this.tableData)
        }*/
                this.total = table.data.total;
                this.pageSize = table.data.pageSize;
            }
        },
        getSpanArr(data) {
            let pos = 0;
            for (var i = 0; i < data.length; i++) {
                if (i === 0) {
                    this.spanArr.push(1);
                    pos = 0;
                } else {
                    // 判断当前元素与上一个元素是否相同
                    if (data[i].id === data[i - 1].id) {
                        this.spanArr[pos] += 1;
                        this.spanArr.push(0);
                    } else {
                        this.spanArr.push(1);
                        pos = i;
                    }
                }
            }
        },
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            /*if (columnIndex <= 3) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col
        }
      }*/
        },
        search() {
            this.getList(1);
        },
        reSearch() {
            this.searchInfo = {
                filter: '0',
                supplier_id: '',
                gather_supply_id: '',
                category1_id: '',
                category2_id: '',
                category3_id: '',
                brand_id: '',
            };
        },
        initOptions() {
            this.getSupplierOptions();
            this.getSupplyOptions();
            this.getClassifyOptions();
            this.getBrandsOptios();
        },
        // 品牌搜索
        remoteMethod(query) {
            this.brandsOptiosData.name = query;
            this.brandsOptiosData.page = 1;
            this.getBrandsOptios();
        },
        handleBrandPage(val) {
            this.brandsOptiosData.page = val;
            this.getBrandsOptios();
        },
        async handleStatus(row, name) {
            let params = {
                column: name,
                id: row.id,
                status: row[name],
            };
            const { code, msg } = await upStatus(params);
            if (code === 0) {
                this.$message.success(msg);
            }
        },
        // 供应商
        async getSupplierOptions() {
            const { data } = await getSupplierOptionList();
            this.supplierList = data.list;
        },
        // 供应链
        async getSupplyOptions() {
            const { data } = await getSupplyList();
            this.supplyOptions = data.list;
        },
        // 获取分类
        async getClassifyOptions(level = 1, parent_id = 0) {
            const { data } = await getClassify(level, parent_id);
            switch (level) {
                case 1:
                    this.categoryList1 = data.list;
                    this.categoryList2 = [];
                    this.categoryList3 = [];
                    this.searchInfo.category1_id = '';
                    this.searchInfo.category2_id = '';
                    this.searchInfo.category3_id = '';
                    break;
                case 2:
                    this.categoryList2 = [];
                    this.categoryList3 = [];
                    this.searchInfo.category2_id = '';
                    this.searchInfo.category3_id = '';
                    this.categoryList2 = data.list;
                    break;
                case 3:
                    this.categoryList3 = [];
                    this.searchInfo.category3_id = '';
                    this.categoryList3 = data.list;
                    break;
            }
        },
        // 获取品牌
        async getBrandsOptios() {
            let params = {
                page: this.brandsOptiosData.page,
                pageSize: this.brandsOptiosData.pageSize,
            };
            if (this.brandsOptiosData.name)
                params.name = this.brandsOptiosData.name;
            this.brandsOptiosData.loading = true;
            const { code, data } = await getBrandsList(params);
            this.brandsOptiosData.loading = false;
            if (code === 0) {
                this.brandsOptiosData.brandsOptios = data.list;
                this.brandsOptiosData.total = data.total;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.h60 {
    height: 60px;
    line-height: 60px;
}
</style>
