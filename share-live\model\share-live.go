package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

// 共享直播分类
type ShareLiveCategory struct {
	source.Model
	Title    string `json:"title" form:"title" gorm:"column:title;comment:分类名称;"`             // 分类名称
	Sort     int    `json:"sort" form:"sort" gorm:"column:sort;comment:排序;type:int(11);"`       // 排序
	ImageUrl string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:分类图片;"` // 分类图片
}

// 是否同步共享直播的商品到小商店 -- 设置
type ShareLiveSmallShopSyn struct {
	source.Model
	UserId      uint `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`                                // 用户id
	SmallShopID uint `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:店主id;type:int(11);"` // 店主id
	IsSyn       int  `json:"is_syn" form:"is_syn" gorm:"column:is_syn;comment:是否同步1是0否;default:0"`                  // 是否同步1是0否
}

// 共享直播间 小商店店主开启记录表 -- 单独的记录表
type ShareLiveRoomSmallShop struct {
	source.Model
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id
	Status          int  `json:"status" form:"status" gorm:"column:status;comment:0关闭1开启;type:int(11);"`                      //状态0关闭1开启
	UserId          uint `json:"user_id" form:"user_id" gorm:"index"`                                                             // 店主会员id
	SmallShopID     uint `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:店主id;type:int(11);"`     // 店主id

}

// 共享直播间
type ShareLiveRoom struct {
	source.Model
	Title               string            `json:"title" form:"title" gorm:"column:title;comment:直播间标题;"`                                                // 直播间标题
	Sort                int               `json:"sort" form:"sort" gorm:"column:sort;comment:排序;type:int(11);"`                                            // 排序
	ShareLiveCategoryId uint              `json:"share_live_category_id" form:"share_live_category_id" gorm:"column:share_live_category_id;comment:分类id;"` // 分类id
	ImageUrl            string            `json:"image_url" form:"image_url" gorm:"column:image_url;comment:封面;"`                                          // 封面
	Status              int               `json:"status" form:"status" gorm:"column:status;comment:状态;type:int(11);"`                                      //状态0等待直播1直播中2已结束
	StartAt             *source.LocalTime `json:"start_at" form:"start_at" gorm:"column:start_at;comment:开始直播时间;"`                                     //开始直播时间 -- 创建时设置
	EndAt               *source.LocalTime `json:"end_at" form:"end_at" gorm:"column:end_at;comment:结束直播时间;"`                                           //结束直播时间 -- 后端按钮操作
	IsOpen              int               `json:"is_open" form:"is_open" gorm:"column:is_open;default:0;comment:开关0开1关;type:int(11);"`                   //状态0等待直播1直播中2已结束

	BeginTime *source.LocalTime `json:"begin_time" form:"begin_time" gorm:"column:begin_time;comment:推流开始时间;"` //推流开始时间 -- 云直播回调
	EndTime   *source.LocalTime `json:"end_time" form:"end_time" gorm:"column:end_time;comment:断流时间;"`           //断流时间 -- 云直播回调

	ThrustState int `json:"thrust_state" form:"thrust_state" gorm:"column:thrust_state;comment:推流状态0未推流，1推流中 2断流/结束直播;"`

	UserId           uint    `json:"user_id" form:"user_id" gorm:"index"`                                                                   // 主播会员id
	TotalTime        uint    `json:"total_time" form:"total_time" gorm:"column:total_time;comment:累计时长;"`                               //累计时长(秒)
	TotalNum         uint    `json:"total_num" form:"total_num" gorm:"column:total_num;comment:累计观看人数;"`                              //累计观看人数
	LikeNum          uint    `json:"like_num" form:"like_num" gorm:"column:like_num;comment:累计点赞人数;"`                                 //累计点赞人数
	OrderAmountTotal uint    `json:"order_amount_total" form:"order_amount_total" gorm:"column:order_amount_total;comment:带货订单总金额;"` //带货订单总金额
	OrderTotal       uint    `json:"order_total" form:"order_total" gorm:"column:order_total;comment:带货订单总数;"`                        //带货订单总数
	PeakBandwidth    float64 `json:"peak_bandwidth" form:"peak_bandwidth" gorm:"column:peak_bandwidth;comment:直播峰值;"`                   //带货订单总数
	TotalFlow        float64 `json:"total_flow" form:"total_flow" gorm:"column:total_flow;comment:总流量;"`                                 //总流量
	IsTranscribe     int     `json:"is_transcribe"  form:"is_transcribe" gorm:"column:is_transcribe;comment:是否有录制 1是0否;"`            //不传则是获取所有
	IsPlayback       int     `json:"is_playback"  form:"is_playback" gorm:"column:is_playback;default:1;comment:是否开启回放 1是2否;"`      //不传则是获取所有

	IsAccountImport int `json:"is_account_import"  form:"is_account_import" gorm:"column:is_account_import;default:2;comment:是否开加入im 1是2否;"` //是否开加入im
	IsCreateGroup   int `json:"is_create_group"  form:"is_create_group" gorm:"column:is_create_group;default:2;comment:是否创建群聊 1是2否;"`       //是否创建群聊

	Identifier           string         `json:"identifier" form:"identifier" gorm:"column:identifier;comment:im-用户唯一身份标识（使用"序号_用户id"）;"` //请求加入im 使用的唯一标识
	GroupId              string         `json:"group_id" form:"group_id" gorm:"column:group_id;comment:im创建群聊的群组id;"`                           //im创建群聊的群组id
	IsSpecifyApplication int            `json:"is_specify_application" form:"is_specify_application" gorm:"column:is_specify_application;default:0;comment:是否指定采购端0否1是;"`
	ApplicationIds       ApplicationIds `json:"application_ids" form:"application_ids" gorm:"column:application_ids;type:json;comment:采购端id;"`

	IsLandscape int `json:"is_landscape" form:"is_landscape" gorm:"column:is_landscape;default:0;comment:是否横屏 0否1是;type:int(11);"`

	//PlugFlowUrl    string    `json:"plug_flow_url" form:"plug_flow_url" gorm:"-"`  //推流地址--返回数据时生成 不保存
	//
	//PlayUrl    common.PlayUrl    `json:"play_url" form:"play_url" gorm:"-"`  //播放地址-返回数据时生成 不保存

}

type ApplicationIds []ApplicationId
type ApplicationId uint

func (value ApplicationIds) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *ApplicationIds) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

// 共享直播间 -- 直播开始与结束实际 -- 用于计算累计直播时间
type ShareLiveRoomTime struct {
	source.Model
	BeginTime       *source.LocalTime `json:"begin_time" form:"begin_time" gorm:"column:begin_time;comment:推流开始时间;"`                     //推流开始时间 -- 云直播回调
	EndTime         *source.LocalTime `json:"end_time" form:"end_time" gorm:"column:end_time;comment:断流时间;"`                               //断流时间 -- 云直播回调
	ShareLiveRoomId uint              `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id

}

// 共享直播间 商品关联表
type ShareLiveRoomProduct struct {
	source.Model
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id
	ProductId       uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;"`                           // 商品id
}

// 共享直播间 商品关联表
type ShareLiveRoomRecordFile struct {
	source.Model
	ShareLiveRoomId uint   `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"`                // 直播间id
	StreamID        string `json:"stream_id" form:"stream_id" gorm:"column:stream_id;comment:直播流名称;"`                                         //直播流名称
	ChannelID       string `json:"channel_id"  form:"channel_id" gorm:"column:channel_id;comment:直播间标识;"`                                     //直播间标识
	FileID          string `json:"file_id"  form:"file_id" gorm:"column:file_id;comment:点播 file ID，在 云点播平台 可以唯一定位一个点播视频文件;"` //点播 file ID，在 云点播平台 可以唯一定位一个点播视频文件
	FileFormat      string `json:"file_format" form:"file_formats" gorm:"column:file_formats;comment:FLV，HLS，MP4，AAC;"`                            //FLV，HLS，MP4，AAC
	Duration        int    `json:"duration" form:"duration" gorm:"column:duration;comment:录制文件时长，单位秒;"`                                   //录制文件时长，单位秒
	FileSize        int    `json:"file_size" form:"file_size" gorm:"column:file_size;comment:录制文件大小，单位字节;"`                              //录制文件大小，单位字节
	VideoURL        string `json:"video_url" form:"video_url" gorm:"column:video_url;comment:录制文件下载 URL;"`                                   //录制文件下载 URL
}

// 共享直播间 采购端关联表
type ShareLiveRoomApplication struct {
	source.Model
	ShareLiveRoomId  uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"`       // 直播间id
	ApplicationId    uint `json:"application_id" form:"application_id" gorm:"column:application_id;comment:采购端id;"`                   // 采购端id
	OrderAmountTotal uint `json:"order_amount_total" form:"order_amount_total" gorm:"column:order_amount_total;comment:带货订单总金额;"` //带货订单总金额
	OrderTotal       uint `json:"order_total" form:"order_total" gorm:"column:order_total;comment:带货订单总数;"`                        //带货订单总数
	TotalNum         uint `json:"total_num" form:"total_num" gorm:"column:total_num;comment:累计观看人数;"`                              //累计观看人数
	LikeNum          uint `json:"like_num" form:"like_num" gorm:"column:like_num;comment:累计点赞人数;"`                                 //累计点赞人数

}

// 共享直播间 -- 标记喜欢
type ShareLiveRoomLike struct {
	source.Model
	SmallUserId     uint `json:"small_user_id" form:"small_user_id" gorm:"column:small_user_id;comment:小商店会员id;"`            // 小商店会员id
	ShareLiveRoomId uint `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"` // 直播间id
	Status          int  `json:"status" form:"status" gorm:"column:status;comment:0取消喜欢1喜欢;type:int(11);"`                  //状态0取消喜欢1喜欢

}
