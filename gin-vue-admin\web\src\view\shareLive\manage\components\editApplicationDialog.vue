<template>
  <el-dialog
      title="修改采购端"
      :visible="isShow"
      width="500px"
      :before-close="handleClose">
    <el-form :model="formData" label-width="120px" ref="form">
      <!-- 是否指定采购端选项 -->
      <el-form-item label="指定采购端:">
        <el-switch v-model="formData.is_specify_application" :active-value="1"
                   :inactive-value="0" @change="handleApplicationSwitch"></el-switch>
      </el-form-item>

      <!-- 采购端多选框组 (当开关打开时显示) -->
      <el-form-item v-if="formData.is_specify_application == 1" label="选择采购端:" prop="application_ids">
        <el-select
            v-model="formData.application_ids"
            multiple
            collapse-tags
            placeholder="请选择采购端"
            style="width: 100%"
            filterable
        >
          <el-option
              v-for="item in applicationOption"
              :key="item.id"
              :label="item.app_name"
              :value="item.id"
          ></el-option>
        </el-select>
        <p class="color-grap" v-if="applicationOption.length === 0">暂无可用采购端，请先配置采购端信息</p>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {getApplicationOption, saveShareLiveRoomApplicationSetting} from "@/api/shareLive";

export default {
  data() {
    return {
      isShow: false,
      applicationOption: [],
      formData: {
        id: null,
        is_specify_application: 0,
        application_ids: []
      }
    }
  },
  methods: {
    init(row) {
      this.isShow = true;
      this.formData.id = row.id;
      this.formData.is_specify_application = row.is_specify_application || 0;
      this.formData.application_ids = row.application_ids || [];
      this.getApplicationOptions();
    },

    // 获取采购端选项
    async getApplicationOptions() {
      const {code, data} = await getApplicationOption();
      if (code === 0) {
        this.applicationOption = data.list;
      }
    },

    // 开关切换处理
    handleApplicationSwitch(value) {
      if (!value) {
        this.formData.application_ids = [];
      }
    },

    handleClose() {
      this.isShow = false;
      this.formData = {
        id: null,
        is_specify_application: 0,
        application_ids: []
      };
    },

    confirm() {
      this.$refs.form.validate(async valid => {
        if (!valid) return;

        const params = {
          share_live_room_id: this.formData.id,
          is_specify_application: this.formData.is_specify_application,
          application_ids: this.formData.application_ids
        };

        const res = await saveShareLiveRoomApplicationSetting(params);
        if (res.code === 0) {
          this.$message.success('修改成功');
          this.handleClose();
          this.$emit('reLoad');
        }
      });
    }
  }
}
</script>
<style scoped lang="scss">
.color-grap {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>