package route

import (
	"github.com/gin-gonic/gin"
	"share-live/api/a"
	"share-live/api/app"
	"share-live/api/callback"
	v1 "share-live/api/v1"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	shareLiveRouter := Router.Group("shareLive")
	{
		shareLiveRouter.POST("saveShareLiveRoomApplicationSetting", a.SaveShareLiveRoomApplicationSetting)
		shareLiveRouter.GET("getSysShareLiveSetting", a.GetSysShareLiveSetting)    //获取基础设置
		shareLiveRouter.POST("saveSysShareLiveSetting", a.SaveSysShareLiveSetting) //保存基础设置

		shareLiveRouter.GET("getShareLiveCategoryList", a.GetShareLiveCategoryList)  //共享直播分类列表
		shareLiveRouter.GET("getShareLiveCategorys", a.GetShareLiveCategorys)        //获取所有共享直播分类
		shareLiveRouter.DELETE("deleteShareLiveCategory", a.DeleteShareLiveCategory) //删除共享直播分类
		shareLiveRouter.GET("getShareLiveCategoryById", a.GetShareLiveCategoryById)  //通过id直播分类详情
		shareLiveRouter.POST("createShareLiveCategory", a.CreateShareLiveCategory)   //创建分类
		shareLiveRouter.POST("saveShareLiveCategory", a.SaveShareLiveCategory)       //修改分类

		shareLiveRouter.POST("createShareLiveRoom", a.CreateShareLiveRoom)  //创建直播间
		shareLiveRouter.POST("saveShareLiveRoom", a.SaveShareLiveRoom)      //修改直播间
		shareLiveRouter.GET("getShareLiveRoomById", a.GetShareLiveRoomById) //通过id获取直播详情
		shareLiveRouter.GET("getShareLiveRoomList", a.GetShareLiveRoomList) //直播间列表
		shareLiveRouter.GET("getShareLiveRooms", a.GetShareLiveRooms)       //获取所有直播间

		shareLiveRouter.GET("getShareLiveRoomRecordFileByRoomId", a.GetShareLiveRoomRecordFileByRoomId) //通过直播间id获取直播回放文件
		shareLiveRouter.GET("saveShareLiveRoomStart", a.SaveShareLiveRoomStart)                         //开始直播
		shareLiveRouter.GET("saveShareLiveRoomEnd", a.SaveShareLiveRoomEnd)                             //结束直播
		shareLiveRouter.GET("SaveShareLiveRoomIsPlayBack", a.SaveShareLiveRoomIsPlayBack)               //是否开启回放

		shareLiveRouter.GET("getShareLiveRoomApplicationList", a.GetShareLiveRoomApplicationList) //直播间--采购端列表

		shareLiveRouter.POST("saveShareLiveRoomIsOpen", a.SaveShareLiveRoomIsOpen)        // 修改直播间开关状态
		shareLiveRouter.POST("saveShareLiveRoomIsLandscape", a.SaveShareLiveRoomIsLandscape) // 修改直播间横屏状态
		//SynShareLive

	}

}

// 后台公共
func InitAdminPublicRouter(Router *gin.RouterGroup) {
	shareLiveRouter := Router.Group("shareLive")
	{
		shareLiveRouter.GET("synShareLive", v1.SynShareLive) //直播间--采购端列表

		shareLiveRouter.POST("begin", callback.Begin)                   //直播开始 推流回调
		shareLiveRouter.POST("end", callback.End)                       //直播结束/断流回调
		shareLiveRouter.POST("record", callback.Record)                 //录像回调
		shareLiveRouter.POST("snapshot", callback.Snapshot)             //截图回调
		shareLiveRouter.POST("pornCensorship", callback.PornCensorship) //鉴黄回调
		shareLiveRouter.GET("getHang", a.GetHang)                       //结束直播

	}

}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {
	shareLiveRouter := Router.Group("shareLive")
	{
		shareLiveRouter.GET("getShareLiveCategorys", a.GetShareLiveCategorys) //获取所有共享直播分类

		shareLiveRouter.GET("getSysShareLiveSettingIsSyn", v1.GetSysShareLiveSettingIsSyn)                           //获取设置同步状态
		shareLiveRouter.POST("saveSysShareLiveSettingIsSyn", v1.SaveSysShareLiveSettingIsSyn)                        //保存设置同步状态
		shareLiveRouter.GET("getShareLiveRoomList", v1.GetShareLiveRoomList)                                         //直播间列表
		shareLiveRouter.GET("getShareLiveRoomProductListByShareLiveId", v1.GetShareLiveRoomProductListByShareLiveId) //通过直播间id获取直播间商品

		shareLiveRouter.POST("saveShareLiveRoomSmallShop", v1.SaveShareLiveRoomSmallShop) //保存修改直播间开启关闭状态 -- 单独

		shareLiveRouter.POST("saveShareLiveRoomSmallShopAll", v1.SaveShareLiveRoomSmallShopAll) //一键开启或者一键关闭
		shareLiveRouter.GET("getShareLiveRoomById", a.GetShareLiveRoomById)                     //通过id获取直播详情(包括商品)

	}

}

// 前端公共
func InitUserPublicRouter(Router *gin.RouterGroup) {
	shareLiveRouter := Router.Group("shareLive")
	{
		shareLiveRouter.GET("getShareLiveCategorysBySmallShopId", v1.GetShareLiveCategorysBySmallShopId) //通过小商店id获取店主开启直播的分类
		shareLiveRouter.GET("getShareLiveRoomListBySmallShopId", v1.GetShareLiveRoomListBySmallShopId)   //通过小商店id获取店主开启直播的列表
		shareLiveRouter.GET("getShareLiveRoomByIdAndSmallShopId", v1.GetShareLiveRoomByIdAndSmallShopId) //通过id获取直播详情 包含商品
		shareLiveRouter.POST("getShaveLiveRoomPoster", v1.GetShaveLiveRoomPoster)                        //生成海报

	}

}

// 小商店私有API
func InitSmallShopUserPublicRouter(Router *gin.RouterGroup) {
	shareLiveRouter := Router.Group("shareLive")
	{
		shareLiveRouter.POST("saveLikeNum", v1.SaveLikeNum)                                                       //增加,减少点赞人数
		shareLiveRouter.POST("saveTotalNum", v1.SaveTotalNum)                                                     //增加观看人数
		shareLiveRouter.POST("getShaveLiveRoomPoster", v1.GetShaveLiveRoomPoster)                                 //生成海报
		shareLiveRouter.POST("genSig", v1.GenSig)                                                                 //im用户登录密码UserSig
		shareLiveRouter.POST("sendGroupMsg", v1.SendGroupMsg)                                                     //群组系统消息
		shareLiveRouter.POST("GetLike", v1.GetLike)                                                               //获取是否标记了喜欢 1是0否
		shareLiveRouter.GET("getShareLiveRoomRecordFileByRoomId", v1.GetSmallShopShareLiveRoomRecordFileByRoomId) //通过直播间id获取直播回放文件
		shareLiveRouter.GET("getSysShareLiveSetting", v1.GetSysShareLiveSetting)                                  //获取开关和sdkappid
	}

}

// 采购端API
func InitAppPrivateRouter(Router *gin.RouterGroup) {
	shareLiveRouter := Router.Group("shareLive")
	{
		shareLiveRouter.GET("getShareLiveCategorys", a.GetShareLiveCategorys)              //获取所有共享直播分类
		shareLiveRouter.GET("getShareLiveRoomList", app.GetShareLiveRoomList)              //直播间列表
		shareLiveRouter.POST("getShareLiveRoomProductList", a.GetShareLiveRoomProductList) //直播间列表
		shareLiveRouter.GET("getShareLiveRoomById", a.GetShareLiveRoomById)                //通过id获取直播详情
		shareLiveRouter.POST("getShareLiveRoomByIds", a.GetShareLiveRoomByIds)             //通过id获取直播详情

		shareLiveRouter.GET("getShareLiveRoomNumById", a.GetShareLiveRoomNumById) //通过id获取累计观看人数

		shareLiveRouter.POST("saveTotalNum", app.SaveTotalNum)   //增加观看人数
		shareLiveRouter.POST("reduceLikeNum", app.ReduceLikeNum) //减少点赞人数
		shareLiveRouter.POST("addLikeNum", app.AddLikeNum)       //增加点赞人数

		shareLiveRouter.POST("createShareLiveRoomApplication", app.CreateShareLiveRoomApplication) //增加导入记录
		shareLiveRouter.POST("genSig", app.GenSig)                                                 //im用户登录密码UserSig
		shareLiveRouter.POST("sendGroupMsg", app.SendGroupMsg)                                     //群组系统消息

		shareLiveRouter.GET("getShareLiveRoomRecordFileByRoomId", app.GetShareLiveRoomRecordFileByRoomId) //通过直播间id获取直播回放文件
		shareLiveRouter.GET("getSysShareLiveSetting", app.GetSysShareLiveSetting)                         //获取开关和sdkappid

	}

}
