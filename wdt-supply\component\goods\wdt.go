package goods

import (
	catemodel "category/model"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	log2 "log"
	pmodel "product/model"
	service2 "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	publicModel "public-supply/model"
	"public-supply/request"
	service3 "public-supply/service"
	"public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"wdt-supply/model"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"

	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Wdt struct {
	WdtData struct {
		BaseInfo struct {
			Sid       string `json:"sid"`
			Appkey    string `json:"appkey"`
			Appsecret string `json:"appsecret"`
		} `json:"baseInfo"`
		UpdateInfo setting.UpdateInfoData `json:"update"`
		Pricing    setting.PricingData    `json:"pricing"`
		Management setting.Management     `json:"management"`
	}
	GatherSupplyID uint
}

func (self *Wdt) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (self *Wdt) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func (self *Wdt) ImportSelectGoodsRun(info publicModel.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []string
	var field string
	field = "code"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []string
	idsArr = GetIdArrs(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToStringArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []publicModel.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.SN == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 100)
	for index, item := range arrList {
		fmt.Println("循环", index)
		self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (self *Wdt) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service3.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service3.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (y *Wdt) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	// 聚水潭供应链请求接口时，未使用供应链配置信息
	// 实际使用配置存储位置在应用--工具类--聚水潭，配置项为“wdt_setting”
	return
}

func (wdt *Wdt) InitSetting(gatherSupplyID uint) (err error) {
	var sysSetting model2.SysSetting
	err, sysSetting = setting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	wdt.GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &wdt.WdtData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if wdt.WdtData.BaseInfo.Appkey == "" || wdt.WdtData.BaseInfo.Appsecret == "" || wdt.WdtData.BaseInfo.Sid == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}
func (s *Wdt) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (s *Wdt) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

func (*Wdt) InitGoods() (err error) {

	return
}

func (wdt *Wdt) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	var wdtGoods []model.WdtGoodsDetail
	db := source.DB().Model(&model.WdtGoodsDetail{}).Preload("GoodsSpecProducts")
	//db.Where("gather_supply_id = ?", info.GatherSupplyID)

	if info.SearchWords != "" {
		db.Where("`goods_name` like ?", "%"+info.SearchWords+"%")
	}
	if info.IsImport > 0 {
		var wdtProductIds []uint
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("deleted_at is NULL").Pluck("source_goods_id", &wdtProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`goods_id` in ?", wdtProductIds)

		} else if info.IsImport == 2 {
			if len(wdtProductIds) > 0 {
				db.Where("`goods_id` not in ?", wdtProductIds)
			}
		}

	}
	if info.CategoryID > 0 {
		//db.Where("`c_id` = ?", info.CategoryID)
	}
	if info.WdtShopId != "" {
		db.Where("`supplier_no` = ?", info.WdtShopId)
	}
	if info.RangeType != "" {
		if info.RangeType == "agreement_price" {
			db.Where("`goods_price` >= ?", info.RangeForm*100)
			db.Where("`goods_price` <= ?", info.RangeTo*100)
		}
		if info.RangeType == "market_price" {
			db.Where("`suggested_price` >= ?", info.RangeForm*100)
			db.Where("`suggested_price` <= ?", info.RangeTo*100)
		}

	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&wdtGoods).Error

	data = wdt.ProductToGoods(wdtGoods, info.GatherSupplyID)
	return
}

func (wdt *Wdt) ProductToGoods(data []model.WdtGoodsDetail, gatherID uint) (list []publicModel.Goods) {
	var ids []int
	source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", wdt.GatherSupplyID).Pluck("source_goods_id", &ids)
	var shops []model.WdtShop
	source.DB().Find(&shops)
	var shopMap = make(map[string]model.WdtShop)
	for _, shop := range shops {
		shopMap[shop.ShopId] = shop
	}
	for _, v := range data {

		var isImport = 0
		for _, id := range ids {
			if v.GoodsId == id {
				isImport = 1
			}
		}
		var rate float64
		if len(v.GoodsSpecProducts) == 0 {
			continue
		}
		if v.SuggestedPrice > v.GoodsPrice {
			rate = service2.Decimal((v.SuggestedPrice - v.GoodsPrice) / (v.SuggestedPrice))
		}
		var thirdCateName string
		for k, category := range v.CategoryList {
			if k == len(v.CategoryList)-1 {
				thirdCateName = category.NodeNamePath
			}
		}
		var cover string
		for _, img := range v.GoodsImages {
			cover = img.ImgUrl
		}
		var shopName string
		if _, ok := shopMap[v.SupplierNo]; ok {
			shopName = shopMap[v.SupplierNo].Name
		}
		var item publicModel.Goods
		item = publicModel.Goods{
			GatherSupplyID:    gatherID,
			ThirdCategoryName: thirdCateName,
			ThirdBrandName:    "",
			IsImport:          uint(isImport),
			ID:                int(v.GoodsId),
			ProductID:         int(0),
			TotalStock:        0,
			Cover:             cover,
			Status:            1,
			Stock:             0,
			Title:             v.GoodsName,
			CategoryIds:       []string{},
			GuidePrice:        uint(v.SuggestedPrice * 100),
			Rate:              utils.Decimal(rate * 100),
			SalePrice:         uint(v.SuggestedPrice * 100),
			AgreementPrice:    uint(v.GoodsPrice * 100),
			CostPrice:         uint(v.GoodsPrice * 100),
			ActivityPrice:     uint(v.SuggestedPrice * 100),
			MarketPrice:       uint(v.SuggestedPrice * 100),
			SN:                v.GoodsSn,
			ShopName:          shopName,
		}
		if item.MarketPrice < item.AgreementPrice {
			item.Rate = 0
		}
		list = append(list, item)
	}
	return

}

func (wdt *Wdt) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	db := source.DB().Model(&model.WdtGoodsDetail{})
	//db.Where("gather_supply_id = ?", info.GatherSupplyID)

	if info.SearchWords != "" {
		db.Where("`item_name` like ?", "%"+info.SearchWords+"%")
	}
	if info.IsImport > 0 {
		var wdtProductIds []string
		err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Where("deleted_at is NULL").Pluck("code", &wdtProductIds).Error
		if err != nil {
			return
		}

		if info.IsImport == 1 {
			db.Where("`style_code` in ?", wdtProductIds)

		} else if info.IsImport == 2 {
			if len(wdtProductIds) > 0 {
				db.Where("`style_code` not in ?", wdtProductIds)
			}
		}

	}
	if info.CategoryID > 0 {
		//db.Where("`c_id` = ?", info.CategoryID)
	}
	if info.WdtShopId != "" {
		db.Where("`supplier_no` = ?", info.WdtShopId)
	}
	if info.RangeType != "" {
		if info.RangeType == "guide_price" {
			db.Where("`sale_price` >= ?", info.RangeForm*100)
			db.Where("`sale_price` <= ?", info.RangeTo*100)
		}
		if info.RangeType == "agreement_price" {
			db.Where("`supply_price` >= ?", info.RangeForm*100)
			db.Where("`supply_price` <= ?", info.RangeTo*100)
		}

	}

	var total int64
	err = db.Count(&total).Error
	searchText, err := json.Marshal(info)

	orderPN := GetOrderNo()
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(total),
		Status:            1,
		SearchCriteria:    string(searchText),
	}

	source.DB().Omit("goods_arr").CreateInBatches(&goodsRecord, 500)

	err = wdt.RunGoodsConcurrent(nil, info, db, 1, orderPN)
	if err != nil {
		return
	}
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

func (wdt *Wdt) RunGoodsConcurrent(wg *sync.WaitGroup, info request.GetGoodsSearch, db *gorm.DB, i int, orderPN string) (err error) {

	var ProductItem []model.WdtGoodsDetail
	err = db.Find(&ProductItem).Error
	if err != nil {
		return
	}
	if len(ProductItem) > 0 {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
			cateId3, err = strconv.Atoi(cateList[2])
		}

		var Item []publicModel.Goods

		var resultArr []string
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Pluck("code", &resultArr).Error
		if err != nil {
			return
		}

		if len(ProductItem) <= 0 {
			fmt.Println("没有选择可导入的数据")

			return
		}

		Item = wdt.ProductToGoods(ProductItem, info.GatherSupplyID)
		idsArr := GetIdArrs(Item)

		difference := collection.Collect(idsArr).Diff(resultArr).ToStringArray()

		//fmt.Println("查询到的导入数据：", idsArr)
		//fmt.Println("已经存在的数据：", resultArr)
		//fmt.Println("目前可以导入的数据：", difference)

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))

		if len(difference) <= 0 {
			fmt.Println("没有可以导入的数据")
			err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", orderPN).Update("status", 2).Error

			err = errors.New("当前数据已经存在，请导入新数据")
			return
		}
		var goodsList []publicModel.Goods

		for _, v := range difference {

			for _, item := range Item {
				if item.SN == v {
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product
		err, listGoods, _ = wdt.CommodityAssembly(goodsList, cateId1, cateId2, cateId3, info.GatherSupplyID, 0)

		if len(listGoods) > 0 {
			service3.FinalProcessing(listGoods, orderPN)
		}

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

// 商品组装
func (wdt *Wdt) CommodityAssembly(list []publicModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isUpdate int) (err error, listGoods []*pmodel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {
	idArr := GetIdArr(list)
	var data map[uint]model.WdtGoodsDetail
	err, data = wdt.BatchGetGoodsDetails(idArr, isUpdate)
	if err != nil {
		return
	}
	//_, detailData := BatchGetGoodsDetails(ids) //批量获取详情
	var riskManageRecord []publicModel.RiskManagementRecord
	var shops []model.WdtShop
	err = source.DB().Find(&shops).Error
	if err != nil {
		return
	}
	var shopMap = make(map[string]model.WdtShop)
	var virtualStockShops []string
	for _, shop := range shops {
		shopMap[shop.ShopId] = shop
		if shop.IsVirtualStock == 1 {
			virtualStockShops = append(virtualStockShops, shop.ShopId)
		}
	}
	var sourceGoodsIds []uint
	for _, elem := range list {
		detail, ok := data[uint(elem.ID)]
		if !ok && isUpdate == 0 {
			continue
		}
		goods := new(pmodel.Product)

		if isUpdate == 1 {
			goods.ID = uint(elem.ID)
		}
		goods.Title = detail.GoodsName
		var intXAdvice uint64
		if wdt.WdtData.Pricing.SupplyAdvice == 1 {
			intXAdvice, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceGuide, 10, 32)
			goods.OriginPrice = uint(detail.SuggestedPrice*100) * uint(intXAdvice) / 100
		} else if wdt.WdtData.Pricing.SupplyAdvice == 2 {
			intXAdvice, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceAgreement, 10, 32)
			goods.OriginPrice = uint(detail.SuggestedPrice*100) * uint(intXAdvice) / 100
		} else {
			goods.OriginPrice = uint(detail.SuggestedPrice * 100)
		}
		var intX uint64
		if wdt.WdtData.Pricing.SupplySales == 1 {
			intX, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesGuide, 10, 32)
			goods.Price = uint(detail.GoodsPrice*100) * uint(intX) / 100
		} else if wdt.WdtData.Pricing.SupplySales == 2 {
			intX, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesAgreement, 10, 32)
			goods.Price = uint(detail.GoodsPrice*100) * uint(intX) / 100
		} else {
			goods.Price = uint(detail.GoodsPrice * 100)
		}
		goods.GuidePrice = goods.OriginPrice
		goods.CostPrice = uint(detail.GoodsPrice * 100)
		goods.ActivityPrice = goods.GuidePrice
		goods.Stock = elem.Stock
		//var status int
		//if detail.ItemStatus == "cantDistribution" {
		//	status = 1
		//} else {
		//	status = 0
		//}
		goods.ImageUrl = elem.Cover
		goods.Unit = elem.Unit
		goods.Unit = "默认"
		goods.Source = common.WDT_SOURCE
		goods.SourceGoodsID = uint(detail.GoodsId)
		goods.SourceGoodsIDString = elem.SN
		goods.GatherSupplyID = wdt.GatherSupplyID
		goods.IsDisplay = 1

		var riskRecord publicModel.RiskManagementRecord
		riskRecord.ProductID = goods.ID
		riskRecord.SourceGoodsID = goods.SourceGoodsID
		riskRecord.GatherSupplyID = goods.GatherSupplyID
		if wdt.WdtData.Management.ProductPriceStatus == 1 {
			if goods.Price < goods.CostPrice*(wdt.WdtData.Management.Products/100) {
				goods.IsDisplay = 0
				riskManageRecord = append(riskManageRecord, riskRecord)

			}
		} else if wdt.WdtData.Management.ProductPriceStatus == 2 {
			if (goods.Price-goods.CostPrice)/goods.CostPrice < wdt.WdtData.Management.Profit/100 {
				goods.IsDisplay = 0
				riskManageRecord = append(riskManageRecord, riskRecord)

			}
		}
		goods.SupplySupplierId = detail.SupplierNo
		if _, jok := shopMap[goods.SupplySupplierId]; jok {
			goods.FreightType = 1
			goods.FreightTemplateID = shopMap[goods.SupplySupplierId].FreightTemplateID
			if shopMap[goods.SupplySupplierId].IsOpen == 1 {
				//走单独定价策略
				err, goods.CostPrice, goods.Price, goods.OriginPrice, goods.ActivityPrice, goods.GuidePrice = GetShopPricingPrice(uint(detail.GoodsPrice*100), shopMap[goods.SupplySupplierId])
			}
			goods.ShopName = shopMap[goods.SupplySupplierId].Name

		}
		for _, mil := range detail.GoodsImages {
			goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
				Type: 1,
				Src:  mil.ImgUrl,
			})
		}
		goods.DetailImages += detail.Description

		if cateId1 == 0 || cateId2 == 0 || cateId3 == 0 {

			cateList := strings.Split(elem.ThirdCategoryName, ">")
			var cate1, cate2, cate3 catemodel.Category
			display := 1
			if len(cateList) > 0 && cateList[0] != "" {

				cate1.IsDisplay = &display
				cate1.ParentID = 0
				cate1.Level = 1
				cate1.Name = cateList[0]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[0], 1, 0).FirstOrCreate(&cate1)
			} else {
				cate2.IsDisplay = &display
				cate2.ParentID = 0
				cate2.Level = 1
				cate2.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate1.Name, cate1.Level, cate1.ParentID).FirstOrCreate(&cate1)

			}

			if len(cateList) > 1 && cateList[1] != "" {
				cate2.IsDisplay = &display
				cate2.ParentID = cate1.ID
				cate2.Level = 2
				cate2.Name = cateList[1]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 2, cate1.ID).FirstOrCreate(&cate2)
			} else {
				cate2.IsDisplay = &display
				cate2.ParentID = cate1.ID
				cate2.Level = 2
				cate2.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate2.Name, cate2.Level, cate2.ParentID).FirstOrCreate(&cate2)

			}

			if len(cateList) > 2 && cateList[2] != "" {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				cate3.Name = cateList[1]
				source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 3, cate2.ID).FirstOrCreate(&cate3)
			} else {
				cate3.IsDisplay = &display
				cate3.ParentID = cate2.ID
				cate3.Level = 3
				cate3.Name = "默认"
				source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, cate3.Level, cate3.ParentID).FirstOrCreate(&cate3)
			}

			goods.Category1ID = cate1.ID
			goods.Category2ID = cate2.ID
			goods.Category3ID = cate3.ID
		} else {
			goods.Category1ID = uint(cateId1)
			goods.Category2ID = uint(cateId2)
			goods.Category3ID = uint(cateId3)
		}

		//if detail.Brand != "" {
		//	var brand model3.Brand
		//	brand.Name = detail.Brand
		//	source.DB().Where("`name` = ?", brand.Name).FirstOrCreate(&brand)
		//	goods.BrandID = brand.ID
		//	goods.FreightType = 0
		//	goods.GatherSupplyID = elem.GatherSupplyID
		//}

		/**
		处理轮播图
		*/

		/**
		处理轮播图结束
		*/

		goods.MinPrice = uint(int(goods.Price))
		goods.MaxPrice = uint(int(goods.Price))
		var totalStock int
		//var spec = make(map[string][]string)
		//var specKeys []string

		//var canFixSku = 0
		//goods.DetailImages = "<p>"
		//for _, idi := range detail.ItemPhoto.ItemDetailImages {
		//	goods.DetailImages += "<img src=\"" + idi + "\">"
		//}
		//goods.DetailImages += "</p>"
		//
		//for _, mil := range detail.ItemPhoto.MainImageList {
		//	goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
		//		Type: 1,
		//		Src:  mil,
		//	})
		//}
		var minProfitRate float64
		for sk, detailSku := range detail.GoodsSpecProducts {
			var sku = pmodel.Sku{}
			attrs := strings.Split(detailSku.PrdDesc, ":")
			if len(attrs) > 1 {
				attrsString := attrs[1]
				attrsB := strings.Split(attrsString, "；")
				if len(attrsB) > 1 {
					sku.Options = append(sku.Options, pmodel.Option{
						SpecName:     attrsB[0],
						SpecItemName: attrsB[1],
					})
				}
			}
			var intXSku uint64
			if wdt.WdtData.Pricing.SupplyAdvice == 1 {
				intXSku, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceGuide, 10, 32)
				sku.OriginPrice = uint(detailSku.PrdPrice*100) * uint(intXSku) / 100
			} else if wdt.WdtData.Pricing.SupplyAdvice == 2 {
				intXSku, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceAgreement, 10, 32)
				sku.OriginPrice = uint(detailSku.PrdPrice*100) * uint(intXSku) / 100
			} else {
				sku.OriginPrice = uint(detailSku.PrdPrice * 100)
			}
			var intXS uint64
			if wdt.WdtData.Pricing.SupplySales == 1 {
				intXS, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesGuide, 10, 32)
				sku.Price = uint(detailSku.DistributionPrice*100) * uint(intXS) / 100
			} else if wdt.WdtData.Pricing.SupplySales == 2 {
				intXS, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesAgreement, 10, 32)
				sku.Price = uint(detailSku.DistributionPrice*100) * uint(intXS) / 100
			} else {
				sku.Price = uint(detailSku.DistributionPrice * 100)
			}
			sku.Title = detailSku.PrdDesc

			sku.Weight = int(detailSku.PrdWeight * 1000)
			if sku.Weight == 0 {
				sku.Weight = 300
			}
			sku.CostPrice = uint(detailSku.DistributionPrice * 100)
			sku.IsDisplay = 1
			if detailSku.IsSale == true {
				sku.Stock = int(detailSku.PrdNumber)
				if collection.Collect(virtualStockShops).Contains(goods.SupplySupplierId) {
					//水滴新供应链虚拟库存,都上架
					if sku.Stock == 0 {
						sku.Stock = 9999
					}

				}
			} else {
				sku.Stock = 0
			}
			totalStock += sku.Stock
			sku.GuidePrice = sku.OriginPrice
			sku.ActivityPrice = sku.OriginPrice
			sku.Sn = detailSku.PrdSn
			sku.Code = detailSku.SpecId
			if len(detailSku.ProductImages) > 0 {
				sku.ImageUrl = detailSku.ProductImages[0].ImgUrl
			}
			if _, jok := shopMap[goods.SupplySupplierId]; jok {
				if shopMap[goods.SupplySupplierId].IsOpen == 1 {
					//走单独定价策略
					err, sku.CostPrice, sku.Price, sku.OriginPrice, sku.ActivityPrice, sku.GuidePrice = GetShopPricingPrice(uint(detailSku.DistributionPrice*100), shopMap[goods.SupplySupplierId])
					if err != nil {
						continue
					}
				}
			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.OriginPrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			//err = source.Redis().LPush(context.Background(), "wdtSkuStockSync", sku.Sn).Err()
			goods.Skus = append(goods.Skus, sku)

		}
		if len(goods.Skus) > 0 {
			goods.ProfitRate = minProfitRate
		}
		//for _, ups := range detail.Ups {
		//	goods.Attrs = append(goods.Attrs, pmodel.Attr{
		//		Name:  ups.PName,
		//		Value: ups.PvValue,
		//	})
		//}

		if totalStock == 0 {
			goods.IsDisplay = 0
		}
		if detail.DeleteStatus != 0 {
			goods.IsDisplay = 0
		}
		//处理资质json图片数组

		//--------处理详情json图片数组结束

		//处----------------理属性json数组

		//---------处理属性json数组结束
		//goods.Desc=detail.Description

		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
			//err = source.Redis().LPush(context.Background(), "wdtUploadProduct", goods.SourceGoodsID).Err()
			sourceGoodsIds = append(sourceGoodsIds, goods.SourceGoodsID)
		} else {
			fmt.Println("无规格商品，不导入", goods.ID)
		}

	}
	err = source.DB().CreateInBatches(&riskManageRecord, 1000).Error
	if err != nil {
		return
	}
	//err = service.SetUploadProductIds(sourceGoodsIds)
	return
}

// 批量获取商品详情
func (wdt *Wdt) BatchGetGoodsDetails(ids []int, isUpdate int) (err error, data map[uint]model.WdtGoodsDetail) {
	var detailList = make(map[uint]model.WdtGoodsDetail)

	fmt.Println("BatchGetGoodsDetails:", ids)
	var list []model.WdtGoodsDetail
	err = source.DB().Preload("GoodsSpecProducts").Where("`goods_id` in ?", ids).Find(&list).Error
	if err != nil {
		return
	}

	var exitsList []pmodel.Product
	err = source.DB().Where("`code` in ?", ids).Where("`gather_supply_id` = ?", wdt.GatherSupplyID).Find(&exitsList).Error
	if err != nil {
		return
	}
	var exitsMap = make(map[uint]pmodel.Product)
	for _, e := range exitsList {
		exitsMap[e.SourceGoodsID] = e
	}
	fmt.Println("总解析数量：", len(list))
	for _, item := range list {
		if _, ok := exitsMap[uint(item.GoodsId)]; ok && isUpdate == 0 {
			continue
		}
		detailList[uint(item.GoodsId)] = item
	}
	fmt.Println("总解析数量1：", len(detailList))

	data = detailList
	return
}

func (*Wdt) GetGroup() (err error, data interface{}) {

	return

}

func (*Wdt) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {

	return
}

func (*Wdt) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	var wdtCate []model.WdtCategory
	err = source.DB().Where("parent_id = ?", pid).Find(&wdtCate).Error
	if err != nil {
		return
	}
	return nil, wdtCate
}
func (*Wdt) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	return
}

// 选品库增加商品
func (*Wdt) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {

	return

}

func (wdt *Wdt) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	var allProducts []service2.ProductForUpdate
	var allJstProductsMap = make(map[int]model.WdtGoodsDetail)
	err = source.DB().Preload("Skus").Where("source_goods_id in ?", GoodsData.Data.GoodsIds).Where("gather_supply_id = ?", wdt.GatherSupplyID).Find(&allProducts).Error
	if err != nil {
		return
	}
	if len(allProducts) == 0 {
		return
	}
	var list []model.WdtGoodsDetail
	err = source.DB().Preload("GoodsSpecProducts").Where("`goods_id` in ?", GoodsData.Data.GoodsIds).Where("deleted_at is null").Find(&list).Error
	if err != nil {
		return
	}
	for _, item := range list {
		allJstProductsMap[item.GoodsId] = item
	}
	var shops []model.WdtShop
	source.DB().Find(&shops)
	var shopMap = make(map[string]model.WdtShop)
	for _, shop := range shops {
		shopMap[shop.ShopId] = shop
	}
	var virtualStockShops []string
	for _, shop := range shops {
		shopMap[shop.ShopId] = shop
		if shop.IsVirtualStock == 1 {
			virtualStockShops = append(virtualStockShops, shop.ShopId)
		}
	}
	var updateProducts []service2.ProductForUpdate
	for _, product := range allProducts {

		_, ok := allJstProductsMap[int(product.SourceGoodsID)]
		if !ok {
			product.IsDisplay = 0
			for key, _ := range product.Skus {
				product.Skus[key].Stock = 0
			}
			updateProducts = append(updateProducts, product)
			continue
		}

		detail := allJstProductsMap[int(product.SourceGoodsID)]
		//var status int
		//if detail.ItemStatus == "cantDistribution" {
		//	status = 1
		//} else {
		//	status = 0
		//}
		if detail.DeleteStatus != 0 {
			product.IsDisplay = 0
			for key, _ := range product.Skus {
				product.Skus[key].Stock = 0
			}
			updateProducts = append(updateProducts, product)
			continue
		}
		product.SourceGoodsID = detail.ID
		product.SupplySupplierId = detail.SupplierNo
		product.DetailImages = detail.Description
		if _, jok := shopMap[product.SupplySupplierId]; jok {
			product.ShopName = shopMap[product.SupplySupplierId].Name
		}
		//for _, idi := range detail.ItemPhoto.ItemDetailImages {
		//	product.DetailImages += "<img src=\"" + idi + "\">"
		//}
		//product.DetailImages += "</p>"
		//
		//for _, mil := range detail.ItemPhoto.MainImageList {
		//	// 重复图片不添加
		//	var repeated bool
		//	for _, pGallery := range product.Gallery {
		//		if pGallery.Src == mil {
		//			repeated = true
		//			break
		//		}
		//	}
		//	if repeated {
		//		continue
		//	}
		//
		//	product.Gallery = append(product.Gallery, pmodel.GalleryItem{
		//		Type: 1,
		//		Src:  mil,
		//	})
		//}
		var pCostPrice, pPrice uint

		var skuList []service2.Sku
		var minProfitRate float64
		var totalStock int
		for sk, detailSku := range detail.GoodsSpecProducts {
			var sku = service2.Sku{}
			for _, oldSku := range product.Skus {
				if detailSku.PrdSn == oldSku.Sn {
					sku = oldSku
				}
			}
			attrs := strings.Split(detailSku.PrdDesc, ":")
			var options pmodel.Options
			if len(attrs) > 1 {
				attrsString := attrs[1]
				attrsB := strings.Split(attrsString, "；")
				if len(attrsB) > 1 {
					options = append(options, pmodel.Option{
						SpecName:     attrsB[0],
						SpecItemName: attrsB[1],
					})
				}
			}
			sku.Options = options
			//var intXSku uint64
			//if wdt.WdtData.Pricing.SupplyAdvice == 1 {
			//	intXSku, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceGuide, 10, 32)
			//	sku.OriginPrice = uint(detailSku.PrdPrice*100) * uint(intXSku) / 100
			//} else if wdt.WdtData.Pricing.SupplyAdvice == 2 {
			//	intXSku, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplyAdviceAgreement, 10, 32)
			//	sku.OriginPrice = uint(detailSku.PrdPrice*100) * uint(intXSku) / 100
			//} else {
			//	sku.OriginPrice = uint(detailSku.PrdPrice * 100)
			//}
			var intXS uint64
			if wdt.WdtData.Pricing.SupplySales == 1 {
				intXS, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesGuide, 10, 32)
				sku.Price = uint(detailSku.DistributionPrice*100) * uint(intXS) / 100
			} else if wdt.WdtData.Pricing.SupplySales == 2 {
				intXS, err = strconv.ParseUint(wdt.WdtData.Pricing.SupplySalesAgreement, 10, 32)
				sku.Price = uint(detailSku.DistributionPrice*100) * uint(intXS) / 100
			} else {
				sku.Price = uint(detailSku.DistributionPrice * 100)
			}
			sku.Title = detailSku.PrdDesc

			sku.Weight = int(detailSku.PrdWeight * 1000)
			if sku.Weight == 0 {
				sku.Weight = 300
			}
			sku.CostPrice = uint(detailSku.DistributionPrice * 100)
			sku.IsDisplay = 1

			sku.Stock = int(detailSku.PrdNumber)
			if collection.Collect(virtualStockShops).Contains(product.SupplySupplierId) {
				//水滴新供应链虚拟库存,都上架
				if sku.Stock == 0 {
					sku.Stock = 9999
				}

			}
			totalStock += sku.Stock
			//sku.GuidePrice = sku.OriginPrice
			//sku.ActivityPrice = sku.OriginPrice
			if _, jok := shopMap[product.SupplySupplierId]; jok {
				if shopMap[product.SupplySupplierId].IsOpen == 1 {
					//走单独定价策略
					err, sku.CostPrice, sku.Price, _, _, _ = GetShopPricingPrice(uint(detailSku.DistributionPrice*100), shopMap[product.SupplySupplierId])
					if err != nil {
						continue
					}
				}
			}
			sku.Sn = detailSku.PrdSn
			sku.Code = detailSku.SpecId
			sku.Barcode = ""
			if len(detailSku.ProductImages) > 0 {
				sku.ImageUrl = detailSku.ProductImages[0].ImgUrl
			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.OriginPrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			//err = source.Redis().LPush(context.Background(), "wdtSkuStockSync", sku.Sn).Err()
			skuList = append(skuList, sku)

		}
		product.Skus = skuList
		if totalStock > 0 {
			if product.StatusLock == 0 {
				product.IsDisplay = 1
			}
		}
		product.Price = pPrice
		product.CostPrice = pCostPrice
		//product.GuidePrice = pGuidePrice
		//product.OriginPrice = pOriginPrice
		//product.ActivityPrice = pActivityPrice
		//if status == 0 {
		//	product.IsDisplay = status
		//}
		//风控
		//if wdt.WdtData.Management.ProductPriceStatus == 1 {
		//	if product.Price < product.CostPrice*(wdt.WdtData.Management.Products/100) {
		//		product.IsDisplay = 0
		//	}
		//} else if wdt.WdtData.Management.ProductPriceStatus == 2 {
		//	if (product.Price-product.CostPrice)/product.CostPrice < wdt.WdtData.Management.Profit/100 {
		//		product.IsDisplay = 0
		//	}
		//}
		product.ProfitRate = minProfitRate
		//err = source.Redis().LPush(context.Background(), "wdtUploadProduct", product.SourceGoodsID).Err()
		updateProducts = append(updateProducts, product)

	}
	//updateGoods := szbao.ProductToGoods(needUpdateProducts, GatherSupplyID)
	//err, updateProducts = szbao.CommodityAssembly(updateGoods, 0, 0, 0, GatherSupplyID, 1)
	//if err != nil {
	//	return
	//}
	for _, updateProduct := range updateProducts {
		err = service2.UpdateProduct(updateProduct)
		if err != nil {
			log.Log().Info("wdt修改商品出错", zap.Any("data", updateProduct))
			continue
		}
	}
	return
}
