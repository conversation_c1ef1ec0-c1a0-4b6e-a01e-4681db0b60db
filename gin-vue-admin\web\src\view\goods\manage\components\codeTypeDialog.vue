<template>
    <el-dialog
        title="发票赋码"
        :visible="isShow"
        width="1000px"
        :before-close="handleClose"
    >
        <el-form label-width="130px">
            <el-row>
                <el-col :span="8" class="tree-box">
                    <div class="f fac">
                        <el-input size="small" v-model="treeInput"></el-input>
                        <el-button
                            size="small"
                            type="primary"
                            @click="searchTree"
                            >搜索</el-button
                        >
                    </div>
                    <el-tree
                        :filter-node-method="filterNode"
                        highlight-current
                        ref="tree"
                        class="mt10"
                        :data="treeData"
                        :props="defaultProps"
                        @node-click="handleNodeClick"
                    >
                    </el-tree>
                </el-col>
                <el-col :span="16" class="right-form-box">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="税收分类编码:">
                                <el-input v-model="tax_code"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="发票商品名称:">
                                <el-input v-model="tax_product_name"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="税收分类简称:">
                                <el-input v-model="tax_short_name"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="商品简码:">
                                <el-input v-model="short_code"></el-input>
                            </el-form-item>
                        </el-col>
                        <!--                                    <el-col :span="12">
                                                    <el-form-item label="规格型号:">
                                                        <el-input v-model="tax_option"></el-input>
                                                    </el-form-item>
                                                </el-col>-->
                        <!--                                    <el-col :span="12">
                                                    <el-form-item label="计量单位(元):">
                                                        <el-input-number v-model="goodsPrice" :controls="false" :min="0"
                                                                         :precision="2"
                                                                         class="w100 input-number-text-left"></el-input-number>
                                                    </el-form-item>
                                                </el-col>-->
                        <!--                                    <el-col :span="12">
                                                    <el-form-item label="单位:">
                                                        <el-input v-model="tax_unit"></el-input>
                                                    </el-form-item>
                                                </el-col>-->
                        <el-col :span="12">
                            <el-form-item label="使用优惠政策:">
                                <el-radio-group v-model="is_favorable_policy">
                                    <el-radio :label="1">是</el-radio>
                                    <el-radio :label="0">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <span slot="label"
                                    ><span
                                        class="color-red"
                                        v-if="is_favorable_policy === 1"
                                        >* </span
                                    >优惠政策类型:</span
                                >
                                <el-select
                                    v-model="favorable_policy"
                                    clearable
                                    class="w100"
                                >
                                    <el-option
                                        v-for="item in favorablePolicyOptios"
                                        :key="item.id"
                                        :label="item"
                                        :value="item"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <span slot="label"
                                    ><span class="color-red">*</span>税率:</span
                                >
                                <div class="f fac">
                                    <!--                                                tax_rate-->
                                    <el-select v-model="tax_rate">
                                        <el-option
                                            v-for="item in taxRateOptios"
                                            :key="item.id"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <span slot="label"
                                    ><span class="color-red">*</span
                                    >免税类型:</span
                                >
                                <el-select v-model="free_of_tax" class="w100">
                                    <el-option
                                        v-for="item in freeOfTaxOptions"
                                        :key="item.id"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="含税标志:">
                                <el-radio-group v-model="is_tax_logo">
                                    <el-radio :label="1">含税</el-radio>
                                    <el-radio :label="0">不含税</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <el-button
                                    size="small"
                                    type="success"
                                    :loading="aiCodingLoading"
                                    @click="handleAiCoding"
                                    class="w100"
                                    >AI赋码</el-button
                                >
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="save">保 存</el-button>
            <el-button @click="handleClose">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
/**
 * 获取数组对象指定k的下标
 */
Array.prototype.indexOfJSON = function (kName, value) {
    for (var i = 0; i < this.length; i++) {
        if (this[i][kName] == value) return i;
    }
    return -1;
};
import { getBillCategory, chatBill } from '@/api/bill';

export default {
    name: 'codeTypeDialog',
    data() {
        return {
            bill_position: null,
            type: '', // all=所有||下标
            isShow: false,
            skus: [],
            goodsTitle: '',
            /**
             * 发票部分开始
             */
            treeInput: '', // 搜索字段
            treeData: [], // 分类数据
            taxRateOptios: [
                // 税率optios
                { label: '0%', value: 0 },
                { label: '1%', value: 1 },
                { label: '3%', value: 3 },
                { label: '6%', value: 6 },
                { label: '9%', value: 9 },
                { label: '10%', value: 10 },
                { label: '11%', value: 11 },
                { label: '13%', value: 13 },
                { label: '17%', value: 17 },
            ],
            freeOfTaxOptions: [
                { label: '正常税率', value: 1 },
                { label: '出口免税和其他免税优惠政策', value: 2 },
                { label: '不征增值税', value: 3 },
                { label: '普通零税率', value: 4 },
            ],
            favorablePolicyOptios: [
                '免税',
                '100%先征后退',
                '50%先征后退不征税',
                '先征后退',
                '即征即退100%',
                '即征即退30%',
                '即征即退50%',
                '即征即退70%',
                '按3%简易征收',
                '按5%简易征收',
                '按5%简易征收减按1.5%计征',
                '稀土产品',
                '简易征收',
                '超税负12%即征即退',
                '超税负3%即征即退',
                '超税负8%即征即退',
            ],
            defaultProps: {
                label: 'mc',
                short_name: 'short_name',
                id: 'bm',
                children: 'children',
            },
            tax_code: '', //税收分类编码
            tax_short_name: '', //税收分类简称
            // tax_option: "", //规格型号
            tax_unit: '', //单位
            favorable_policy: '', //优惠政策
            is_favorable_policy: 0, //是否使用优惠政策
            free_of_tax: 1, //1正常税率2出口免税和其他免税优惠政策3不征增值税4普通零税率
            short_code: '', // 商品简码
            tax_measure_price: 0, //税收计量单价
            tax_rate: 0, //税率
            is_tax_logo: 1, //含税标志
            tax_product_name: '',
            /***** 发票部分结束 *****/
            is_single: false,
            aiCodingLoading: false, // AI赋码加载状态
        };
    },
    methods: {
        // 单条赋值
        setForm() {
            let that = this;
            let goods = that.skus[that.type];
            that.tax_product_name = goods.tax_product_name;
            that.tax_code = goods.tax_code;
            that.tax_short_name = goods.tax_short_name;
            // that.tax_option = goods.tax_option
            that.tax_unit = goods.tax_unit;
            that.favorable_policy = goods.favorable_policy;
            that.is_favorable_policy = goods.is_favorable_policy ?? 0;
            that.free_of_tax = goods.free_of_tax || 1;
            that.short_code = goods.short_code;
            that.tax_measure_price = that.$changeMoneyF2Y(
                goods.tax_measure_price,
            );

            that.taxRateOptios = [
                // 税率optios
                { label: '0%', value: 0 },
                { label: '1%', value: 1 },
                { label: '3%', value: 3 },
                { label: '6%', value: 6 },
                { label: '9%', value: 9 },
                { label: '10%', value: 10 },
                { label: '11%', value: 11 },
                { label: '13%', value: 13 },
                { label: '17%', value: 17 },
            ];
            if (
                that.taxRateOptios.indexOfJSON('value', goods.tax_rate) === -1
            ) {
                that.taxRateOptios.push({
                    label: `${goods.tax_rate}%`,
                    value: goods.tax_rate,
                });
            }
            that.tax_rate = goods.tax_rate ?? 0;
            that.is_tax_logo = goods.is_tax_logo ?? 1;
            that.is_single = goods.is_single ? true : false;
        },
        save() {
            let that = this;
            if (!that.checkVerify()) {
                return;
            }
            let params = {
                tax_code: that.tax_code,
                tax_short_name: that.tax_short_name,
                tax_unit: that.goodsUnit,
                favorable_policy: that.favorable_policy,
                is_favorable_policy: that.is_favorable_policy,
                free_of_tax: that.free_of_tax,
                short_code: that.short_code,
                tax_measure_price: that.$changeMoneyY2F(that.goodsPrice),
                tax_rate: that.tax_rate,
                is_tax_logo: that.is_tax_logo,
                tax_product_name: that.tax_product_name,
            };
            if (this.is_single) {
                params.is_single = this.is_single;
            }
            let skus = [];
            if (that.type === 'all') {
                skus = that.skus.map((item) => ({
                    ...item,
                    ...params,
                }));
                that.$message.success('批量赋码操作成功');
            } else if (Number.isInteger(that.type) >= 0) {
                skus = that.skus;
                skus[that.type] = { ...skus[that.type], ...params };
                that.$message.success(`[${skus[that.type].title}]赋码操作成功`);
            } else {
                that.$message.error('系统错误');
            }
            that.$emit('resSkus', skus, this.bill_position || null);
            that.handleClose();
        },
        checkVerify() {
            let that = this;
            if (that.tax_rate === '') {
                that.$message.error('请选择税率');
                return false;
            }
            if (that.is_favorable_policy === 1 && !that.favorable_policy) {
                that.$message.error('请选择优惠政策类型');
                return false;
            }
            if ([1, 2, 3, 4].indexOf(that.free_of_tax) === -1) {
                that.$message.error('请选择免税类型');
                return false;
            }
            return true;
        },
        // AI赋码
        async handleAiCoding() {
            let that = this;

            // 检查是否有商品数据
            if (!that.skus || that.skus.length === 0) {
                that.$message.error('请先选择商品');
                return;
            }

            // 构建商品信息
            let goodsInfo = '';
            if (that.type === 'all') {
                // 批量赋码时，使用第一个商品的信息作为示例
                const firstGoods = that.skus[0];
                goodsInfo = `商品名称：${
                    firstGoods.title || firstGoods.goods_name || ''
                }`;
                if (firstGoods.spec) {
                    goodsInfo += `\n规格：${firstGoods.spec}`;
                }
                if (firstGoods.unit) {
                    goodsInfo += `\n单位：${firstGoods.unit}`;
                }
                if (firstGoods.price) {
                    goodsInfo += `\n单价：${firstGoods.price} 元`;
                }
            } else if (Number.isInteger(that.type) && that.type >= 0) {
                // 单个商品赋码
                const goods = that.skus[that.type];
                goodsInfo = `商品名称：${
                    goods.title || goods.goods_name || ''
                }`;
                if (goods.spec) {
                    goodsInfo += `\n规格：${goods.spec}`;
                }
                if (goods.unit) {
                    goodsInfo += `\n单位：${goods.unit}`;
                }
                if (goods.price) {
                    goodsInfo += `\n单价：${goods.price} 元`;
                }
            } else {
                that.$message.error('请选择要赋码的商品');
                return;
            }

            if (!goodsInfo.trim()) {
                that.$message.error('商品信息不完整，无法进行AI赋码');
                return;
            }

            that.aiCodingLoading = true;

            /* try {
                const res = await chatBill({
                    message: goodsInfo,
                });

                if (res.code === 0 && res.data && res.data.data) {
                    const aiResult = res.data.data;

                    // 解析AI返回的数据并填充到表单
                    if (aiResult.tax_code) {
                        that.tax_code = aiResult.tax_code;
                    }
                    if (aiResult.tax_short_name) {
                        that.tax_short_name = aiResult.tax_short_name;
                    }
                    if (aiResult.tax_product_name) {
                        that.tax_product_name = aiResult.tax_product_name;
                    }
                    if (aiResult.short_code) {
                        that.short_code = aiResult.short_code;
                    }
                    if (aiResult.tax_rate !== undefined) {
                        // 检查税率是否在选项中，如果不在则添加
                        if (
                            that.taxRateOptios.indexOfJSON(
                                'value',
                                aiResult.tax_rate,
                            ) === -1
                        ) {
                            that.taxRateOptios.push({
                                label: `${aiResult.tax_rate}%`,
                                value: aiResult.tax_rate,
                            });
                        }
                        that.tax_rate = aiResult.tax_rate;
                    }
                    if (aiResult.free_of_tax !== undefined) {
                        that.free_of_tax = aiResult.free_of_tax;
                    }
                    if (aiResult.is_favorable_policy !== undefined) {
                        that.is_favorable_policy = aiResult.is_favorable_policy;
                    }
                    if (aiResult.favorable_policy) {
                        that.favorable_policy = aiResult.favorable_policy;
                    }
                    if (aiResult.is_tax_logo !== undefined) {
                        that.is_tax_logo = aiResult.is_tax_logo;
                    }

                    that.$message.success('AI赋码成功，请检查并确认信息');
                } else {
                    that.$message.error(res.msg || 'AI赋码失败，请重试');
                }
            } catch (error) {
                console.error('AI赋码错误:', error);
                that.$message.error('AI赋码失败，请检查网络连接或稍后重试');
            } finally {
                that.aiCodingLoading = false;
            } */
        },
        handleClose() {
            this.isShow = false;
            this.type = '';
            this.goodsTitle = '';
            this.is_single = false;
            this.treeInput = ''; // 搜索字段
            this.treeData = []; // 分类数据
            this.taxRateOptios = [
                // 税率optios
                { label: '0%', value: 0 },
                { label: '1%', value: 1 },
                { label: '3%', value: 3 },
                { label: '6%', value: 6 },
                { label: '9%', value: 9 },
                { label: '10%', value: 10 },
                { label: '11%', value: 11 },
                { label: '13%', value: 13 },
                { label: '17%', value: 17 },
            ];
            this.freeOfTaxOptions = [
                { label: '正常税率', value: 1 },
                { label: '出口免税和其他免税优惠政策', value: 2 },
                { label: '不征增值税', value: 3 },
                { label: '普通零税率', value: 4 },
            ];
            this.favorablePolicyOptios = [
                '免税',
                '100%先征后退',
                '50%先征后退不征税',
                '先征后退',
                '即征即退100%',
                '即征即退30%',
                '即征即退50%',
                '即征即退70%',
                '按3%简易征收',
                '按5%简易征收',
                '按5%简易征收减按1.5%计征',
                '稀土产品',
                '简易征收',
                '超税负12%即征即退',
                '超税负3%即征即退',
                '超税负8%即征即退',
            ];
            this.defaultProps = {
                label: 'mc',
                short_name: 'short_name',
                id: 'bm',
                children: 'children',
            };
            this.tax_code = ''; //税收分类编码
            this.tax_short_name = ''; //税收分类简称
            this.tax_unit = ''; //单位
            this.favorable_policy = ''; //优惠政策
            this.is_favorable_policy = 0; //是否使用优惠政策
            this.free_of_tax = 1; //1正常税率2出口免税和其他免税优惠政策3不征增值税4普通零税率
            this.short_code = ''; // 商品简码
            this.tax_measure_price = 0; //税收计量单价
            this.tax_rate = 0; //税率
            this.is_tax_logo = 1; //含税标志
            this.aiCodingLoading = false; // 重置AI赋码加载状态
        },
        /* 发票部分开始 */
        // 过滤tree
        searchTree() {
            this.$refs.tree.filter(this.treeInput);
        },
        filterNode(value, data, node) {
            if (!value) {
                node.expanded = false;
                return true;
            }
            let val = value.toLowerCase();
            return this.chooseNode(val, data, node);
        },
        chooseNode(value, data, node) {
            if (data.mc.indexOf(value) !== -1) {
                return true;
            }
            const level = node.level;
            if (level === 1) {
                return false;
            }
            let parentData = node.parent;
            let index = 0;
            while (index < level - 1) {
                if (parentData.data.mc.indexOf(value) !== -1) {
                    return true;
                }
                parentData = parentData.parent;
                index++;
            }
            return false;
        },
        // 选中发票分类
        handleNodeClick(data) {
            if (!data.children) {
                let value = parseInt(data.zzssl.split('%')[0]);
                this.taxRateOptios = [
                    // 税率optios
                    { label: '0%', value: 0 },
                    { label: '1%', value: 1 },
                    { label: '3%', value: 3 },
                    { label: '6%', value: 6 },
                    { label: '9%', value: 9 },
                    { label: '10%', value: 10 },
                    { label: '11%', value: 11 },
                    { label: '13%', value: 13 },
                    { label: '17%', value: 17 },
                ];
                if (this.taxRateOptios.indexOfJSON('value', value) === -1) {
                    this.taxRateOptios.push({
                        label: data.zzssl,
                        value: value,
                    });
                }
                this.tax_rate = value;
                this.tax_code = data.bm;
                this.tax_short_name = data.spbmjc;
            }
        },
        // 获取发票分类
        async getBillClassify() {
            let res = await getBillCategory();
            if (res.code === 0) {
                this.treeData = res.data.list;
            }
        },
        /* 发票部分结束 */
    },
};
</script>

<style lang="scss" scoped>
.tree-box {
    height: 368px;
    overflow-y: auto;
}
</style>
