package v1

import (
	"ai-assistant/request"
	"ai-assistant/service"
	yzResponse "yz-go/response"

	"github.com/gin-gonic/gin"
)

func ChatSelectGoods(c *gin.Context) {
	var info request.ChatCompletionsRequest
	err := c.ShouldBindJSON(&info)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if info.Message == "" {
		yzResponse.FailWithMessage("输入为空", c)
		return
	}

	// 使用服务
	result, err := service.Chat(info.Message, "")
	if err != nil {
		// 处理错误
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 使用结果
	yzResponse.OkWithData(gin.H{"data": result}, c)
}

func ChatBill(c *gin.Context) {
	var info request.ChatCompletionsRequest
	err := c.ShouldBindJSON(&info)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if info.Message == "" {
		yzResponse.FailWithMessage("输入为空", c)
		return
	}

	// 使用服务
	result, err := service.Chat(info.Message, "bill")
	if err != nil {
		// 处理错误
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 使用结果
	yzResponse.OkWithData(gin.H{"data": result}, c)
}
