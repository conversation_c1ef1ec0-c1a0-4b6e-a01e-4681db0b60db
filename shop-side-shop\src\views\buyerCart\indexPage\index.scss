.cart-box {
  margin-bottom: 100px;

  .distribution-box {
    padding: 15px 10px;
  }

  .cart-table-head {
    padding: 15px 30px;

    ::v-deep .el-checkbox {
      .el-checkbox__label {
        padding-left: 25px;
      }
    }
  }

  .cart-table-con {
    padding: 15px 30px;

    .order-goods-list-box {
      height: 0;
      .order-goods-expired {
        width: 36px;
        height: 24px;
        margin-right: 20px;
        line-height: 24px;
        text-align: center;
        border-radius: 10px;
        background-color: #EFEFEF;
      }
      .order-goods-img {
        border: 1px solid #c0c0c0;
        width: 118px !important;
        height: 118px !important;
        margin-right: 15px;
      }

      .order-goods-text {
        p {
          line-height: 25px;
        }
      }
    }

    ::v-deep .order-checkbox.el-checkbox {
      .el-checkbox__input {
        top: -12px;
      }

      .el-checkbox__label {
        padding-left: 25px;

        .el-avatar {
          margin-right: 10px;
        }
      }
    }
  }
}

.mtb {
  margin-bottom: 15px !important;
  margin-top: 15px !important;
}

::v-deep .cart-radio-group.el-radio-group {
  .el-radio {
    margin-right: 30px;

    &.is-checked {
      .el-radio__input.is-checked {
        .el-radio__inner {
          border-color: $theme-color;
          background: $theme-color;
          // √样式
          &::after {
            content: "";
            width: 7px;
            height: 3px;
            border: 1px solid white;
            border-top: transparent;
            border-right: transparent;
            text-align: center;
            display: block;
            position: absolute;
            top: 3px;
            left: 2px;
            vertical-align: middle;
            transform: rotate(-45deg);
            border-radius: 0px;
            background: none;
          }
        }
      }

      .el-radio__label {
        color: #606266;
      }
    }
  }
}

.del-text-ben {
  margin: 0;
  padding: 0;
  border: 0;
  color: #333333;
}

.table-jc {
  height: 120px;
  line-height: 120px;
}

//底部结算
.footer-account-box {
  width: 100%;
  position: fixed;
  z-index: 999;
  bottom: 0;
  height: 80px;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.13);

  .left-box {
    font-size: 16px;
    margin-left: 31px;

    ::v-deep .el-checkbox {
      .el-checkbox__label {
        font-size: 16px;
      }
    }

    p.ppp {
      margin-left: 18px;
      margin-right: 18px;
      cursor: pointer;
    }

    p.red-p {
      color: #f42121;
    }
  }

  .right-box {
    span:nth-child(1) {
      font-size: 16px;
      color: #666666;
    }

    span:nth-child(2) {
      font-size: 24px;
      font-weight: bold;
      color: #f42121;
      margin-left: 13px;
      margin-right: 39px;
    }

    .el-button {
      width: 139px;
      height: 78px;
      background: #f42121;
      border-color: #f42121;
      color: white;
      font-size: 24px;
      border-radius: 0;
    }
  }
}
.border-red{
  border: 1px solid red;
}