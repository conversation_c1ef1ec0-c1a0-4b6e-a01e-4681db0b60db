package route

import (
	v2 "ai-assistant/api/f/v1"
	v1 "ai-assistant/api/v1"

	"github.com/gin-gonic/gin"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	AiRouter := Router.Group("ai")
	{
		AiRouter.POST("saveSetting", v1.UpdateAiSetting) //
		AiRouter.POST("getSetting", v1.FindAiSetting)    //
		AiRouter.POST("/chat/bill", v2.ChatBill)         //

	}

}

func InitUserPrivateRouter(Router *gin.RouterGroup) {
	AiRouter := Router.Group("ai")
	{
		AiRouter.POST("/chat/selectGoods", v2.ChatSelectGoods) //
		AiRouter.POST("getSetting", v1.FindAiSetting)          //

	}

}
