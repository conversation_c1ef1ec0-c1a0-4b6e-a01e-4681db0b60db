package common

// 供应链分类常量
const (
	SUPPLY_STBZ        uint = 1
	SUPPLY_SELF        uint = 2
	SUPPLY_SZBAO       uint = 3
	SUPPLY_YZH         uint = 4
	SUPPLY_CROSS       uint = 5
	SUPPLY_DWD         uint = 6
	SUPPLY_YZHNEW      uint = 7
	SUPPLY_LIANLIAN    uint = 8
	SUPPLY_CAKE        uint = 9
	SUPPLY_HEHE        uint = 10
	SUPPLY_CURRICULUM  uint = 11
	LEVEL_BIND_PRODUCT uint = 12
	SUPPLY_LIJING      uint = 13
	SUPPLY_JUSHUITAN   uint = 14
	SUPPLY_YOUXUAN     uint = 16
	FULU_EQUITY        uint = 98
	FULU_USER_EQUITY   uint = 110
	USER_EQUITY        uint = 111
	SUPPLY_ZYHX        uint = 115
	SUPPLY_ALJX        uint = 116
	SUPPLY_WPS         uint = 119
	LEASE              uint = 120 //租赁
	SUPPLY_TIANMA      uint = 121
	SUPPLY_YIYATONG    uint = 122
	SUPPLY_SHAMA       uint = 123
	DACHANGHANGERP     uint = 125 //大昌行erp
	LEIJUEEQUITY       uint = 126 //雷珏-权益商品  //雷珏的需求对接的权益商品
	MILLENNIUM         uint = 127 // 千禧券
	SUPPLY_YJF         uint = 128 // 易积分
	SUPPLY_JD_VOP      uint = 129 // 京东VOP
	SUPPLY_GAT         uint = 130
	SUPPLY_MAIGER      uint = 131 // 迈戈供应链
	SUPPLY_GD          uint = 132
	SELF_GD            uint = 133
	SELF_CAKE          uint = 134
	SUPPLY_KUNSHENG    uint = 135
	SUPPLY_WDT         uint = 136
)

// 商品来源常量 product.source
const (
	// 胜天半子-云仓
	STBZ_YC int = 1
	// 胜天半子-京东
	STBZ_JD             int  = 2
	STBZ_AL             int  = 6
	STBZ_TM             int  = 7
	STBZ_SN             int  = 8
	STBZ_HNYC           int  = 11
	STBZ_TMYC           int  = 12
	STBZ_HDYC           int  = 14
	STBZ_TB             int  = 15
	STBZ_KJYC           int  = 16
	STBZ_TMJX           int  = 17
	STBZ_CJZX           int  = 18
	STBZ_YCYX           int  = 19
	SZBAO_SOURCE        int  = 99
	YZH_SOURCE          int  = 100
	DWD_SOURCE          int  = 103
	YZH_NEW             int  = 108
	LIANLIAN_SOURCE     int  = 109 // 周边游
	CAKE_SOURCE         int  = 110 // 蛋糕叔叔
	Hehe_SOURCE         int  = 112
	FULU_SOURCE         int  = 113
	EQUITY_SOURCE       int  = 114
	ZYHX_SOURCE         int  = 115 //智优惠选
	ALJX_SOURCE         int  = 116 //阿里精选
	LIJING_SOURCE       int  = 117
	JUSHUITAN_SOURCE    int  = 118
	WEIPINSHANG_SOURCE  int  = 119
	YOUXUAN_SOURCE      int  = 120
	TIANMA_SOURCE       int  = 121
	SHAMA_SOURCE        int  = 123
	YIYATONG_SOURCE     int  = 122
	LEIJUEEQUITY_SOURCE int  = 126
	MILLENNIUM_SOURCE   int  = 127 // 千禧券
	YIJIFEN_SOURCE      int  = 128 // 千禧券
	JD_VOP_SOURCE       int  = 129
	GUANAITONG_SOURCE   int  = 130 // 千禧券
	MAIGER_SOURCE       int  = 131 // 迈戈供应链
	GDSOURCE            uint = 132
	SELFGDSOURCE        uint = 133
	CAKESOURCE          uint = 134
	KUNSHENG_SOURCE     int  = 135
	WDT_SOURCE          int  = 136
)
