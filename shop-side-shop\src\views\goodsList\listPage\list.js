import { mapGetters } from "vuex";

/**
 * 获取数组对象指定k的下标
 */
Array.prototype.indexOfJSON = function (kName, value) {
    for (var i = 0; i < this.length; i++) {
        if (this[i][kName] == value) return i;
    }
    return -1;
};
import Breadcrumb from "../components/breadcrumb";
import zhLang from 'element-ui/lib/locale/lang/zh-CN';
import SortButton from '@/components/sortButton/sortButton.vue'
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue'
import albumDialog from "../components/albumDialog.vue";
import selectDialog from "../components/selectDialog.vue"
import scrollPaginationMixin from '@/mixin/scrollPaginationMixin.js';
import skeletonGoods from '@/components/mGoods/skeletonGoods.vue';

export default {
    name: "newList",
    mixins: [scrollPaginationMixin],
    components: {
        Breadcrumb,
        SortButton,
        SortButtonGroup,
        albumDialog,
        selectDialog,
        skeletonGoods
    },
    directives: {
        "watch-height": {
            inserted: function (el, binding) {
                const h = parseInt(window.getComputedStyle(el).height);
                binding.value(h);
            },
            componentUpdated(el, binding) {
                const h = parseInt(window.getComputedStyle(el).height);
                binding.value(h);
            },
        },
    },
    computed: {
        ...mapGetters("home", ["getFramework"])
    },
    watch: {
        breadcrumbData(newVal) {
            this.updateSeo(newVal[newVal.length - 1].name, newVal[newVal.length - 1].name, true)
        }
    },
    data() {
        return {
            category1_id: parseInt(this.$route.query.category1_id) || null,
            category2_id: parseInt(this.$route.query.category2_id) || null,
            category3_id: parseInt(this.$route.query.category3_id) || null,
            classify_list: [],
            classify2_list: [],
            classify3_list: [],
            isUnfold: false, // 一级分类是否展开            
            isUnfold2: false,// 二级分类是否展开
            isUnfold3: false,// 三级分类是否展开
            isShowFirstExpand: true,
            isShowSecondExpand: true,
            isShowThirdExpand: true,
            breadcrumbData: [],// 面包屑
            goodsList: [],
            formData: {
                source: null, // 来源
                grossProfitRate: null, // 毛利率
                profitRate: null, // 利润率
                agreementPrice: null, // 协议价
                guidePrice: null, // 指导价
                marketingPrice: null,// 营销价
                discount: null, // 折扣
                self_is_import: null, // 是否已导入
                marketing: null, // 营销属性
                d1: null,
                d2: null,
                a1: null,
                a2: null,
                g1: null,
                g2: null,
                p1: null,
                p2: null,
            },
            sortForm: {
                value: 'default', //按"默认"排序
                sort: 'up', // 升序
            },

            sourceList: [], // 来源
            grossProfitRate: [ // 毛利率
                { label: '0-35%', value: 1 },
                { label: '35%-50%', value: 2 },
                { label: '50%-75%', value: 3 },
                { label: '75%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            profitRateOptios: [ // 利润率
                { label: '0-50%', value: 1 },
                { label: '50%-150%', value: 2 },
                { label: '150%-300%', value: 3 },
                { label: '300%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            agreementPriceOptions: [ // 协议价
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            // 指导价/营销价optios
            guide_marketing_PriceOptions: [
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
            ],
            // 折扣
            discountList: [
                { label: '0-3折', value: 1 },
                { label: '3-5折', value: 2 },
                { label: '5-8折', value: 3 },
                { label: '8折以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            my_center_total: null, // 选品库数量
            checked: false, // 全选商品的按钮
            sort: [
                // 排序数组
                { id: 1, name: '最新上架', value: 'created_at' },
                { id: 2, name: '协议价', value: 'agreement_price' },
                { id: 3, name: '指导价', value: 'guide_price' },
                { id: 4, name: '营销价', value: 'activity_price' },
                { id: 5, name: '利润率', value: 'market_rate' },
                { id: 6, name: '毛利率', value: 'gross_profit_rate' },
                { id: 7, name: '折扣', value: 'discount' },
            ],
            sortid: 0, // 排序id
            sortForm: {
                // 最新上架:'created_at', 协议价:agreement_price,
                // 指导价:guide_price, 营销价:activity_price,
                // 利润律:market_rate, 毛利率:gross_profit_rate
                // 排序:discount
                value: '',
                sort: '1', // 1为升序，2为降序
            },
            selection_list: [],
            centerDialogVisible: false, // 弹幕开关
            centerDialogVisibleID: 0, // 判断打开弹窗渲染的数据

            dialog: {
                visible: false,
                propRow: {},
            },
        }
    },
    mounted() {
        this.getMyGatherSupplyAndSource()
        this.init()
    },
    filters: {
        unfoldText: function (status) {
            return status ? '收起' : '展开'
        }
    },
    methods: {
        // 获取搜索数据
        getParams() {
            let params = {
                category_1_id: this.category1_id || null,
                category_2_id: null,
                category_3_id: null,
            }

            // 二级分类
            if (this.category1_id && this.category2_id) {
                params.category_2_id = this.category2_id
                if (this.category3_id) {
                    params.category_3_id = this.category3_id
                    this.classify2_list.map(item => {
                        if (item.id === this.category3_id) {
                            params.category_3_id = null
                        }
                    })
                }
            }
            // 来源
            if (this.formData.source) {
                let sourceObj = this.formData.source.split('-')
                // console.log(str1);
                if (sourceObj.length === 3) {
                    let str = this.sourceList.find(
                        (item) => item.source_name == sourceObj[2]
                    );
                    if (str.type) {
                        params.gather_supply_id = str.gather_supply_id;
                    } else {
                        params.source = str.source_id;
                    }
                }

            }
            // 毛利率 gross_profit_rate
            if (this.formData.grossProfitRate) {
                if (this.formData.grossProfitRate == 5) {
                    params.gross_profit_rate = {
                        from: Number(this.formData.g1),
                        to: Number(this.formData.g2),
                    };
                } else {
                    const rate = [
                        { from: 0, to: 35 },
                        { from: 35, to: 50 },
                        { from: 50, to: 75 },
                        { from: 75, to: 9999 },
                    ];
                    params.gross_profit_rate = rate[this.formData.grossProfitRate - 1];
                }
            }

            // 利润率 profit_rate
            if (this.formData.profitRate) {
                if (this.formData.profitRate == 5) {
                    params.profit_rate = {
                        from: Number(this.formData.p1),
                        to: Number(this.formData.p2),
                    };
                } else {
                    const rate = [
                        { from: 0, to: 50 },
                        { from: 50, to: 150 },
                        { from: 150, to: 300 },
                        { from: 300, to: 9999 },
                    ];
                    params.profit_rate = rate[this.formData.profitRate - 1];
                }
            }

            // 协议价 agreement_price
            if (this.formData.agreementPrice) {
                if (this.formData.agreementPrice == 5) {
                    params.agreement_price = {
                        from: Number(this.formData.a1),
                        to: Number(this.formData.a2),
                    };
                } else {
                    const section = [
                        { from: 0, to: 200 },
                        { from: 200, to: 500 },
                        { from: 500, to: 1000 },
                        { from: 1000, to: 99999 },
                    ];
                    params.agreement_price =
                        section[this.formData.agreementPrice - 1];
                }
            }

            // 指导价 guide_price
            if (this.formData.guidePrice) {
                const rate = [
                    { from: 0, to: 200 },
                    { from: 200, to: 500 },
                    { from: 500, to: 1000 },
                    { from: 1000, to: 99999 },
                ];
                params.guide_price = rate[this.formData.guidePrice - 1];
            }

            // 折扣 discount
            if (this.formData.discount) {
                if (this.formData.discount == 5) {
                    params.discount = {
                        from: Number(this.formData.d1),
                        to: Number(this.formData.d2)
                    }
                } else {
                    const rate = [
                        { from: 0, to: 3 },
                        { from: 3, to: 5 },
                        { from: 5, to: 8 },
                        { from: 8, to: 10 },
                    ]
                    params.discount = rate[this.formData.discount - 1]
                }

            }

            // 营销价 activity_price
            if (this.formData.marketingPrice) {
                const rate = [
                    { from: 0, to: 200 },
                    { from: 200, to: 500 },
                    { from: 500, to: 1000 },
                    { from: 1000, to: 99999 },
                ];
                params.activity_price = rate[this.formData.marketingPrice - 1];
            }

            // 是否已导入 is_import
            if (this.formData.self_is_import) {
                params.is_import = parseInt(this.formData.self_is_import);
            }

            // 营销属性
            if (this.formData.marketing) {
                params[this.formData.marketing] = 1;
            }

            return params
        },
        // 加入商品专辑
        addAlbum() {
            this.dialog.visible = true;
            this.dialog.propRow = this.getParams()
        },
        // 加入选品
        addOption(res) {
            let a = false;
            let val = this.selection_list.find((item) => item == res);
            if (!val) {
                this.selection_list.push(res);
            }

            let goods = this.goodsList.filter((item) => item.is_import != 1);
            // 判断 是否 是全选
            for (let index = 0; index < goods.length; index++) {
                if (
                    undefined ==
                    this.selection_list.find((item) => item == goods[index].id)
                ) {
                    a = true;
                    break;
                }
            }
            if (!a) {
                this.checked = true;
            }
        },
        // 移除选品
        delOption(res) {
            let val = this.selection_list.find((item) => item == res);
            this.checked = false;
            if (val) {
                this.selection_list = this.selection_list.filter(
                    (item) => item != res,
                );
            }
        },
        // 关闭导入对话框
        nolead() {
            this.centerDialogVisible = false;
        },
        // 开启导入对话框
        tolead(res) {
            this.centerDialogVisibleID = res;
            this.centerDialogVisible = true;
        },
        acktrue() {
            let params = {}
            if (this.centerDialogVisibleID) {
                params = this.getParams()

                // 排序
                params.type = this.sortForm.value;
                switch (this.sortForm.sort) {
                    case '1':
                        params.sort = true;
                        break;
                    case '2':
                        params.sort = false;
                        break;
                }

                this.$post('/product/importGoods', params)
                    .then((res) => {
                        if (res.code == 0) {
                            this.$message.success(res.msg);
                            this.resetfun(); // 重置数据
                            // 重置待移除选品库的数据
                            this.selection_list = [];
                            this.centerDialogVisible = false; // 关闭弹窗
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                    .catch((res) => {
                        console.log(res);
                    });
            } else {
                if (this.selection_list.length == 0) {
                    this.$message('至少选中一个商品');
                    return;
                }
                params = {
                    ids: this.selection_list,
                };
                this.$post('/product/addStorageCenter', params)
                    .then((res) => {
                        if (res.code == 0) {
                            this.$message.success(res.msg);
                            this.resetfun(); // 重置数据
                            this.selection_list = []; // 清空导入商品的数组
                            this.centerDialogVisible = false; // 关闭弹窗
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                    .catch((res) => {
                        console.log(res);
                    });
            }
        },
        // 来源
        getMyGatherSupplyAndSource() {
            let sourceList = [
                {
                    gather_supply_id: -1,
                    source_id: -1,
                    source_name: '全部',
                    type: 1,
                },
                {
                    gather_supply_id: 0,
                    source_id: -1,
                    source_name: '平台自营',
                    type: 1,
                },
            ];
            this.$get('/application/getMyGatherSupplyAndSource')
                .then((res) => {
                    this.sourceList = sourceList.concat(res.data);
                })
                .catch((res) => {
                    console.log(res);
                });
        },
        // 搜索
        goSearch() {
            this.page = 1;
            this.total = 0;
            this.search()
        },
        loadNextPage() {
            this.page++;
            this.search(true)
        },
        async search(addList = false) {
            this.isLoading = true
            this.checked = false; // 改变全选状态
            if (addList) {
                this.disableScroll();
            }
            let params = this.getParams()
            params.page = this.page;
            params.pageSize = this.pageSize;
            // 排序
            params.type = this.sortForm.value;
            switch (this.sortForm.sort) {
                case '1':
                    params.sort = true;
                    break;
                case '2':
                    params.sort = false;
                    break;
            }
            params.is_display = 1;
            let res = {}
            if (this.$fn.isLogin()) {
                res = await this.$post("product/listLoginNew", params)
            } else {
                res = await this.$post("product/listNew", params)
            }
            this.isLoading = false;
            if (res.code === 0) {
                // this.goodsList = res.data.list?res.data.list:[];

                this.total = res.data.total;
                this.goodsList = addList === true ? this.goodsList.concat(res.data.list) : res.data.list ?? [];
                this.hasMore = res.data.total > this.goodsList.length;


                this.my_center_total = res.data.my_center_total;

                // 全选是否开启
                if (this.selection_list.length > 0) {
                    let k = false
                    this.goodsList.forEach(element => {
                        let index = this.selection_list.find(item => item === element.id)
                        if (index === undefined) {
                            k = true;
                        }
                    });
                    if (!k) {
                        this.checked = true
                    }
                }

            }
            if (addList) {
                this.enableScroll();
            }
        },
        resetfun() {
            // 重置面包屑
            this.breadcrumbData = [this.breadcrumbData[0], this.breadcrumbData[1]]
            this.page = 1;
            this.pageSize = 20;
            this.total = 0;
            // 重置排序
            this.sortForm = {
                value: '',
                sort: '1',
            };
            this.sortid = 0;

            this.category2_id = null;
            this.category3_id = null;
            this.classify3_list = []
            this.formData = {
                source: null, // 来源
                grossProfitRate: null, // 毛利率
                profitRate: null, // 利润率
                agreementPrice: null, // 协议价
                guidePrice: null, // 指导价
                marketingPrice: null,// 营销价
                discount: null, // 折扣
                self_is_import: null, // 是否已导入
                marketing: null, // 营销属性
                d1: null,
                d2: null,
                a1: null,
                a2: null,
                g1: null,
                g2: null,
                p1: null,
                p2: null,
            }

            this.search()
        },
        toMySel() {
            this.$router.push('/personalCenter/mySelectionList');
        },
        // 选品AI
        selectAi() {
            this.$refs.selectDialog.visible = true
            this.$refs.selectDialog.getSetting()
        },
        // 跳转商品详情
        goodsDetail(res) {
            this.$_blank(`/goodsDetail?goods_id=${res}`);
        },
        // 全选
        checkedfun() {
            let goodsList = this.goodsList.filter((item) => item.is_import != 1);
            let val;
            if (this.checked) {
                for (let index = 0; index < goodsList.length; index++) {
                    val = this.selection_list.find(
                        (item) => item == goodsList[index].id,
                    );
                    if (!val) {
                        this.selection_list.push(goodsList[index].id);
                    }
                }
            } else {
                for (let index = 0; index < goodsList.length; index++) {
                    val = this.selection_list.find(
                        (item) => item == goodsList[index].id,
                    );
                    if (val) {
                        this.selection_list = this.selection_list.filter(
                            (item) => item != goodsList[index].id,
                        );
                    }
                }
            }
        },
        // 最新上架
        newest() {
            this.page = 1
            this.pageSize = 20;
            this.total = 0;
            (this.sortForm = {
                value: 'created_at',
                sort: '2',
            }),
                (this.sortid = 1);
            this.search()
        },
        // 切换排序
        onChangeSort(res) {
            this.page = 1
            this.pageSize = 20;
            this.total = 0;
            let val = this.sort.find((item) => item.value == res.value);
            this.sortid = val.id;
            this.search()
        },

        checkFirstExpand(height) {
            this.isShowFirstExpand = height > 960;
        },
        checkSecondExpand(height) {
            this.isShowSecondExpand = height > 90;
        },
        checkThirdExpand(height) {
            this.isShowThirdExpand = height > 90;
        },
        // 点击二级分类的“全部”
        handleAllClassiyfClick() {
            let query = {
                category1_id: this.category1_id,
                category2_id: null,
                category3_id: null,
            }
            this.$router.replace({
                query
            })
            this.jointBreadcrumb()
            this.search()
        },
        /* pagination(val) {
            this.page = val.page
            this.search()
        }, */
        // 分类重新后取赋值, 解决面包屑二三级分类值不变问题
        initCategory() {
            this.category1_id = parseInt(this.$route.query.category1_id) || null
            this.category2_id = parseInt(this.$route.query.category2_id) || null
            // 二级全部
            if (this.$route.query.category2_id && !this.$route.query.category3_id) {
                this.category3_id = this.category2_id
            } else {
                this.category3_id = parseInt(this.$route.query.category3_id) || null
            }
        },
        // 拼接面包屑
        jointBreadcrumb() {
            this.initCategory();
            this.breadcrumbData = []
            let arr = [
                { name: "首页" }
            ]
            if (this.category1_id && this.$route.query.category1_id) {
                let obj = this.classify_list[this.classify_list.indexOfJSON('id', this.category1_id)]
                arr.push({
                    name: obj.name
                })
                if (this.category2_id && this.$route.query.category2_id) {
                    if (obj.children && obj.children.length > 0) {
                        let c2 = {}
                        c2 = obj.children.find((v) => {
                            return v.id === this.category2_id
                        })
                        arr.push({
                            name: c2.name
                        })
                        if (this.category3_id && this.$route.query.category3_id) {
                            if (c2.children && c2.children.length > 0) {
                                let c3 = {}
                                c3 = c2.children.find((v) => {
                                    return v.id === this.category3_id
                                })
                                arr.push({
                                    name: c3.name
                                })
                            }
                        }
                    }
                }
            } else {
                arr.push({
                    name: "全部分类"
                })
            }
            this.breadcrumbData = arr;
        },
        // 一级分类切换
        handleClassify1Change(val) {
            this.classify3_list = []
            this.goodsList = []
            let query = {
                category1_id: val,
                category2_id: null,
                category3_id: null,
            }
            this.$router.replace({
                query
            })
            this.getClassify2(val)
            this.jointBreadcrumb()
            this.search()
        },
        // 二级分类切换
        handleClassify2Change(cid) {
            this.classify3_list = []
            const clssify2_Obj = this.classify2_list.find(item => item.id === cid)
            this.classify3_list = clssify2_Obj.children
            let query = {
                category1_id: this.category1_id,
                category2_id: this.category2_id,
                category3_id: null,
            }
            this.$router.replace({
                query
            })
            this.jointBreadcrumb()
            this.search()
            this.getClassify2(parseInt(this.category1_id))
        },
        // 三级分类切换
        handleClassify3Change(cid) {
            let query = {
                category1_id: this.category1_id,
                category2_id: this.category2_id,
                category3_id: cid,
            }
            this.$router.replace({
                query
            })
            this.jointBreadcrumb()
            this.search()
        },
        // 获取二级分类
        getClassify2(val) {
            if (val) {
                this.classify2_list = this.classify_list[this.classify_list.indexOfJSON('id', val)].children
                // 二级分类增加全部选项
                if (!this.classify2_list.find(item => item.name === "全部")) {
                    this.classify2_list.unshift({
                        id: val,
                        name: "全部"
                    })
                }
                // 三级分类增加全部选项
                let c3Obj = this.classify2_list[this.classify2_list.indexOfJSON("id", parseInt(this.category2_id))]
                this.classify3_list = c3Obj && c3Obj.children ? c3Obj.children : [{ id: this.category2_id, name: "全部" }]
                if (!this.classify3_list.find(item => item.name === "全部")) {
                    this.classify3_list.unshift({
                        id: this.category2_id,
                        name: "全部"
                    })
                }
                if (!this.$route.query.category3_id) {
                    this.category3_id = parseInt(this.$route.query.category2_id)
                } else {
                    this.category3_id = parseInt(this.$route.query.category3_id)
                }
            }
        },
        // 初始化
        init() {
            // 获取全部分类
            this.$get("category/tree").then(res => {
                if (res.code === 0) {
                    this.classify_list = res.data.categories
                    this.jointBreadcrumb()
                    this.getClassify2(this.category1_id)
                    this.search()
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        srotChange() {
            this.search()
        },
        // 获取商品
        async fetch(page = this.page) {
            this.isLoading = true;
            /**
             * 排序查询：
             * formatObj键 => 1:向上up; 2:向下down。
             * formatObj值 => 1:默认降; 6:默认升; 4:销量降; 7:销量升; 2:价格降; 3:价格升; 9:利润率降; 10:利润率升; 5:发布时间降; 8:发布时间升。
             */
            const formatObj = {
                default: {
                    1: 8,
                    2: 5,
                },
                hot: {
                    1: 4,
                    2: 7,
                },
                price: {
                    1: 3,
                    2: 2,
                },
                profit: {
                    1: 10,
                    2: 9,
                },
            }
            let params = {
                category1_id: this.category1_id || null,
                category2_id: null,
                category3_id: null,
                sort_by: formatObj[this.sortForm.value][this.sortForm.sort], //排序
                origin_rate: { // 利润率
                    from: Number(this.formData.min_rate) || null,
                    to: Number(this.formData.max_rate) || null
                },
                min_price: this.$fn.changeMoneyY2F(Number(this.formData.min_price)) || null, //最低价格
                max_price: this.$fn.changeMoneyY2F(Number(this.formData.max_price)) || null, //最高价格
                is_new: this.formData.is_new, //新品
                is_hot: this.formData.is_hot || null, //热卖
                is_promotion: this.formData.is_promotion, //促销
                page,
                pageSize: this.pageSize,
            }
            // 处理二级分类id
            if (this.category1_id && this.category2_id) {
                params.category2_id = this.category2_id
                if (this.category3_id) {
                    params.category3_id = this.category3_id
                    this.classify2_list.map(item => {
                        if (item.id === this.category3_id) {
                            params.category3_id = null
                        }
                    })
                }
            }
            let res = {}
            if (this.$fn.isLogin()) {
                res = await this.$post("product/listLogin", params)
            } else {
                res = await this.$post("product/list", params)
            }
            this.isLoading = false;
            if (res.code === 0) {
                this.page = res.data.page
                this.total = res.data.total
                this.goodsList = res.data.list
                zhLang.el.pagination.total = `共 {total} ${this.total / this.pageSize === 100 ? '+' : ''} 条`
            } else {
                this.total = 0
                this.goodsList = []
            }
        },
    },
}