<template>
    <div class="system">
        <p class="title-p">系统更新</p>
        <el-divider></el-divider>
        <div class="f fw">
            <systemCard class="systemcard" title="当前应用信息">
                <div class="card-item">
                    <p>版本号: {{ updataInfo.version }}</p>
                    <p>发布时间: {{ Number(updataInfo.time) | formatDate }}</p>
                    <p>当前域名: {{ updataInfo.domain }}</p>
                    <p>更新日志</p>
                    <p>{{ updataInfo.versionTxt }}</p>
                </div>
            </systemCard>
            <systemCard class="systemcard" title="最新应用信息">
                <div class="card-item">
                    <p>版本号: {{ updataInfo.remoteVersion }}</p>
                    <p>更新日志</p>
                    <p>{{ updataInfo.remoteVersionTxt }}</p>
                </div>
            </systemCard>
            <systemCard class="systemcard" title="H5应用信息">
                <div class="card-item">
                    <p>当前版本号: {{ updataInfo.h5CurrentVersion }}</p>
                    <p class="h5-version">
                        最新版本号: {{ updataInfo.h5remoteVersion }}
                    </p>
                </div>
            </systemCard>
            <systemCard class="systemcard" title="小商店H5应用">
                <div class="card-item">
                    <p>当前版本号: {{ updataInfo.smallShoph5CurrentVersion }}</p>
                    <p class="h5-version">
                        最新版本号: {{ updataInfo.smallShoph5remoteVersion }}
                    </p>
                </div>
            </systemCard>
        </div>
        <p class="title-p2">更新至指定版本</p>
        <el-form model="updataInfo" inline>
            <el-form-item label="">
                <el-input
                    placeholder="请输入"
                    v-model="version"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">应用版本号</span>
                </el-input>
                <p class="color-grap">更新版本必须大于当前版本</p>
            </el-form-item>
            <el-form-item label="">
                <el-input
                    placeholder="请输入"
                    v-model="h5Version"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">H5应用版本号</span>
                </el-input>
                <p class="color-grap">更新版本必须大于当前版本</p>
            </el-form-item>
            <el-form-item label="">
                <el-input
                    placeholder="请输入"
                    v-model="smallShopH5Version"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">小商店H5应用版本号</span>
                </el-input>
                <p class="color-grap">更新版本必须大于当前版本</p>
            </el-form-item>
            <br />
            <el-form-item class="search-term">
                <el-button
                    v-if="updataInfo.isUpdate == '1'"
                    @click="checkUpdate"
                    type="primary"
                    >下载更新
                </el-button>
                <el-button
                    v-if="updataInfo.h5isUpdate == '1'"
                    class="btn-b"
                    @click="checkH5Update"
                    >更新H5
                </el-button>
                <el-button
                    v-if="updataInfo.smallShoph5isUpdate == '1'"
                    class="btn-b"
                    @click="checkSmallShopH5Update"
                    >更新小商店H5
                </el-button>
                <el-button @click="restartFn" class="btn-b">重启服务</el-button>
            </el-form-item>
        </el-form>
        <el-divider class="mt_30 mb_21"></el-divider>
        <p class="title-p2">更新日志</p>
        <div class="search-box">
            <el-form
                :model="searchForm"
                class="search-term"
                label-width="80px"
                inline
            >
                <el-form-item>
                    <el-input
                        placeholder="请输入"
                        v-model="searchForm.version"
                        class="line-input"
                        clearable
                    >
                        <span slot="prepend">版本号</span>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        placeholder="请输入"
                        v-model="searchForm.content"
                        class="line-input"
                        clearable
                    >
                        <span slot="prepend">关键词</span>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <div class="line-input">
                        <div class="line-box">
                            <span>应用</span>
                        </div>
                        <el-select
                            class="w100"
                            filterable
                            clearable
                            v-model="searchForm.type"
                        >
                            <el-option label="总应用" value="pc"></el-option>
                            <el-option label="H5应用" value="h5"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="line-input-date line-input">
                        <div class="line-box"><span>更新时间</span></div>
                        <div class="f fac">
                            <el-date-picker
                                class="w100"
                                placeholder="开始日期"
                                type="datetime"
                                v-model="searchForm.time_start"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            >
                            </el-date-picker>
                            <p class="title-3">至</p>
                            <el-date-picker
                                class="w100"
                                placeholder="结束日期"
                                type="datetime"
                                v-model="searchForm.time_end"
                                value-format="yyyy-MM-dd HH:mm:ss"
                            >
                            </el-date-picker>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button type="text" @click="resetForm"
                        >重置搜索条件</el-button
                    >
                </el-form-item>
            </el-form>
            <div class="table-box" v-if="logsList.length > 0">
                <div v-for="item in logsList" :key="item.id" class="table-item">
                    <el-row :gutter="20">
                        <el-col :span="4" class="item-left">
                            <h1 class="version">V{{ item.version }}</h1>
                            <p class="time">{{ item.updated_at }}</p>
                            <p
                                class="typetext"
                                :class="item.type === 'h5' ? 'h5' : 'pc'"
                            >
                                {{ item.type | formatType }}
                            </p>
                        </el-col>
                        <el-col :span="20" class="item-right">
                            {{ item.content }}
                        </el-col>
                    </el-row>
                </div>
            </div>
            <div v-else class="text-center mt_40">暂无数据～</div>
            <el-pagination
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100]"
                :style="{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    marginRight: '20px',
                }"
                :total="total"
                @current-change="handleCurrentChange"
                background
                layout="total,sizes, prev, pager, next, jumper"
            ></el-pagination>
        </div>
    </div>
</template>

<script>
import {
    downLoadSmallShopH5UpdateSupply,
    downLoadH5UpdateSupply,
    getSystemConfig,
    downLoadUpdateSupply,
    killSupply,
} from '@/api/system';
import { confirm } from '@/decorators/decorators';
import systemCard from './components/systemCard.vue';
import axios from 'axios';
export default {
    name: 'systemVersion',
    components: {
        systemCard,
    },
    filters: {
        formatType(val) {
            let s = '';
            switch (val) {
                case 'h5':
                    s = 'H5应用';
                    break;
                case 'pc':
                    s = '总应用';
                    break;
                default:
                    s = '未知类型';
                    break;
            }
            return s;
        },
    },
    data() {
        return {
            searchForm: {},
            page: 1,
            pageSize: 10,
            total: 0,
            logsList: [],
            version: '',
            h5Version: '',
            smallShopH5Version:'',
            updataInfo: {
                isUpdate: '',
                version: '',
                versionTxt: '',
                h5CurrentVersion: '',
                h5remoteVersion: '',
                h5isUpdate: '',
                remoteVersion: '',
                remoteVersionTxt: '',
                smallShoph5CurrentVersion: '',
                smallShoph5isUpdate: '',
                smallShoph5remoteVersion: '',
                time: '',
                domain: '',
                proto: '',
            },
        };
    },
    mounted() {
        this.initForm();
        this.getLogsList();
    },
    methods: {
        resetForm() {
            this.searchForm = {};
            this.page = 1;
        },
        search() {
            this.page = 1;
            this.getLogsList();
        },
        async getLogsList() {
            let params = {
                page: this.page,
                pageNum: this.pageSize,
                ...this.searchForm,
            };
            let res = await axios.get(
                'https://auth-test.yunzmall.com/api/getUpdateLogs',
                {
                    params,
                },
            );
            if (res.status === 200 && res.data.code === 0) {
                let data = res.data.data;
                console.log(data);
                this.logsList = data.data;
                this.total = data.total;
            } else {
                let msg = res.data.msg || '获取日志失败';
                this.$message.error(msg);
            }
        },
        handleCurrentChange(v) {
            this.page = v;
            this.getLogsList();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.getLogsList();
        },
        // 重启服务
        restartFn() {
            //killSupply
            this.$confirm('是否重启服务?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    killSupply().then((res) => {
                        if (res.code === 0) {
                            this.$message.success(res.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        //赋值表单
        setFrom(val) {
            const keys = Object.keys(val);
            const that = this;
            keys.forEach((element) => {
                that.updataInfo[element] = val[element];
            });
            this.version = val.remoteVersion;
            this.h5Version = val.h5remoteVersion;
            this.smallShopH5Version = val.smallShoph5remoteVersion
        },
        async initForm() {
            const res = await getSystemConfig();
            if (res.code == 0) {
                res.updateInfo.time = res.updateInfo.time
                    ? parseInt(res.updateInfo.time)
                    : '';
                this.setFrom(res.updateInfo);
            }
        },
        checkUpdate() {
            this.$confirm('是否确认更新?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    this.downLoadUpdate();
                })
                .catch(() => {});
        },
        @confirm('提示', '是否确认更新h5?')
        async checkH5Update() {
            let params = {};
            if (this.h5Version) {
                if (this.updataInfo.h5CurrentVersion) {
                    const versionRes = this.compareVersions(
                        this.h5Version,
                        this.updataInfo.h5CurrentVersion,
                    );
                    if (versionRes < 1) {
                        this.$message.error('更新版本必须大于当前版本');
                        return;
                    }
                }
                params.h5_version = this.h5Version;
            }
            let res = await downLoadH5UpdateSupply(params);
            if (res.code == 0) {
                this.$message({
                    type: 'success',
                    message: '更新成功, 10秒后重启',
                });
            } else {
                this.$message({
                    type: 'error',
                    message: res.msg,
                });
            }
        },
        @confirm('提示', '是否确认更新小商店h5?')
        async checkSmallShopH5Update(){
            let params = {};
            if (this.smallShopH5Version) {
                if (this.updataInfo.smallShoph5CurrentVersion) {
                    const versionRes = this.compareVersions(
                        this.smallShopH5Version,
                        this.updataInfo.smallShoph5CurrentVersion,
                    );
                    console.log(versionRes,'?????')
                    if (versionRes < 1) {
                        this.$message.error('更新版本必须大于当前版本');
                        return;
                    }
                }
                params.h5_version = this.smallShopH5Version;
            }
            let res = await downLoadSmallShopH5UpdateSupply(params);
            if (res.code == 0) {
                this.$message({
                    type: 'success',
                    message: '更新成功, 10秒后重启',
                });
            } else {
                this.$message({
                    type: 'error',
                    message: res.msg,
                });
            }
        },
        async downLoadUpdate() {
            let params = {};
            if (this.version) {
                if (this.updataInfo.version) {
                    const versionRes = this.compareVersions(
                        this.version,
                        this.updataInfo.version,
                    );
                    if (versionRes < 1) {
                        this.$message.error('更新版本必须大于当前版本');
                        return;
                    }
                }
                params.version = this.version;
            }
            let res = await downLoadUpdateSupply(params);
            if (res.code == 0) {
                this.$message({
                    type: 'success',
                    message: '更新成功, 10秒后重启',
                });
            } else {
                this.$message({
                    type: 'error',
                    message: res.msg,
                });
            }
        },
        compareVersions(v1, v2) {
            v1 = v1.replace(/\n/g, '');
            v2 = v2.replace(/\n/g, '');
            var v1parts = v1.split('.');
            var v2parts = v2.split('.');
            for (var i = 0; i < v1parts.length; ++i) {
                if (v2parts.length == i) {
                    return 1;
                }
                // 将字符串转换为数字进行比较
                const num1 = Number(v1parts[i]);
                const num2 = Number(v2parts[i]);
                if (num1 === num2) {
                    continue;
                } else if (num1 > num2) {
                    return 1;
                } else {
                    return -1;
                }
            }

            if (v1parts.length != v2parts.length) {
                return -1;
            }

            return 0;
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/style/base.scss';
.table-box {
    .table-item {
        margin-block: 30px;
        .item-left {
            h1.version {
                font-size: 20px;
                font-weight: bold;
            }
            p.time {
                color: #909399;
                font-size: 12px;
                margin: 10px 0;
            }
            p.typetext {
                display: inline-block;
                font-size: 12px;
                padding: 4px 5px;
                border-radius: 6px;
                &.pc {
                    background-color: rgba($color: #155bd4, $alpha: 0.2);
                    color: #155bd4;
                }
                &.h5 {
                    background-color: rgba($color: #25b191, $alpha: 0.2);
                    color: #25b191;
                }
            }
        }
        .item-right {
            min-height: 95px;
            padding: 16px 12px;
            border-radius: 8px;
            border: 1px solid #dcdfe6;
        }
    }
}
p.title-p {
    font-size: 16px;
    font-weight: bold;
}
p.title-p2 {
    font-size: 16px;
    color: #060606;
    margin-block: 14px;
}
.el-divider {
    margin: 15px 0;
}
.systemcard {
    margin-right: 16px;
    margin-bottom: 16px;
    .card-item {
        padding: 0 12px;
        p {
            margin: 10px 0;
            &:first-child {
                font-size: 20px;
                color: #155bd4;
                font-weight: bold;
            }
            &.h5-version {
                font-size: 20px;
                font-weight: bold;
            }
        }
    }
}
.btn-b {
    border-color: #155bd4 !important;
    color: #155bd4 !important;
}
</style>
