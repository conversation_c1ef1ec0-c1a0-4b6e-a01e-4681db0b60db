package app_trade

import (
	om "order/model"
	orderPay "order/pay"
	"payment/model"
	service4 "payment/service"
	model2 "product/model"
	"strconv"
	"trade/pay"
	"yz-go/component/log"
	"yz-go/source"

	"go.uber.org/zap"
)

func AppOrderPay(orders []om.Order, appID uint) (messageCode int, errMessages []string, orderPrice []string) {
	var err error
	for k, v := range orders {
		if len(v.OrderItems) == 0 {
			var orderItems []om.OrderItem
			err = source.DB().Where("order_id = ?", v.ID).Find(&orderItems).Error
			if err != nil {
				return
			}
			orders[k].OrderItems = orderItems
			v.OrderItems = orderItems
		}
		if v.Status > 0 {
			log.Log().Error(v.ThirdOrderSN+"订单已支付，无需再次支付!", zap.Any("err", err))
			continue
		} else if v.Status == -1 {
			log.Log().Error(v.ThirdOrderSN+"订单已关闭，无法支付!", zap.Any("err", err))
			errMessages = append(errMessages, v.ThirdOrderSN+"订单已关闭，无法支付!")
			continue
		} else {
			// 获取支付信息
			var payInfo orderPay.PayInfo
			err, payInfo = orderPay.GetPayInfo(v.UserID, []uint{v.ID})
			if err != nil {
				log.Log().Error("订单号:"+v.ThirdOrderSN+"的支付信息获取失败!", zap.Any("err", err))
				errMessages = append(errMessages, "订单号:"+strconv.Itoa(int(v.OrderSN))+"的支付信息获取失败!")
				continue
			}
			var payTypeSort []model.ApplicationPaySort
			err, payTypeSort = service4.GetPaySort(appID)
			if err != nil {
				log.Log().Error("订单号:"+v.ThirdOrderSN+"的支付方式获取失败!", zap.Any("err", err))
				errMessages = append(errMessages, "订单号:"+strconv.Itoa(int(v.OrderSN))+"的支付方式获取失败!")
				continue
			}

			var product model2.Product
			if len(v.OrderItems) == 0 {
				log.Log().Error("订单:"+v.ThirdOrderSN+"的OrderItems空!", zap.Any("err", err))
				errMessages = append(errMessages, v.ThirdOrderSN+"订单数据出错，无法支付!")
				continue
			}
			source.DB().Where("id=?", v.OrderItems[0].ProductID).First(&product)
			if product.ID == 0 {
				errMessages = append(errMessages, v.ThirdOrderSN+"商品数据出错，无法支付!")
				continue
			}

			var sort []pay.SupplyAccount
			accountErr := source.DB().Where("gather_supply_id=?", product.GatherSupplyID).Where("enable=1").Order("sort_level asc").Find(&sort).Error
			if accountErr != nil {
				log.Log().Info("下单:支付方式组支付未查到支付设置", zap.Any("err", accountErr))
			}
			log.Log().Info("下单:支付方式组支付", zap.Any("info", sort), zap.Any("info", product.GatherSupplyID))

			//var orderDataRecord model3.OrderDataRecord
			//source.DB().Where("order_sn=?", v.ThirdOrderSN).First(&orderDataRecord)
			//if orderDataRecord.Type == "hbsk" { //商城端慧呗支付不走正常扣款
			//	log.Log().Info("orderDataRecord.Type == hbsk")
			//	err = pay.NoPaid(v, payInfo)
			//} else {
			if len(sort) > 0 {
				err = pay.SupplyPaidBySort(v, payInfo, product.GatherSupplyID)
			} else {
				//支付
				err = pay.PaidBySort(v, payInfo, payTypeSort)
			}
			//}

			if err != nil {
				log.Log().Error("订单号:"+v.ThirdOrderSN+"的支付扣款失败!", zap.Any("err", err))
				orderPrice = append(orderPrice, strconv.Itoa(int(v.Amount)))
				messageCode = 1
				errMessages = append(errMessages, "订单号:"+strconv.Itoa(int(v.OrderSN))+"的支付扣款失败!")
				continue
			}

		}
	}
	return
}
