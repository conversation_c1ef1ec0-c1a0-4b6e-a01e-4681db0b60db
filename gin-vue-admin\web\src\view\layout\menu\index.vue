<!-- 菜单 -->
<template>
    <div
        class="menu-main"
        :style="childrenMenu.length > 0 ? 'width:240px' : 'width:100px'"
    >
        <!-- 左侧一级导航 -->
        <nav class="shared-first-sidebar">
            <!-- logo部分 -->
            <div class="shared-team-logo">
                <a class="shared-team-logo-wrap">
                    <img alt class="logoimg" :src="$fn.getLogo()" />
                </a>
            </div>
            <!-- 导航部分 -->
            <ul class="shared-first-sidebar-nav">
                <li
                    v-for="menu in asyncRouters[0].children"
                    :key="menu.id"
                    v-if="!menu.hidden"
                    :class="pathName === menu.name ? 'active' : ''"
                >
                    <span
                        @click="handleMenuClick(menu)"
                        style="padding-left: 13px"
                    >
                        <i :class="'el-icon-' + menu.meta.icon"></i>
                        {{ menu.meta.title | formartTitle }}
                        <el-badge
                            :max="99"
                            v-if="menu.name === 'Finance' && caiwuCount !== 0"
                            :value="caiwuCount"
                            class="item"
                        >
                            <router-link
                                :to="{ name: menu.name }"
                                @click.stop="handleJumpLinkClick(menu)"
                                class="level2-a"
                            >
                            </router-link>
                        </el-badge>
                        <el-badge
                            :max="99"
                            v-if="
                                menu.name === 'orderIndex' &&
                                afterSalesCount !== 0
                            "
                            :value="afterSalesCount"
                            class="item"
                        >
                            <router-link
                                :to="{ name: menu.name }"
                                @click.stop="handleJumpLinkClick(menu)"
                                class="level2-a"
                            >
                            </router-link>
                        </el-badge>
                        <el-badge
                            :max="99"
                            v-if="
                                menu.name === 'application' && caigouCount !== 0
                            "
                            :value="caigouCount"
                            class="item2"
                            style="top: 3px; right: 4px"
                        >
                            <router-link
                                :to="{ name: menu.name }"
                                @click.stop="handleJumpLinkClick(menu)"
                                class="level2-a"
                            >
                            </router-link>
                        </el-badge>
                        <el-badge
                            :max="99"
                            v-if="
                                menu.name === 'supplier' && gongyingCount !== 0
                            "
                            :value="gongyingCount"
                            class="item"
                            style="top: 3px; right: 4px"
                        >
                            <router-link
                                :to="{ name: menu.name }"
                                @click.stop="handleJumpLinkClick(menu)"
                                class="level2-a"
                            >
                            </router-link>
                        </el-badge>
                        <el-badge
                            :max="99"
                            v-if="
                                menu.name === 'goodsIndex' &&
                                shangpinCount !== 0
                            "
                            :value="shangpinCount"
                            class="item"
                            style="top: 3px; right: 4px"
                        >
                            <router-link
                                :to="{ name: menu.name }"
                                @click.stop="handleJumpLinkClick(menu)"
                                class="level2-a"
                            >
                            </router-link>
                        </el-badge>
                        <el-badge
                            :max="99"
                            v-if="
                                menu.name === 'MemberIndex' && userCount !== 0
                            "
                            :value="userCount"
                            class="item"
                            style="top: 3px; right: 4px"
                        >
                            <router-link
                                :to="{ name: menu.name }"
                                @click.stop="handleJumpLinkClick(menu)"
                                class="level2-a"
                            >
                            </router-link>
                        </el-badge>
                    </span>
                    <!--          <el-badge :max="99" v-if="menu.meta.title === '财务'" :value="financeNum" class="itemNum" style="top:-50px;right:-69px">-->
                    <!--          </el-badge>-->
                    <!--            <el-badge :max="99" v-if="menu.name === 'orderIndex'&&  afterSalesCount !== 0 " :value="afterSalesCount" class="item">-->
                    <!--            <router-link :to="{name:menu.name}" @click.stop="handleJumpLinkClick(menu)" class="level2-a">-->
                    <!--            </router-link>-->
                    <!--          </el-badge>-->
                    <!--            <el-badge :max="99" v-if="menu.name === 'application'&&  caigouCount !== 0 " :value="caigouCount " class="item2" style="top: 3px;right: 4px;">-->
                    <!--            <router-link :to="{name:menu.name}" @click.stop="handleJumpLinkClick(menu)" class="level2-a">-->
                    <!--            </router-link>-->
                    <!--          </el-badge>-->
                    <!--            <el-badge :max="99" v-if="menu.name === 'supplier'&&  gongyingCount !== 0 " :value="gongyingCount " class="item" style="top: 3px;right: 4px;">-->
                    <!--            <router-link :to="{name:menu.name}" @click.stop="handleJumpLinkClick(menu)" class="level2-a">-->
                    <!--            </router-link>-->
                    <!--          </el-badge>-->
                    <!--            <el-badge  v-if="menu.name === 'goodsIndex'&&  shangpinCount !== 0 " :value="shangpinCount " class="item" style="top: 3px;right: 4px;">-->
                    <!--            <router-link :to="{name:menu.name}" @click.stop="handleJumpLinkClick(menu)" class="level2-a">-->
                    <!--            </router-link>-->
                    <!--          </el-badge>-->
                    <!--               <el-badge v-if="menu.name === 'MemberIndex'&&  userCount !== 0 " :value="userCount" class="item" style="top: 3px;right: 4px;">-->
                    <!--            <router-link :to="{name:menu.name}" @click.stop="handleJumpLinkClick(menu)" class="level2-a">-->
                    <!--            </router-link>-->
                    <!--          </el-badge>-->
                    <!--          </span>-->
                </li>
            </ul>

            <!-- 消息 -->
            <div class="shared-corner fac fjc">
                <div class="notice-center" @click="openSystemLog">
                    <!-- <div class="notice-center" @click="openMessage">
          <div class="notice-num-box" v-if="unreadMessageTotal > 0">{{ unreadMessageTotal }}</div>
        </div> -->
                    <div class="newMessage-box" v-show="isNewMessage"></div>
                </div>
                <!-- <el-badge :max="99" class="item">
                    <div class="notice-center" @click="openSystemLog"></div>
                </el-badge> -->
            </div>
        </nav>
        <el-drawer
            title="系统消息"
            :visible="systemLogIsShow"
            :append-to-body="true"
            @close="closeSystemLog"
        >
            <m-card>
                <div style="display: flex; justify-content: flex-end">
                    <el-button type="text" @click="updateAllRead"
                        >全部已读</el-button
                    >
                </div>
                <div class="systemLog-list-box">
                    <div
                        class="systemLog-list-item mb_15"
                        v-for="(item, index) in systemData.list"
                        :key="item.id"
                        @click="updateRead(item.id, index)"
                    >
                        <div class="title-box f fac fjsb">
                            <h1 class="title">{{ item.title }}</h1>
                            <p>{{ item.created_at | formatDate }}</p>
                        </div>
                        <div class="body f fjsb fac">
                            <p class="f1">
                                {{ item.body }}
                            </p>
                            <div
                                class="new-d"
                                v-show="item.notified === 0"
                            ></div>
                        </div>
                    </div>
                </div>
                <el-pagination
                    :current-page="systemData.page"
                    :page-size="systemData.pageSize"
                    :style="{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        marginRight: '20px',
                    }"
                    :total="systemData.total"
                    @current-change="handleCurrentChange"
                    background
                    layout="total, prev, pager, next"
                ></el-pagination>
            </m-card>
        </el-drawer>
        <!-- 右侧二级导航 -->
        <nav class="shared-second-sidebar" v-if="childrenMenu.length > 0">
            <!-- 菜单标题 -->
            <h2 class="second-sidebar-title">{{ sidebarTitle }}</h2>
            <ul class="shared-second-sidebar-nav">
                <li
                    v-for="item in childrenMenu"
                    :key="item.id"
                    class="second-nav-item"
                    :class="
                        sidebarName === item.name && item.children === null
                            ? 'active'
                            : ''
                    "
                    v-if="!item.hidden"
                >
                    <span
                        class="level2-a"
                        v-if="item.children && item.children.length > 0"
                        @click.stop="handleL3UpClick(item)"
                    >
                        <i
                            :class="
                                item.unfold
                                    ? 'el-icon-arrow-down'
                                    : 'el-icon-arrow-right'
                            "
                            class="down-up-i"
                            v-if="item.children"
                        ></i>
                        {{ item.meta.title }}
                    </span>
                    <el-badge
                        :max="99"
                        v-else-if="
                            item.name === 'afterSaleIndex' ||
                            (item.name === 'supplierAfterSalesList' &&
                                afterSalesCount !== 0)
                        "
                        :value="afterSalesCount"
                        class="shouhou"
                    >
                        <router-link
                            :to="{ name: item.name }"
                            @click.stop="handleJumpLinkClick(item)"
                            class="level2-a"
                        >
                            {{ item.meta.title }}
                        </router-link>
                    </el-badge>
                    <router-link
                        v-else-if="item.name === 'cloudSupplyManage'"
                        to="/layout/cloudIndexManage/cloudSupplyManage"
                        class="level2-a"
                    >
                        {{ item.meta.title }} </router-link
                    ><router-link
                        v-else
                        :to="{ name: item.name }"
                        @click.stop="handleJumpLinkClick(item)"
                        class="level2-a"
                    >
                        {{ item.meta.title }}
                    </router-link>
                    <el-badge
                        :max="99"
                        v-if="
                            item.name === 'financeWithdrawDepositIndex' &&
                            caiwuCount !== 0
                        "
                        :value="caiwuCount"
                        class="caiwu"
                    >
                        <router-link
                            :to="{ name: item.name }"
                            @click.stop="handleJumpLinkClick(item)"
                            class="level2-a"
                        >
                        </router-link>
                    </el-badge>
                    <el-badge
                        :max="99"
                        v-if="
                            item.name === 'applicationAudit' &&
                            caigouCount !== 0
                        "
                        :value="caigouCount"
                        style="display: block; top: -11px; right: 26px"
                    >
                        <router-link
                            :to="{ name: item.name }"
                            @click.stop="handleJumpLinkClick(item)"
                            class="level2-a"
                        >
                        </router-link>
                    </el-badge>
                    <el-badge
                        :max="99"
                        v-if="
                            item.name === 'goodsVerifyList' &&
                            shangpinCount !== 0
                        "
                        :value="shangpinCount"
                        style="display: block; top: -11px; right: 36px"
                    >
                        <router-link
                            :to="{ name: item.name }"
                            @click.stop="handleJumpLinkClick(item)"
                            class="level2-a"
                        >
                        </router-link>
                    </el-badge>
                    <el-badge
                        :max="99"
                        v-if="
                            item.name === 'supplierApply' && gongyingCount !== 0
                        "
                        :value="gongyingCount"
                        style="display: block; top: -11px; right: 30px"
                    >
                        <router-link
                            :to="{ name: item.name }"
                            @click.stop="handleJumpLinkClick(item)"
                            class="level2-a"
                        >
                        </router-link>
                    </el-badge>
                    <el-badge
                        :max="99"
                        v-if="item.name === 'allMember' && userCount !== 0"
                        :value="userCount"
                        style="display: block; top: -11px; right: 36px"
                    >
                        <router-link
                            :to="{ name: item.name }"
                            @click.stop="handleJumpLinkClick(item)"
                            class="level2-a"
                        >
                        </router-link>
                    </el-badge>
                    <!--          <router-link v-else :to="{name:item.name}" @click.stop="handleJumpLinkClick(item)" class="level2-a">{{ item.meta.title }}{{item.name}}</router-link>-->
                    <!--          <a
                        href="javascript:;"
                        class="level2-a"
                        v-else
                        @click.stop="handleJumpLinkClick(item)"
                    >
                      {{ item.meta.title }}</a
                    >-->
                    <el-collapse-transition>
                        <ul v-show="item.children && item.unfold">
                            <li
                                v-for="l3 in item.children"
                                :key="l3.id"
                                v-if="!l3.hidden"
                                class="second-nav-item"
                                :class="sidebarName === l3.name ? 'active' : ''"
                            >
                                <!--                <a
                                    href="javascript:;"
                                    class="l3-a"
                                    @click.stop="handleJumpLinkL3Click(item, l3)"
                                >{{ l3.meta.title }}</a
                                >-->
                                <router-link
                                    :to="{ name: l3.name }"
                                    @click.stop="
                                        handleJumpLinkL3Click(item, l3)
                                    "
                                    class="l3-a"
                                >
                                    {{ l3.meta.title }}
                                    <el-badge
                                        v-if="
                                            l3.name ===
                                            'financeWithdrawDepositRecord'
                                        "
                                        :value="caiwuCount"
                                        class="tixianjilu"
                                    >
                                        <router-link
                                            :to="{ name: l3.name }"
                                            @click.stop="
                                                handleJumpLinkClick(l3)
                                            "
                                            class="level2-a"
                                        >
                                        </router-link>
                                    </el-badge>
                                </router-link>
                            </li>
                        </ul>
                    </el-collapse-transition>
                </li>
            </ul>
        </nav>
        <!-- <message ref="message"></message> -->
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { logo_url } from '@/utils/page';
// import message from './components/message';
import {
    getAfterSalesCount,
    getWithdrawUnCompleteCount,
    getApplicationApplyCount,
    getSupplierApplyCount,
    getProductVerifyCount,
    getUserVerifyCount,
    getNotifyCount,
} from '@/api/afterSale';
import { supplierGetAfterSalesCount } from '@/api/supplier/afterSale';
import { getNotifyList, updateAsRead, updateAsAllRead } from '@/api/notify';

/**
 * 获取数组对象指定k的下标
 */
Array.prototype.indexOfJSON = function (kName, value) {
    for (var i = 0; i < this.length; i++) {
        if (this[i][kName] == value) return i;
    }
    return -1;
};

export default {
    name: 'MenuIndex',
    // components: { message },
    data() {
        return {
            isNewMessage: false,
            systemLogIsShow: false,
            afterSalesCount: 0,
            //财务
            caiwuCount: 0,
            //订单数量
            dingdanCount: 0,
            //采购端
            caigouCount: 0,
            gongyingCount: 0,
            shangpinCount: 0,
            userCount: 0,
            // 子导航
            childrenMenu: [],
            pathName: '',
            sidebarName: '',
            sidebarTitle: '',
            path: '',
            systemData: {
                list: [],
                page: 1,
                pageSize: 10,
                total: 0,
            },
            unreadMessageTotal: 0,
        };
    },
    computed: {
        ...mapGetters('router', ['asyncRouters']),
        ...mapGetters('user', ['userInfo']),
    },
    watch: {
        $route() {
            this.checkedMenu();
        },
    },
    mounted() {
        this.checkedMenu();
        this.getSystemLogList();
        // this.getWithdrawUnComplete();
        // this.getApplicationApply();
        // this.getSupplierApply();
        // this.getProductVerify();
        // this.getUserVerify();
        // this.getAfterSalesCount();
        this.getNotifyCount();
    },
    filters: {
        formartTitle: function (value) {
            if (value.indexOf('供应') >= 0) {
                return value.slice(0, 3);
            } else if (value.indexOf('采购端') >= 0) {
                return value;
            } else {
                return value.slice(0, 2);
            }
        },
    },
    methods: {
        handleCurrentChange(page) {
            this.systemData.page = page;
            this.getSystemLogList();
        },
        // 单条已读
        async updateRead(id, index) {
            const { code } = await updateAsRead({ id });
            if (code === 0) {
                this.$set(this.systemData.list[index], 'notified', 1);
            }
        },
        // 全部已读
        async updateAllRead() {
            const { code } = await updateAsAllRead();
            if (code === 0) {
                this.getSystemLogList();
            }
        },
        // 打开系统消息抽屉回调
        openSystemLog() {
            this.systemLogIsShow = true;
            this.getSystemLogList();
        },
        // 关闭系统消息抽屉回调
        closeSystemLog() {
            this.systemLogIsShow = false;
        },
        // 获取系统信息列表
        async getSystemLogList(
            page = this.systemData.page,
            pageSize = this.systemData.pageSize,
        ) {
            let params = {
                page,
                pageSize,
            };
            const { code, data } = await getNotifyList(params);
            if (code === 0) {
                this.systemData.list = data.list;
                // 判断是否有新的未读消息
                this.isNewMessage = this.systemData.list.some(
                    (item) => item.notified === 0,
                );
                this.systemData.total = data.total;
            }
        },
        //所有数量
        getNotifyCount() {
            getNotifyCount().then((res) => {
                if (res.code == '0') {
                    this.caiwuCount = res.data.data.financeCount;
                    this.caigouCount = res.data.data.applicationCount;
                    if(this.userInfo.is_supplier){
                        this.supplierGetAfterSalesCount();
                    } else{
                        this.afterSalesCount = res.data.data.orderCount;
                    }
                    this.gongyingCount = res.data.data.supplierCount;
                    this.shangpinCount = res.data.data.productCount;
                    this.userCount = res.data.data.userMap;
                }
            });
        },
        // 供应商数量
        async supplierGetAfterSalesCount() {
            let res = await supplierGetAfterSalesCount();
            if (res.code === 0) {
                this.afterSalesCount = res.data;
            }
        },
        getWithdrawUnComplete() {
            getWithdrawUnCompleteCount().then((res) => {
                if (res.code == '0') {
                    console.log(res);
                    this.caiwuCount = res.data;
                }
            });
        },
        getApplicationApply() {
            getApplicationApplyCount().then((res) => {
                if (res.code == '0') {
                    console.log(res);
                    this.caigouCount = res.data;
                }
            });
        },
        getAfterSalesCount() {
            getAfterSalesCount().then((res) => {
                if (res.code === 0) {
                    this.afterSalesCount = res.data;
                }
            });
        },
        getSupplierApply() {
            getSupplierApplyCount().then((res) => {
                if (res.code == '0') {
                    console.log(res);
                    this.gongyingCount = res.data;
                }
            });
        },
        getProductVerify() {
            getProductVerifyCount().then((res) => {
                if (res.code == '0') {
                    console.log(res);
                    this.shangpinCount = res.data;
                }
            });
        },
        getUserVerify() {
            getUserVerifyCount().then((res) => {
                if (res.code == '0') {
                    console.log(res);
                    this.userCount = res.data;
                }
            });
        },
        openMessage() {
            this.$refs.message.isShow = !this.$refs.message.isShow;
            this.$refs.message.handleNews();
        },
        // 处理选中导航
        checkedMenu() {
            // 选中的一级导航
            this.pathName =
                this.asyncRouters[0].children[
                    this.asyncRouters[0].children.indexOfJSON(
                        'name',
                        this.$route.matched[1].name,
                    )
                ].name;
            if (this.pathName === 'orderIndex') {
                getAfterSalesCount().then((res) => {
                    if (res.code === 0) {
                        this.afterSalesCount = res.data;
                    }
                });
            } else if (this.pathName === 'SupplierAdminOrderManage') {
                supplierGetAfterSalesCount().then((res) => {
                    if (res.code === 0) {
                        this.afterSalesCount = res.data;
                    }
                });
            }
            // 判断二级导航
            if (
                this.$route.matched.length === 3 ||
                this.$route.matched.length === 4
            ) {
                let menu =
                    this.asyncRouters[0].children[
                        this.asyncRouters[0].children.indexOfJSON(
                            'name',
                            this.$route.matched[1].name,
                        )
                    ];
                this.childrenMenu = menu.children.map((v) => ({
                    ...v,
                    unfold: false,
                }));
                if (this.$route.matched.length === 4) {
                    this.childrenMenu[
                        this.childrenMenu.indexOfJSON(
                            'name',
                            this.$route.matched[2].name,
                        )
                    ].unfold = true;
                }
                this.pathName = menu.name;
                this.sidebarName = this.$route.name;
                this.sidebarTitle = menu.meta.title;
                /* this.sidebarName =
          this.asyncRouters[0].children[
            this.asyncRouters[0].children.indexOfJSON(
              "name",
              this.$route.matched[1].name
            )
          ].children[
            (this.sidebarName = this.asyncRouters[0].children[
              this.asyncRouters[0].children.indexOfJSON(
                "name",
                this.$route.matched[1].name
              )
            ].children.indexOfJSON("name", this.$route.matched[2].name))
          ].name; */
            } else if (this.$route.matched.length === 2) {
                this.childrenMenu = [];
                this.sidebarTitle = '';
                this.$emit('totalCollapse', true);
            }
        },
        handleL3UpClick(item) {
            this.$nextTick(() => {
                for (let i = 0; i < this.childrenMenu.length; i++) {
                    this.childrenMenu[i].unfold = false;
                }
                this.childrenMenu[
                    this.childrenMenu.indexOfJSON('ID', item.ID)
                ].unfold = true;
            });
        },
        // 三级路由跳转
        handleJumpLinkL3Click(item, l3) {
            this.$router.push({ name: l3.name });
            this.sidebarName = l3.name;
        },
        handleJumpLinkClick(item) {
            if (item.name == 'supplierList') {
                this.getSupplierApply();
            }
            if (item.name == 'balance') {
                this.getWithdrawUnComplete();
            }
            if (item.name == 'goodsList') {
                this.getProductVerify();
            }
            if (item.name == 'applicationList') {
                this.getApplicationApply();
            }
            if (item.name == 'allOrderIndex') {
                this.getAfterSalesCount();
            }
            if (item.name == 'allMember') {
                this.getUserVerify();
            }
            this.sidebarName = item.name;
            // this.$router.push({name: item.name});
        },
        handleMenuClick(menu) {
            if (menu.children !== null) {
                this.path = '/layout/' + menu.path + '/';
                this.childrenMenu = menu.children.map((v) => ({
                    ...v,
                    unfold: false,
                }));
                this.pathName = menu.name;
                this.sidebarTitle = menu.meta.title;
                this.$emit('totalCollapse', false);
                for (let i = 0; i < this.childrenMenu.length; i++) {
                    if (!this.childrenMenu[i].hidden) {
                        this.handleJumpLinkClick(this.childrenMenu[i]);
                        if (menu.name === 'cloudIndexManage') {
                            this.$router.push({
                                path: '/layout/cloudIndexManage/cloudSupplyManage',
                            });
                        } else {
                            this.$router.push({
                                name: this.childrenMenu[i].name,
                            });
                        }
                        break;
                    }
                }
            } else {
                // 无二级导航
                this.$router.push('/layout/' + menu.path);
                this.pathName = menu.name;
                this.sidebarName = '';
                this.childrenMenu = [];
                this.sidebarTitle = '';
                this.$emit('totalCollapse', true);
            }
        },
    },
};
</script>
<style src="./indexPage/index.scss" lang="scss" scoped>
.tixianjilu {
    //marigin-right:30px;
}
</style>
