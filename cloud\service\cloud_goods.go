package service

import (
	"cloud/common"
	model2 "cloud/model"
	"cloud/request"
	"cloud/response"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/gogf/gf/frame/g"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"golang.org/x/net/context"
	"gorm.io/gorm"
	"math"
	"order/express"
	model4 "order/model"
	"order/order"
	orderPay "order/pay"
	request3 "order/request"
	"os"
	payment "payment/entrance"
	paymentModel "payment/model"
	"product/model"
	"product/mq"
	productService "product/service"
	model3 "region/model"
	express2 "shipping/express"
	shoppingCartModel "shopping-cart/model"
	shoppingCartService "shopping-cart/service"
	"strconv"
	"strings"
	"sync"
	"time"
	"trade/checkout"
	"trade/confirm"
	request2 "trade/request"
	"trade/service"
	"yz-go/cache"
	"yz-go/common_data"
	"yz-go/component/log"
	YzGoConfig "yz-go/config"
	service2 "yz-go/service"
	"yz-go/source"
	"yz-go/utils"
)

type ProductList struct {
	model.Product
	CreatedAt *source.LocalTime `json:"created_at" gorm:"index;"`
	Title     string            `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"` // 标题
	// 三级分类

	OriginPrice   uint `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价 市场价
	GuidePrice    uint `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:供货价(单位:分);"`          //指导价
	Price         uint `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                            // 供货价、给采购端的协议价
	CostPrice     uint `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价，上游给的协议价
	ActivityPrice uint `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
	MinBuyQty     uint `json:"min_buy_qty" form:"min_buy_qty" gorm:"column:min_buy_qty;default:0;comment:最小起订量;"`     // 最小起订量

	//如果有activity_price时，那么cost_price为activity_price，否则为price
	Stock        uint   `json:"stock" form:"stock" gorm:"column:stock;default:0;comment:库存数量;index"`            // 库存数量
	Sales        uint   `json:"sales" form:"sales" gorm:"column:sales;comment:销量;"`                               // 销量
	FeedbackRate int    `json:"feedback_rate" form:"feedback_rate" gorm:"column:feedback_rate;comment:好评率;"`     // 好评率
	Sn           string `json:"sn" form:"sn" gorm:"column:sn;comment:产品编号;type:text;"`                          // 产品编号
	Code         string `json:"code" form:"code" gorm:"column:code;comment:自定义编码;type:varchar(255);size:255;"` // 自定义编码

	IsNew        int `json:"is_new" form:"is_new" gorm:"column:is_new;comment:新品（1是0否）;type:smallint;size:1;index;"`                   // 新品（1是0否）
	IsRecommend  int `json:"is_recommend" form:"is_recommend" gorm:"column:is_recommend;comment:推荐（1是0否）;type:smallint;size:1;index;"` // 推荐（1是0否）
	IsHot        int `json:"is_hot" form:"is_hot" gorm:"column:is_hot;comment:热销（1是0否）;type:smallint;size:1;index;"`                   // 热销（1是0否）
	IsPromotion  int `json:"is_promotion" form:"is_promotion" gorm:"column:is_promotion;comment:促销（1是0否）;type:smallint;size:1;index;"` // 促销（1是0否）
	IsDisplay    int `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:smallint;size:1;index;"`       // 上架（1是0否）
	StatusLock   int `json:"status_lock" form:"status_lock" gorm:"column:status_lock;comment:锁定（1是0否）;type:smallint;size:1;index;"`    // 锁定（1是0否）
	SingleOption int `json:"single_option" form:"single_option" gorm:"column:single_option;comment:单规格(1是0否);type:smallint;size:1;"`  // 单规格（1是0否）

	Desc        string `json:"desc" form:"desc" gorm:"column:desc;comment:简介;type:varchar(200);size:200;"`            // 简介
	ImageUrl    string `json:"image_url" gorm:"column:image_url;comment:图片url;"`                                      // 图片url
	VideoUrl    string `json:"video_url" gorm:"column:video_url;comment:视频url;"`                                      // 视频url
	Unit        string `json:"unit" form:"unit" gorm:"column:unit;comment:单位(件,个);type:varchar(255);size:255;"`     // 单位(件,个)
	Barcode     string `json:"barcode" form:"barcode" gorm:"column:barcode;comment:条形码;type:varchar(255);size:255;"` // 条形码
	Long        int    `json:"long" form:"long" gorm:"column:long;type:int;comment:长(单位:毫米);"`                     //长(单位:毫米)
	Wide        int    `json:"wide" form:"wide" gorm:"column:wide;type:int;comment:宽(单位:毫米);"`                     // 宽(单位:毫米)
	High        int    `json:"high" form:"high" gorm:"column:high;type:int;comment:高(单位:毫米);"`                     // 高(单位:毫米)
	Volume      int    `json:"volume" form:"volume" gorm:"column:volume;type:int;comment:体积(单位:立方毫米);"`         // 高(单位:毫米)
	Freight     int    `json:"freight" form:"freight" gorm:"column:freight;type:int;comment:运费(分);"`                 // 高(单位:毫米)
	FreightType int    `json:"freight_type" form:"freight_type" gorm:"column:freight_type;type:smallint;comment:运费类型（0统一，1模板， 2第三方运费，3包邮）;"`
	MaxPrice    uint   `json:"maxPrice" form:"maxPrice" gorm:"column:max_price;comment:最高价(单位:分);"` // 最高价(单位:分)
	MinPrice    uint   `json:"minPrice" form:"minPrice" gorm:"column:min_price;comment:最低价(单位:分);"` // 最低价(单位:分)

	Gallery        model.Gallery        `json:"gallery" form:"gallery" gorm:"column:gallery;comment:相册图json数组;type:text;"`                    // 相册图json数组
	Qualifications model.Qualifications `json:"qualifications" form:"qualifications" gorm:"column:qualifications;comment:资质json数组;type:text;"` // 资质json数组

	Supplier     model.Supplier     `json:"supplier,omitempty" form:"supplier"` // 供应商
	GatherSupply model.GatherSupply `json:"gather_supply" form:"supplier"`      // 供应链

	BrandID           uint  `json:"brand_id" form:"brand_id" gorm:"index"`                                                           // 品牌
	Brand             Brand `json:"brand"`                                                                                           // 品牌
	SupplierID        uint  `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`                // 供应商id
	GatherSupplyID    uint  `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
	Category1ID       uint  `json:"category1_id" form:"category1_id" gorm:"index"`                                                   // 一级分类
	Category2ID       uint  `json:"category2_id" form:"category2_id" gorm:"index"`                                                   // 二级分类
	Category3ID       uint  `json:"category3_id" form:"category3_id" gorm:"index"`
	FreightTemplateID uint  `json:"freight_template_id" form:"freight_template_id" gorm:"column:freight_template_id;comment:配送模板id;"` // 配送模板id

	Source        int    `json:"source" form:"source" gorm:"column:source;comment:商品来源;type:int;"`                           //     1云仓 2京东 6阿里 7天猫  8苏宁 100YZH 99永源 98中台云仓 101中台 102跨境 103dwd
	Sort          int    `json:"sort" form:"sort" gorm:"column:sort;comment:商品排序;type:int;"`                                 //     商品排序（序号小的在前）
	SourceGoodsID uint   `json:"source_goods_id" form:"source_goods_id" gorm:"column:source_goods_id;index;comment:商品来源ID;"` // 商品来源
	Freeze        uint   `json:"freeze" form:"freeze" gorm:"column:freeze;comment:是否冻结;default:0;"`                          // 冻结
	ShopLevel     int    `json:"shop_level" form:"shop_level" gorm:"column:shop_level;default:0;type:int(2);"`
	DesLevel      int    `json:"des_level" form:"des_level" gorm:"column:des_level;default:0;type:int(2);"`
	ExpressLevel  int    `json:"express_level" form:"express_level" gorm:"column:express_level;default:0;type:int(2);"`
	Level         int    `json:"level" form:"level" gorm:"column:level;default:0;type:int(2);"`
	ChildTitle    string `json:"child_title" form:"child_title" gorm:"column:child_title;comment:简称;type:varchar(255);size:255;index;"` // 简称
	BillPosition  int    `json:"bill_position" form:"bill_position"  gorm:"column:bill_position;default:1;"`                              //发票信息存储位置  1商品本体 2sku
	MD5           string `json:"md5" gorm:"column:md5;type:varchar(255);"`
	model.ProductBill
	CloudGoods model2.CloudGoods `json:"cloud_goods" gorm:"foreignkey:product_id"`
	CloudPrice CloudPrice        `json:"cloud_price" gorm:"-"`
	IsPlugin   int               `json:"is_plugin" gorm:"column:is_plugin;default:0;"`
	Skus       []Sku             `json:"skus,omitempty" form:"skus"  gorm:"foreignkey:product_id;references:ID"` // sku数组

}

type Sku struct {
	source.Model
	Title         string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;"`            // 标题
	Price         uint   `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                            // 供货价(单位:分)
	CostPrice     uint   `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价(单位:分)
	OriginPrice   uint   `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价/建议零售价(单位:分)
	GuidePrice    uint   `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:指导价(单位:分);"`          // 指导价（分为单位）
	ActivityPrice uint   `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
	Stock         int    `json:"stock" form:"stock" gorm:"column:stock;comment:库存数量;"`                                   // 库存数量
	Weight        int    `json:"weight" form:"weight" gorm:"column:weight;comment:重量（g）;"`                                 // 重量（g）

	IsDisplay  int  `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:smallint;size:1;"` // 上架（1是0否）
	ProductID  uint `json:"product_id" form:"product_id" gorm:"index"`
	SupplierID uint `json:"supplier_id" form:"supplier_id" gorm:"index"`

	Code     string `json:"code" form:"code" gorm:"column:code;comment:自定义编码;type:varchar(255);size:255;"`      // 自定义编码
	Sn       string `json:"sn" form:"sn" gorm:"column:sn;comment:产品编号;type:text;"`                               // 编码
	Barcode  string `json:"barcode" form:"barcode" gorm:"column:barcode;comment:条形码;type:varchar(255);size:255;"` // 条形码
	ImageUrl string `json:"image_url" gorm:"column:image_url;comment:图片url;"`
}

func (receiver ProductList) TableName() string {
	return "products"
}

type CloudPrice struct {
	CloudOriginPrice uint `json:"cloud_origin_price" form:"cloud_origin_price" gorm:"column:cloud_origin_price;comment:市场价(单位:分);"` // 市场价 市场价
	CloudGuidePrice  uint `json:"cloud_guide_price" form:"cloud_guide_price" gorm:"column:cloud_guide_price;comment:供货价(单位:分);"`    //指导价
	CloudPrice       uint `json:"cloud_price" form:"cloud_price" gorm:"column:cloud_price;comment:结算价(单位:分);"`                      // 供货价、给采购端的协议价
}

/**
  获取可推送至云仓的商品，与推送状态
*/
//GetAfterSalesList
//
//@function: GetAfterSalesList
//@description: 分页获取Product记录
//@param: info request.ProductSearch
//@return: err error, list interface{}, total int64
func GetProductListOld(config common.SupplySetting, info request.ProductSearch) (err error, list []ProductList, total int64) {

	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	db := source.DB().Model(&ProductList{})
	db = db.Where("deleted_at is NULL")
	var products []ProductList

	isSearch := 0
	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("search_title", info.Title))
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
		isSearch++
	}

	filterQ := elastic.NewBoolQuery()
	if info.SupplierID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("search_title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
		isSearch++
	}

	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		isSearch++

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
		isSearch++
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
		isSearch++
	}

	if info.IsRecommend != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", info.IsRecommend))
		isSearch++
	}
	if info.IsNew != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_new", info.IsNew))
		isSearch++
	}
	if info.IsHot != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_hot", info.IsHot))
		isSearch++
	}
	if info.IsPromotion != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", info.IsPromotion))
		isSearch++
	}
	if info.Filter == 1 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		isSearch++
	}
	if info.Filter == 2 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 0))
		isSearch++
	}
	if info.Filter == 3 {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		filterQ.Must(elastic.NewRangeQuery("stock").Lte(0))
		isSearch++
	}
	if info.MinPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice * 100))
		isSearch++
	}
	if info.MaxPrice != 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice * 100))
		isSearch++
	}

	if info.ResGatherSupplyID != nil {
		//es = es.Query(elastic.NewMatchPhraseQuery("search_title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.ResGatherSupplyID))
		isSearch++
	} else {
		var gatherSupply []model.GatherSupply
		source.DB().Model(&model.GatherSupply{}).Where("category_id != ?", 1).Pluck("id", &gatherSupply)
		gatherSupplierIds := make([]interface{}, len(gatherSupply))
		gatherSupplierIds = append(gatherSupplierIds, 0)
		for index, value := range gatherSupply {
			gatherSupplierIds[index] = value.ID
		}
		filterQ.Must(elastic.NewTermsQuery("gather_supplier_id", gatherSupplierIds...))
	}
	boolQ.Filter(filterQ)

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	//es执行搜索
	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	var res *elastic.SearchResult
	res, err = es.Search("product" + common_data.GetOldProductIndex()).Size(limit).From(offset).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	var ids []uint
	var productSearchs []productService.ProductElasticSearch
	//获取es搜索结果
	productSearchs, err = GetSearchResult(res)
	for _, v := range productSearchs {
		ids = append(ids, v.ID)
	}
	db = db.Where("id in ?", ids)
	err = db.Preload("CloudGoods", "gather_supplies_id = ?", info.GatherSuppliesId).Order("id desc").Find(&products).Error
	for key, item := range products {
		var productPrice ProductPrice
		productPrice.Price = item.Price
		productPrice.CostPrice = item.CostPrice
		productPrice.OriginPrice = item.OriginPrice
		productPrice.GuidePrice = item.GuidePrice
		item.CloudPrice.CloudGuidePrice = CloudGuidePrice(config, productPrice)
		item.CloudPrice.CloudPrice = CloudJsPrice(config, productPrice)
		item.CloudPrice.CloudOriginPrice = CloudScPrice(config, productPrice)
		products[key].CloudPrice = item.CloudPrice
	}
	return err, products, total
}

/**
  获取可推送至云仓的商品，与推送状态
*/
//GetAfterSalesList
//
//@function: GetAfterSalesList
//@description: 分页获取Product记录
//@param: info request.ProductSearch
//@return: err error, list interface{}, total int64
func GetProductList(config common.SupplySetting, info request.ProductSearch) (err error, list []ProductList, total int64) {

	db := source.DB().Model(&ProductList{}).Preload("Skus")

	db = ProductListWhere(info, db)

	var joinWhere string
	if info.CloudStatus != nil {
		switch *info.CloudStatus {
		case 0:
			db = db.Where("products.deleted_at is NULL")
			//var cloudGoodsProductIds []uint
			//source.DB().Model(&model2.CloudGoods{}).Pluck("product_id", &cloudGoodsProductIds)
			//if len(cloudGoodsProductIds) > 0 {
			//	db.Where("`id` not in ?", cloudGoodsProductIds)
			//}
			//db.Where("(select count(1) as num from cloud_goods where cloud_goods.product_id = products.id) = 0")
			db.Where("NOT EXISTS(SELECT t2.id FROM  cloud_goods t2   WHERE    t2.product_id = products.id)")
			break
		case 1:
			db = db.Where("products.deleted_at is NULL")
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id"
			db.Where("cloud_goods.gather_supplies_id = ?", info.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			break
		case 2:
			db = db.Where("products.deleted_at is NULL")
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id and cloud_goods.is_update = 1"
			db.Where("cloud_goods.gather_supplies_id = ?", info.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			break
		case 3:
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id and cloud_goods.is_delete = 1"
			db.Where("cloud_goods.gather_supplies_id = ?", info.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			db = db.Where("products.deleted_at is not NULL")
			break
		}
	} else {
		db = db.Where("products.deleted_at is NULL")
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	if joinWhere != "" {
		db.Joins(joinWhere)
	}
	err = db.Select("products.id").Count(&total).Error
	err = db.Select("products.id,products.title,products.image_url,products.origin_price,products.price,products.cost_price,products.guide_price,products.stock,products.sales,products.supplier_id,products.gather_supply_id").Limit(limit).Offset(offset).Preload("CloudGoods", "gather_supplies_id = ?", info.GatherSuppliesId).Preload("Supplier").Preload("GatherSupply").Order("products.id desc").Find(&list).Error

	for key, item := range list {
		var productPrice ProductPrice
		if len(item.Skus) > 0 {
			item.Price = item.Skus[0].Price
			item.CostPrice = item.Skus[0].CostPrice
			item.OriginPrice = item.Skus[0].OriginPrice
			item.GuidePrice = item.Skus[0].GuidePrice

			list[key].Price = item.Skus[0].Price
			list[key].CostPrice = item.Skus[0].CostPrice
			list[key].OriginPrice = item.Skus[0].OriginPrice
			list[key].GuidePrice = item.Skus[0].GuidePrice

		}
		productPrice.Price = item.Price
		productPrice.CostPrice = item.CostPrice
		productPrice.OriginPrice = item.OriginPrice
		productPrice.GuidePrice = item.GuidePrice
		item.CloudPrice.CloudGuidePrice = CloudGuidePrice(config, productPrice)
		item.CloudPrice.CloudPrice = CloudJsPrice(config, productPrice)
		item.CloudPrice.CloudOriginPrice = CloudScPrice(config, productPrice)
		list[key].CloudPrice = item.CloudPrice
	}
	return err, list, total
}

// 商品列表条件拼接
func ProductListWhere(info request.ProductSearch, db *gorm.DB) (resDb *gorm.DB) {
	if info.Title != "" {
		db.Where("products.title like ?", "%"+info.Title+"%")
	}
	if info.Id != 0 {
		db.Where("products.id  = ?", info.Id)
	}

	if info.SupplierID != nil {
		db.Where("products.supplier_id = ?", info.SupplierID)
	}

	if info.Category1ID != 0 {
		db.Where("products.category1_id = ?", info.Category1ID)
	}

	if info.Category2ID != 0 {
		db.Where("products.category2_id = ?", info.Category2ID)
	}

	if info.Category3ID != 0 {
		db.Where("products.category3_id = ?", info.Category3ID)
	}

	if info.IsRecommend != 0 {
		db.Where("products.is_recommend = ?", info.IsRecommend)
	}

	if info.IsNew != 0 {
		db.Where("products.is_new = ?", info.IsNew)
	}

	if info.IsHot != 0 {
		db.Where("products.is_hot = ?", info.IsHot)
	}

	if info.IsPromotion != 0 {
		db.Where("products.is_promotion = ?", info.IsPromotion)
	}
	if info.Filter == 1 {
		db.Where("products.is_display = ?", 1)
	}
	if info.Filter == 2 {
		db.Where("products.is_display = ?", 0)
	}
	if info.Filter == 3 {
		db.Where("products.is_display = ?", 1).Where("products.stock = ?", 0)
	}
	if info.MinPrice != 0 {
		db.Where("products.price >= ?", info.MinPrice*100)
	}
	if info.MaxPrice != 0 {
		db.Where("products.price <= ?", info.MinPrice*100)

	}

	if info.ResGatherSupplyID != nil {
		db.Where("products.gather_supply_id  = ?", info.ResGatherSupplyID)
	} else {
		var gatherSupply []GatherSupply
		source.DB().Model(&GatherSupply{}).Where("category_id not in  ?", common.ExcludeGatherSupplysCategoryIds()).Pluck("id", &gatherSupply)
		gatherSupplierIds := make([]interface{}, len(gatherSupply))
		gatherSupplierIds = append(gatherSupplierIds, 0)
		for index, value := range gatherSupply {
			gatherSupplierIds[index] = value.ID
		}
		//var NotSource = []int{109} //不查询的来源 (有的来源 是应用里面的，不是供应链里面的)
		db.Where("products.gather_supply_id  in ? or products.gather_supply_id is null", gatherSupplierIds)
		//db.Where("source not in ?",NotSource)
		db.Where("products.is_plugin = 0")
	}
	//todo 通过聚水潭店铺搜索商品
	if info.JushuitanDistributorSupplierName != "" {
		db.Where("products.jushuitan_distributor_supplier_name like ?", "%"+info.JushuitanDistributorSupplierName+"%")
	}
	return db
}

// 推送全部条件拼接
func PushProductListWhere(info request.CloudAllPush, db *gorm.DB) (resDb *gorm.DB) {
	db = db.Where("products.deleted_at is NULL")

	if info.Title != "" {
		db.Where("products.title like ?", "%"+info.Title+"%")
	}
	if info.Id != 0 {
		db.Where("products.id  =  ?", info.Id)
	}

	if info.SupplierID != nil {
		db.Where("products.supplier_id = ?", info.SupplierID)
	}

	if info.Category1ID != 0 {
		db.Where("products.category1_id = ?", info.Category1ID)
	}

	if info.Category2ID != 0 {
		db.Where("products.category2_id = ?", info.Category2ID)
	}

	if info.Category3ID != 0 {
		db.Where("products.category3_id = ?", info.Category3ID)
	}

	if info.IsRecommend != 0 {
		db.Where("products.is_recommend = ?", info.IsRecommend)
	}

	if info.IsNew != 0 {
		db.Where("products.is_new = ?", info.IsNew)
	}

	if info.IsHot != 0 {
		db.Where("products.is_hot = ?", info.IsHot)
	}

	if info.IsPromotion != 0 {
		db.Where("products.is_promotion = ?", info.IsPromotion)
	}
	if info.Filter == 1 {
		db.Where("products.is_display = ?", 1)
	}
	if info.Filter == 2 {
		db.Where("products.is_display = ?", 0)
	}
	if info.Filter == 3 {
		db.Where("products.is_display = ?", 1).Where("products.stock = ?", 0)
	}
	if info.MinPrice != 0 {
		db.Where("products.price >= ?", info.MinPrice*100)
	}
	if info.MaxPrice != 0 {
		db.Where("products.price <= ?", info.MinPrice*100)

	}

	if info.ResGatherSupplyID != nil {
		db.Where("products.gather_supply_id  = ?", info.ResGatherSupplyID)
	} else {
		var gatherSupply []GatherSupply
		source.DB().Model(&GatherSupply{}).Where("category_id not in  ?", common.ExcludeGatherSupplysCategoryIds()).Pluck("id", &gatherSupply)
		gatherSupplierIds := make([]interface{}, len(gatherSupply))
		gatherSupplierIds = append(gatherSupplierIds, 0)
		for index, value := range gatherSupply {
			gatherSupplierIds[index] = value.ID
		}
		//var NotSource = []int{109} //不查询的来源 (有的来源 是应用里面的，不是供应链里面的)
		db.Where("products.gather_supply_id  in ? or products.gather_supply_id is null", gatherSupplierIds)
		//db.Where("source not in ?",NotSource)
		db.Where("products.is_plugin = 0")
	}
	//todo 通过聚水潭店铺搜索商品
	if info.JushuitanDistributorSupplierName != "" {
		db.Where("products.jushuitan_distributor_supplier_name like ?", "%"+info.JushuitanDistributorSupplierName+"%")
	}
	return db
}
func GetSearchResult(searchResult *elastic.SearchResult) (productSearch []productService.ProductElasticSearch, err error) {

	if searchResult.Hits.TotalHits.Value > 0 {

		for _, hit := range searchResult.Hits.Hits {

			var t productService.ProductElasticSearch
			err := json.Unmarshal(hit.Source, &t)
			if err != nil {
				// Deserialization failed
			}
			productSearch = append(productSearch, t)
		}
	}
	return
}

// 获取云仓分类
func GetCateBusinessList(config common.SupplySetting) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.GetCateBusinessList), "POST", nil, nil, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 获取运费模板
func GetFreightList(config common.SupplySetting, searchData request.FreightSearch) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.GetFreightList), "POST", nil, g.Map{"page": searchData.Page, "limit": searchData.PageSize, "is_have_default": searchData.IsHaveDefault}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 获取云仓分类
func GetStagsList(config common.SupplySetting) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.GetStagsList), "POST", nil, nil, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 获取云仓商品
func GetGoodsList(config common.SupplySetting, searchData request.CloudGoodsSearch) (err error, res *common.APIResult, total int64) {
	var body = make(map[string]interface{})
	body["page"] = searchData.Page
	body["limit"] = searchData.PageSize
	if searchData.GoodsName != "" {
		body["goods_name"] = searchData.GoodsName
	}
	if searchData.PriceMax != 0 {
		body["price_max"] = searchData.PriceMax
	}
	if searchData.PriceMin != 0 {
		body["price_min"] = searchData.PriceMin
	}
	if searchData.IsOnsale != nil {
		body["is_onsale"] = searchData.IsOnsale
	}
	if searchData.ThirdGoodsId != 0 {
		body["goods_id"] = searchData.ThirdGoodsId
	}
	res, err = common.RequestApi(string(common.GetGoodsList), "POST", nil, body, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}

	datas := res.Data.(map[string]interface{})
	var count = datas["count"].(float64)
	total = int64(count)
	if total > 0 {
		data := datas["list"]
		dataList := data.([]interface{})
		var List []interface{}
		for _, item := range dataList {
			maps := item.(map[string]interface{})
			var cloudGoods model2.CloudGoods
			var product model.Product
			source.DB().Where("gather_supplies_id=? and cloud_product_id=?", searchData.GatherSuppliesId, maps["goods_id"]).First(&cloudGoods)
			if cloudGoods.ID > 0 {
				source.DB().Where("id=? ", cloudGoods.ProductId).First(&product)
			}
			maps["cloud_goods"] = cloudGoods
			maps["product"] = product
			List = append(List, maps)
		}
		datas["list"] = List

		res.Data = datas
	}

	return
}

// 云仓下架的 中台上架的 进行上架处理
func GetGoodsOn(config common.SupplySetting, searchData request.CloudGoodsSearch) (err error) {
	var body = make(map[string]interface{})
	body["page"] = searchData.Page
	body["limit"] = searchData.PageSize
	time.Sleep(2 * time.Second)
	if searchData.GoodsName != "" {
		body["goods_name"] = searchData.GoodsName
	}
	if searchData.PriceMax != 0 {
		body["price_max"] = searchData.PriceMax
	}
	if searchData.PriceMin != 0 {
		body["price_min"] = searchData.PriceMin
	}
	if searchData.IsOnsale != nil {
		body["is_onsale"] = searchData.IsOnsale
	}
	if searchData.ThirdGoodsId != 0 {
		body["goods_id"] = searchData.ThirdGoodsId
	}
	var res *common.APIResult
	res, err = common.RequestApi(string(common.GetGoodsList), "POST", nil, body, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}

	datas := res.Data.(map[string]interface{})
	var count = datas["count"].(float64)
	var total = int(count)
	if total > 0 {
		var cloudPushProductIds request.CloudUpdateCloudGoods
		cloudPushProductIds.GatherSuppliesId = searchData.GatherSuppliesId
		data := datas["list"]
		dataList := data.([]interface{})
		for _, item := range dataList {
			maps := item.(map[string]interface{})
			var cloudGoods model2.CloudGoods
			var product model.Product
			source.DB().Where("gather_supplies_id=? and cloud_product_id=?", searchData.GatherSuppliesId, maps["goods_id"]).First(&cloudGoods)
			if cloudGoods.ID > 0 {
				source.DB().Where("id=? ", cloudGoods.ProductId).First(&product)
				if product.ID != 0 && product.IsDisplay == 1 {
					cloudPushProductIds.ProductIds = append(cloudPushProductIds.ProductIds, product.ID)
				}
			} else {
				continue
			}
		}
		if len(cloudPushProductIds.ProductIds) > 0 {
			CloudUpdateGoods(config, cloudPushProductIds)
		}
		if searchData.Page*searchData.PageSize < total {
			searchData.Page++
			_ = GetGoodsOn(config, searchData)
		}
	}

	return
}

// 商品上下架
func UpdateGoodsOnsale(config common.SupplySetting, searchData request.GoodsOnsale) (err error) {
	var res *common.APIResult
	//for _, goodsId := range searchData.ThirdGoodsId{
	res, err = common.RequestApi(string(common.UpdateGoodsOnsale), "POST", nil, g.Map{"goods_id": searchData.ThirdGoodsId, "is_onsale": searchData.IsOnsale}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	//}
	return
}

// 商品删除
func DeleteGoods(config common.SupplySetting, searchData request.DeleteGoods, gatherSuppliesId uint) (err error) {
	var res *common.APIResult
	for _, goodsId := range searchData.ThirdGoodsId {
		res, err = common.RequestApi(string(common.DeleteGoods), "POST", nil, g.Map{"goods_id": goodsId}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res.Code != 1 {
			err = errors.New(res.Message)
			return
		}
		err = source.DB().Where("cloud_product_id = ?", goodsId).Where("gather_supplies_id = ?", gatherSuppliesId).Delete(&model2.CloudGoods{}).Error
	}
	return
}

// 运费模板详情
func GetFreight(config common.SupplySetting, searchData request.FreightDetail) (err error, cloudFreight response.CloudFreight) {
	var res *common.APIResult
	res, err = common.RequestApi(string(common.GetFreight), "POST", nil, g.Map{"id": searchData.FreightId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	data := res.Data.(map[string]interface{})
	resCloudFreight := data["info"].(map[string]interface{})
	json1, _ := json.Marshal(resCloudFreight)
	_ = json.Unmarshal(json1, &cloudFreight)
	for k, v := range cloudFreight.Dispatching {
		if v.A == "全国" {
			continue
		}
		var dispatchingA []string
		//目前发现地址返回存在英文逗号 和英文分号
		if strings.Index(v.A, ",") != -1 {
			dispatchingA = strings.Split(v.A, ",")
		}
		if strings.Index(v.A, ";") != -1 {
			dispatchingA = strings.Split(v.A, ";")
		}
		if len(dispatchingA) > 0 {
			var region []model3.Region
			source.DB().Where("name in ?", dispatchingA).Find(&region)

			for _, regionItem := range region {
				var region1 model3.Region   //省
				var region2 []model3.Region //区
				var isExist = 2             //是否存在 1是2否
				for _, areaItem := range v.Area {
					if areaItem.ID == regionItem.ParentID {
						isExist = 1
						break
					}
				}
				if isExist == 1 {
					continue
				}
				var res response.Area
				if regionItem.ParentID == 0 {
					res.Name = regionItem.Name
					res.ID = regionItem.ID
				} else {
					source.DB().Where("id = ?", regionItem.ParentID).First(&region1)

					res.Name = region1.Name
					res.ID = region1.ID

					source.DB().Where("name in ?", dispatchingA).Where("parent_id", regionItem.ParentID).Find(&region2)

					for _, region2Item := range region2 {
						var resChildren response.Area
						resChildren.Name = region2Item.Name
						resChildren.ID = region2Item.ID
						res.Children = append(res.Children, resChildren)
					}
				}

				v.Area = append(v.Area, res)
			}
			cloudFreight.Dispatching[k].Area = v.Area
		}

	}
	return
}

// 删除运费模板
func DeleteFreight(config common.SupplySetting, searchData request.DeleteFreight) (err error, res *common.APIResult) {

	res, err = common.RequestApi(string(common.DeleteFreight), "POST", nil, g.Map{"id": searchData.FreightId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 添加运费模板
func AddFreight(config common.SupplySetting, addFreight request.SaveFreight) (err error, res *common.APIResult) {
	requestAddFreight := make(map[string]interface{})
	requestAddFreight["sort"] = addFreight.Sort
	requestAddFreight["name"] = addFreight.Name
	requestAddFreight["is_default"] = addFreight.IsDefault
	requestAddFreight["charge_type"] = addFreight.ChargeType
	requestAddFreight["dispatching"] = addFreight.Dispatching
	requestAddFreight["dis_dispatching"] = addFreight.DisDispatching
	requestAddFreight["publish"] = addFreight.Publish
	requestAddFreight["created"] = addFreight.Created
	requestAddFreight["modified"] = addFreight.Modified
	res, err = common.RequestApi(string(common.AddFreight), "POST", nil, requestAddFreight, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 修改运费模板
func UpdateFreight(config common.SupplySetting, saveFreight request.SaveFreight) (err error, res *common.APIResult) {
	requestAddFreight := make(map[string]interface{})
	requestAddFreight["id"] = saveFreight.Id
	requestAddFreight["sort"] = saveFreight.Sort
	requestAddFreight["name"] = saveFreight.Name
	requestAddFreight["is_default"] = saveFreight.IsDefault
	requestAddFreight["charge_type"] = saveFreight.ChargeType
	requestAddFreight["dispatching"] = saveFreight.Dispatching
	requestAddFreight["dis_dispatching"] = saveFreight.DisDispatching
	requestAddFreight["publish"] = saveFreight.Publish
	requestAddFreight["created"] = saveFreight.Created
	requestAddFreight["modified"] = saveFreight.Modified
	res, err = common.RequestApi(string(common.UpdateFreight), "POST", nil, requestAddFreight, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 获取云仓订单
func GetCloudOrderList(config common.SupplySetting, searchData request.CloudOrderSearch) (err error, res *common.APIResult, total int64) {
	var body = make(map[string]interface{})
	body["page"] = searchData.Page
	body["limit"] = searchData.PageSize
	if searchData.OrderSn != "" {
		body["order_sn"] = searchData.OrderSn
	}
	if searchData.Status != nil {
		body["status"] = searchData.Status
	}
	if searchData.Mobile != 0 {
		body["mobile"] = searchData.Mobile
	}
	if searchData.RealName != "" {
		body["real_name"] = searchData.RealName
	}
	if searchData.CreatedStartTime != 0 {
		body["created_start_time"] = searchData.CreatedStartTime
	}
	if searchData.CreatedEndTime != 0 {
		body["created_end_time"] = searchData.CreatedEndTime
	}
	res, err = common.RequestApi(string(common.GetCloudOrderList), "POST", nil, body, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	if res.Data == "" {
		err = errors.New("暂无订单")
		return
	}

	datas := res.Data.(map[string]interface{})
	var count = datas["count"].(float64)
	total = int64(count)
	if total > 0 {
		data := datas["list"]
		dataList := data.([]interface{})
		var List []interface{}
		for _, item := range dataList {

			maps := item.(map[string]interface{})
			maps1 := maps["order"].(map[string]interface{})

			var cloudOrder model2.CloudOrder
			source.DB().Where("gather_supplies_id=? and cloud_order_id=?", searchData.GatherSuppliesId, maps1["id"]).First(&cloudOrder)

			orderGoods := maps["order_goods"]
			orderGoodsList := orderGoods.([]interface{})
			var orderGoodsLists []interface{}           //拼接order goods 数组
			var itemList = make(map[string]interface{}) //拼接总数组

			for _, itemGood := range orderGoodsList {
				var cloudGoods model2.CloudGoods
				itemGoods := itemGood.(map[string]interface{})
				source.DB().Where("gather_supplies_id=? and cloud_product_id=?", searchData.GatherSuppliesId, itemGoods["goods_id"]).First(&cloudGoods)

				itemGoods["cloud_goods"] = cloudGoods
				orderGoodsLists = append(orderGoodsLists, itemGoods)
			}
			itemList["cloud_order"] = cloudOrder
			itemList["order"] = maps["order"]
			itemList["order_goods"] = orderGoodsLists

			List = append(List, itemList)
		}
		datas["list"] = List

		res.Data = datas
	}

	return
}

// 获取云仓物流公司
func GetDeliverList(config common.SupplySetting) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.GetDeliverList), "POST", nil, nil, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 订单详情
func GetCloudOrder(config common.SupplySetting, searchData request.CloudOrderDetatil) (err error, res *common.APIResult) {

	res, err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": searchData.OrderId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	orderData := res.Data.(map[string]interface{})
	orderItem := orderData["order_goods_detail"]
	orderItems := orderItem.([]interface{})
	var orderGoodsDetail []interface{}
	for _, item := range orderItems {
		var cloudGoods model2.CloudGoods
		itemOrderGoods := item.(map[string]interface{})
		source.DB().Where("gather_supplies_id=? and cloud_product_id=?", searchData.GatherSuppliesId, itemOrderGoods["goods_id"]).First(&cloudGoods)
		itemOrderGoods["cloud_goods"] = cloudGoods
		orderGoodsDetail = append(orderGoodsDetail, itemOrderGoods)
	}
	orderData["order_goods_detail"] = orderGoodsDetail
	res.Data = orderData
	return
}

// 云仓订单发货（旧）
func CloudOrderSend(config common.SupplySetting, cloudOrderSend request.CloudOrderSend) (err error, res *common.APIResult) {

	res, err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": cloudOrderSend.OrderId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	orderData := res.Data.(map[string]interface{})
	orderItem := orderData["order_goods_detail"]
	orderItems := orderItem.([]interface{})

	var sendData []interface{}

	var orderGoods = make(map[string]interface{})
	for _, item := range orderItems {
		itemOrderGoods := item.(map[string]interface{})
		orderGoods["goods_order_sn"] = itemOrderGoods["goods_order_sn"]
		orderGoods["express_name"] = cloudOrderSend.ExpressName
		orderGoods["express_sn"] = cloudOrderSend.ExpressSn

		sendData = append(sendData, orderGoods)

	}
	res.Data = sendData
	sendBodyData, _ := json.Marshal(sendData)
	res, err = common.RequestApiJson(string(common.CloudOrderSend), "POST", nil, sendBodyData, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 云仓订单发货（新）
func CloudOrderSendNew(config common.SupplySetting, cloudOrderSendNew request.CloudOrderSendNew) (err error, res *common.APIResult) {
	//先屏蔽避免前端误发货
	sendBodyData, _ := json.Marshal(cloudOrderSendNew.CloudOrderSendApi)
	res, err = common.RequestApiJson(string(common.CloudOrderSend), "POST", nil, sendBodyData, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}

	//如果是中台记录表的订单 则同步发货中台订单
	var cloudOrder model2.CloudOrder
	source.DB().Where("cloud_order_id = ?", cloudOrderSendNew.CloudOrderId).Where("status != ?", -1).First(&cloudOrder)
	if cloudOrder.ID != 0 {
		//var orderModel model4.Order
		//source.DB().Where("id = ?", cloudOrder.OrderId).First(&orderModel)
		//if orderModel.Status == model4.WaitSend {
		//	var cloudOrderDetail CloudOrderDetail
		//	res,err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": cloudOrderSendNew.CloudOrderId}, config)
		//	if err != nil {
		//		err = errors.New(err.Error())
		//		return
		//	}
		//	if res.Code != 1 {
		//		log.Log().Error("同步发货中台订单失败:获取云仓订单详情失败,订单id!"+strconv.Itoa(cloudOrderSendNew.CloudOrderId), zap.Any("err", res.Message))
		//		return
		//	}
		//	if res.Data == "" {
		//		log.Log().Error("同步发货中台订单失败:获取云仓订单详情失败,订单id!"+strconv.Itoa(cloudOrderSendNew.CloudOrderId), zap.Any("err", res.Message))
		//		return
		//	}
		//	datas := res.Data.(map[string]interface{})
		//
		//	json1, _ := json.Marshal(datas)
		//	_ = json.Unmarshal(json1, &cloudOrderDetail)
		//
		//	err = CloudOrderCreateStep5(config, cloudOrderDetail)
		//	if err != nil {
		//		err = errors.New("同步发货中台订单失败，原因：" + err.Error())
		//	}
		//}

		//发货成功之后同步云仓订单状态
		var res1 *common.APIResult

		res1, err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": cloudOrder.CloudOrderId}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res1.Code != 1 {
			err = errors.New("获取订单详情失败,订单id" + strconv.Itoa(cloudOrder.CloudOrderId))
			return
		}
		if res1.Data == "" {
			err = errors.New("获取订单详情数据失败,订单id" + strconv.Itoa(cloudOrder.CloudOrderId))
			return
		}
		datas := res1.Data.(map[string]interface{})
		var getCloudOrder CloudOrderDetail
		json1, _ := json.Marshal(datas)
		_ = json.Unmarshal(json1, &getCloudOrder)
		cloudOrder.Status = getCloudOrder.Order.Status
		source.DB().Where("id = ?", cloudOrder.ID).Updates(cloudOrder)
		for _, item := range getCloudOrder.OrderGoodsDetail {
			source.DB().Model(&model2.CloudOrderItem{}).Where("cloud_goods_order_sn = ?", item.GoodsOrderSn).Updates(g.Map{"status": item.GoodsStatus})
		}
		//改变记录表状态
		cloudOrder.Status = 2
		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)
	}

	return
}

// 云仓订单发货（新）
func UpdateCloudOrderSend(config common.SupplySetting, cloudOrderSendNew request.CloudOrderSendNew) (err error, res *common.APIResult) {
	//先屏蔽避免前端误发货
	sendBodyData, _ := json.Marshal(cloudOrderSendNew.CloudOrderSendApi)
	res, err = common.RequestApiJson(string(common.CloudOrderAgainSends), "POST", nil, sendBodyData, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}

	//如果是中台记录表的订单 则同步发货中台订单
	var cloudOrder model2.CloudOrder
	source.DB().Where("cloud_order_id = ?", cloudOrderSendNew.CloudOrderId).Where("status != ?", -1).First(&cloudOrder)
	if cloudOrder.ID != 0 {
		//var orderModel model4.Order
		//source.DB().Where("id = ?", cloudOrder.OrderId).First(&orderModel)
		//if orderModel.Status == model4.WaitSend {
		//	var cloudOrderDetail CloudOrderDetail
		//	res,err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": cloudOrderSendNew.CloudOrderId}, config)
		//	if err != nil {
		//		err = errors.New(err.Error())
		//		return
		//	}
		//	if res.Code != 1 {
		//		log.Log().Error("同步发货中台订单失败:获取云仓订单详情失败,订单id!"+strconv.Itoa(cloudOrderSendNew.CloudOrderId), zap.Any("err", res.Message))
		//		return
		//	}
		//	if res.Data == "" {
		//		log.Log().Error("同步发货中台订单失败:获取云仓订单详情失败,订单id!"+strconv.Itoa(cloudOrderSendNew.CloudOrderId), zap.Any("err", res.Message))
		//		return
		//	}
		//	datas := res.Data.(map[string]interface{})
		//
		//	json1, _ := json.Marshal(datas)
		//	_ = json.Unmarshal(json1, &cloudOrderDetail)
		//
		//	err = CloudOrderCreateStep5(config, cloudOrderDetail)
		//	if err != nil {
		//		err = errors.New("同步发货中台订单失败，原因：" + err.Error())
		//	}
		//}

		//发货成功之后同步云仓订单状态
		var res1 *common.APIResult

		res1, err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": cloudOrder.CloudOrderId}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res1.Code != 1 {
			err = errors.New("获取订单详情失败,订单id" + strconv.Itoa(cloudOrder.CloudOrderId))
			return
		}
		if res1.Data == "" {
			err = errors.New("获取订单详情数据失败,订单id" + strconv.Itoa(cloudOrder.CloudOrderId))
			return
		}
		datas := res1.Data.(map[string]interface{})
		var getCloudOrder CloudOrderDetail
		json1, _ := json.Marshal(datas)
		_ = json.Unmarshal(json1, &getCloudOrder)
		cloudOrder.Status = getCloudOrder.Order.Status
		source.DB().Where("id = ?", cloudOrder.ID).Updates(cloudOrder)
		for _, item := range getCloudOrder.OrderGoodsDetail {
			source.DB().Model(&model2.CloudOrderItem{}).Where("cloud_goods_order_sn = ?", item.GoodsOrderSn).Updates(g.Map{"status": item.GoodsStatus})
		}
		//改变记录表状态
		cloudOrder.Status = 2
		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)
	}

	return
}

// 获取云仓订单
func GetRefundList(config common.SupplySetting, searchData request.CloudRefundSearch) (err error, res *common.APIResult) {
	var body = make(map[string]interface{})
	body["page"] = searchData.Page
	body["limit"] = searchData.PageSize
	if searchData.OrderSn != "" {
		body["order_sn"] = searchData.OrderSn
	}
	if searchData.RefundType != nil {
		body["refund_type"] = searchData.RefundType
	}
	if searchData.GoodsOrderSn != "" {
		body["goods_order_sn"] = searchData.GoodsOrderSn
	}

	res, err = common.RequestApi(string(common.GetRefundList), "POST", nil, body, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}

	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	if res.Data == "" {
		err = errors.New("暂无售后信息")
		return
	}

	return
}

// 售后详情
func GetRefundDetatil(config common.SupplySetting, cloudRefund request.CloudRefund) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.GetRefund), "POST", nil, g.Map{"order_refund_sn": cloudRefund.RefundSn}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 售后详情
func GetAddressList(config common.SupplySetting, cloudRefundAddressList request.CloudRefundAddressList) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.GetAddressList), "POST", nil, g.Map{"page": cloudRefundAddressList.Page, "limit": cloudRefundAddressList.PageSize}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 获取地区
func GetRegionList(config common.SupplySetting, cloudRegion request.CloudRegion) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.GetRegionList), "POST", nil, g.Map{"pid": cloudRegion.Pid}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 添加退货地址
func AddRefundAddress(config common.SupplySetting, saveRefundAddress request.SaveRefundAddress) (err error, res *common.APIResult) {
	requestRefundAddress := make(map[string]interface{})
	requestRefundAddress["real_name"] = saveRefundAddress.RealName
	requestRefundAddress["mobile"] = saveRefundAddress.Mobile
	requestRefundAddress["address"] = saveRefundAddress.Address
	requestRefundAddress["province_id"] = saveRefundAddress.ProvinceId
	requestRefundAddress["city_id"] = saveRefundAddress.CityId
	requestRefundAddress["district_id"] = saveRefundAddress.DistrictId
	requestRefundAddress["is_default"] = saveRefundAddress.IsDefault
	res, err = common.RequestApi(string(common.AddRefundAddress), "POST", nil, requestRefundAddress, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}

	return
}

// 修改退货地址
func UpdateRefundAddress(config common.SupplySetting, saveRefundAddress request.SaveRefundAddress) (err error, res *common.APIResult) {
	requestAddFreight := make(map[string]interface{})
	requestAddFreight["id"] = saveRefundAddress.Id
	requestAddFreight["real_name"] = saveRefundAddress.RealName
	requestAddFreight["mobile"] = saveRefundAddress.Mobile
	requestAddFreight["address"] = saveRefundAddress.Address
	requestAddFreight["province_id"] = saveRefundAddress.ProvinceId
	requestAddFreight["city_id"] = saveRefundAddress.CityId
	requestAddFreight["district_id"] = saveRefundAddress.DistrictId
	requestAddFreight["is_default"] = saveRefundAddress.IsDefault
	res, err = common.RequestApi(string(common.UpdateRefundAddress), "POST", nil, requestAddFreight, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 获取售后地址详情
func GetRefundAddress(config common.SupplySetting, saveRefundAddress request.SaveRefundAddress) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.GetRefundAddress), "POST", nil, g.Map{"id": saveRefundAddress.Id}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 删除
func DeleteRefundAddress(config common.SupplySetting, saveRefundAddress request.SaveRefundAddress) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.DeleteRefundAddress), "POST", nil, g.Map{"id": saveRefundAddress.Id}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 售后同意
func CloudRefundAgree(config common.SupplySetting, cloudRefundAgree request.CloudRefundAgree) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.RefundAgree), "POST", nil, g.Map{"order_refund_sn": cloudRefundAgree.OrderRefundSn, "order_refund_address": cloudRefundAgree.OrderRefundAddress}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

// 售后拒绝
func CloudRefundReject(config common.SupplySetting, cloudRefundAgree request.CloudRefundReject) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.RefundReject), "POST", nil, g.Map{"order_refund_sn": cloudRefundAgree.OrderRefundSn, "reason": cloudRefundAgree.Reason, "content": cloudRefundAgree.Content}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	return
}

type Product struct {
	model.Product
	Brand Brand `json:"brand" form:"brand"` // 品牌
}

type ProductPrice struct {
	OriginPrice uint `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"` // 市场价 市场价
	CostPrice   uint `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`       // 成本价，上游给的协议价
	Price       uint `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                      // 供货价、给采购端的协议价
	GuidePrice  uint `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:指导价(单位:分);"`    //指导价

}
type Brand struct {
	ID   int    `json:"id" gorm:"column:id"`
	Name string `json:"name" gorm:"column:name"`
}

// 推送成功返回值
type AddPushData struct {
	GoodsId uint `json:"goods_id" gorm:"column:id"`
}

// 推送全部商品
func CloudAllPushGoods(config common.SupplySetting, info request.CloudAllPush) (err error, totalTime string) {
	db := source.DB().Model(&ProductList{})

	db = PushProductListWhere(info, db)

	var joinWhere string
	if info.CloudStatus != nil {
		switch *info.CloudStatus {
		case 0:
			var cloudGoodsProductIds []uint
			source.DB().Model(&model2.CloudGoods{}).Pluck("product_id", &cloudGoodsProductIds)
			if len(cloudGoodsProductIds) > 0 {
				db.Where("`id` not in ?", cloudGoodsProductIds)
			}
			//db.Where("(select count(1) as num from cloud_goods where cloud_goods.product_id = products.id) = 0")
			break
		case 1:
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id"
			db.Where("cloud_goods.gather_supplies_id = ?", info.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			break
		case 2:
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id and cloud_goods.is_update = 1"
			db.Where("cloud_goods.gather_supplies_id = ?", info.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			break
		case 3:
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id and cloud_goods.is_delete = 1"
			db.Where("cloud_goods.gather_supplies_id = ?", info.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			break
		}
	}
	if joinWhere != "" {
		db.Joins(joinWhere)
	}
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	if total == 0 {
		err = errors.New("没有商品需要推送")
		return
	}
	//计算导入需要的大概时间
	var loop = int(math.Ceil(float64(total) / float64(100))) //循环次数
	importTime := float64(100+(loop*10)) * 0.09

	totalTime = "大约需要" + strconv.FormatFloat(importTime, 'f', -1, 64) + "秒"
	if importTime > 60 {
		importTime = importTime / 60
		totalTime = "大约需要" + strconv.FormatFloat(importTime, 'f', -1, 64) + "分钟"
	}

	go ImportCloudGoodsRun(config, info, total)
	return
}
func ImportCloudGoodsRun(config common.SupplySetting, cloudAllPush request.CloudAllPush, total int64) {

	var pageSize = 100
	var loop = int(math.Ceil(float64(total) / float64(pageSize))) //循环次数

	// 创建db
	db := source.DB().Model(&Product{})

	db = PushProductListWhere(cloudAllPush, db)

	var joinWhere string
	if cloudAllPush.CloudStatus != nil {
		switch *cloudAllPush.CloudStatus {
		case 0:
			var cloudGoodsProductIds []uint
			source.DB().Model(&model2.CloudGoods{}).Pluck("product_id", &cloudGoodsProductIds)
			if len(cloudGoodsProductIds) > 0 {
				db.Where("`id` not in ?", cloudGoodsProductIds)
			}
			//db.Where("(select count(1) as num from cloud_goods where cloud_goods.product_id = products.id) = 0")
			break
		case 1:
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id"
			db.Where("cloud_goods.gather_supplies_id = ?", cloudAllPush.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			break
		case 2:
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id and cloud_goods.is_update = 1"
			db.Where("cloud_goods.gather_supplies_id = ?", cloudAllPush.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			break
		case 3:
			joinWhere = "INNER join cloud_goods on products.id = cloud_goods.product_id and cloud_goods.is_delete = 1"
			db.Where("cloud_goods.gather_supplies_id = ?", cloudAllPush.GatherSuppliesId)
			db = db.Where("cloud_goods.deleted_at is NULL")
			break
		}
	}
	if joinWhere != "" {
		db.Joins(joinWhere)
	}
	//拼接推送商品需要的条件
	var cloudPushProductIds request.CloudPushProductIds
	cloudPushProductIds.GoodsDes = cloudAllPush.GoodsDes
	cloudPushProductIds.CloudCategory3Id = cloudAllPush.CloudCategory3Id
	cloudPushProductIds.CloudCategory2Id = cloudAllPush.CloudCategory2Id
	cloudPushProductIds.CloudCategory1Id = cloudAllPush.CloudCategory1Id
	cloudPushProductIds.ProducingArea = cloudAllPush.ProducingArea
	cloudPushProductIds.GatherSuppliesId = cloudAllPush.GatherSuppliesId
	cloudPushProductIds.DelayCompensate = cloudAllPush.DelayCompensate
	cloudPushProductIds.AftersaleTime = cloudAllPush.AftersaleTime
	cloudPushProductIds.FreightId = cloudAllPush.FreightId
	cloudPushProductIds.DeliverArea = cloudAllPush.DeliverArea
	cloudPushProductIds.Stags = cloudAllPush.Stags

	var batch = "cloudPush" + strconv.FormatInt(time.Now().Unix(), 10)

	var wg sync.WaitGroup
	for i := 1; i <= loop; i++ {
		wg.Add(1)
		offset := pageSize * (i - 1)
		err := db.Limit(pageSize).Offset(offset).Pluck("products.id", &cloudPushProductIds.ProductIds).Error
		if err != nil {
			log.Log().Error("全部推送查询商品!", zap.Any("err", err))
			return
		}
		var cloudPushGoodsMessage model2.CloudPushGoodsMessage

		cloudPushGoodsMessage.ProductIds = strings.Replace(strings.Trim(fmt.Sprint(cloudPushProductIds.ProductIds), "[]"), " ", ",", -1)
		cloudPushGoodsMessage.Status = 0
		cloudPushGoodsMessage.Batch = batch
		cloudPushGoodsMessage.GatherSuppliesId = cloudAllPush.GatherSuppliesId
		source.DB().Create(&cloudPushGoodsMessage)
		//for _, value := range productData {
		//	cloudPushProductIds.ProductIds = append(cloudPushProductIds.ProductIds,value.ID)
		//}
		go func(wg *sync.WaitGroup, i int, cloudPushProductIds request.CloudPushProductIds, cloudPushGoodsMessage model2.CloudPushGoodsMessage) {
			defer wg.Done()
			errString, _ := CloudPushGoodsAll(config, cloudPushProductIds)
			if errString != "" {
				cloudPushGoodsMessage.Status = 2
				cloudPushGoodsMessage.ErrorMsg = errString
				source.DB().Where("id = ?", cloudPushGoodsMessage.ID).Updates(&cloudPushGoodsMessage)

				log.Log().Error("中台推送商品结果错误!", zap.Any("err", errString))
			} else {
				cloudPushGoodsMessage.Status = 1
				source.DB().Where("id = ?", cloudPushGoodsMessage.ID).Updates(&cloudPushGoodsMessage)
			}
		}(&wg, i, cloudPushProductIds, cloudPushGoodsMessage)
		//清空上次使用的商品id
		cloudPushProductIds.ProductIds = []uint{}
	}
	wg.Wait()
	return
}

// 添加云仓商品
func CloudPushGoodsAll(config common.SupplySetting, cloudPushProductIds request.CloudPushProductIds) (errString string, res *common.APIResult) {
	var err error
	for _, v := range cloudPushProductIds.ProductIds {

		var cloudGoods model2.CloudGoods

		source.DB().Where("deleted_at is NULL").Where("product_id = ?", v).Where("gather_supplies_id = ?", cloudPushProductIds.GatherSuppliesId).First(&cloudGoods)
		//跳过已导入的
		if cloudGoods.ID != 0 {
			//如果需要更新 则更新
			if cloudGoods.IsUpdate == 1 {
				_, _ = CloudUpdateGoodsRun(config, cloudGoods, v)
			}
			continue
		}

		var product Product
		err = source.DB().Preload("Brand").Preload("Skus").Where("id = ?", v).First(&product).Error
		//如果商品没查询到就不执行 可能商品不存在
		if err != nil {
			errString += "商品不存在id:" + strconv.Itoa(int(v))
			continue
		}
		var categoryId uint
		if cloudPushProductIds.CloudCategory3Id != 0 {
			categoryId = cloudPushProductIds.CloudCategory3Id
		} else if cloudPushProductIds.CloudCategory2Id != 0 {
			categoryId = cloudPushProductIds.CloudCategory2Id
		} else if cloudPushProductIds.CloudCategory1Id != 0 {
			categoryId = cloudPushProductIds.CloudCategory1Id
		} else {
			errString += "请选择分类"
			continue
		}
		var cloudPushGoods model2.CloudPushGoods
		//商品计算初始价格
		var productPrice ProductPrice
		productPrice.Price = product.Price
		productPrice.CostPrice = product.CostPrice
		productPrice.OriginPrice = product.OriginPrice
		productPrice.GuidePrice = product.GuidePrice
		var brandId uint = 0
		var brandName = "暂无品牌"
		//暂时云仓不需要品牌
		//if product.Brand.ID != 0 {
		//	brandId = uint(product.Brand.ID)
		//	brandName = product.Brand.Name
		//}
		//log.Log().Info("importCloudPushGoods", zap.Any("id", product.ID))
		//if utf8.RuneCountInString(product.Title)>50 {
		//	product.Title = string([]rune(product.Title)[:50]) //保持商品名称50个字符
		//}
		//没有详情的时候赋值暂无详情
		if product.DetailImages == "" {
			product.DetailImages = "暂无详情"
		}
		//if len(product.Skus) == 0 {
		//	//err = errors.New("商品规格异常:商品id:"+ strconv.Itoa(int(product.ID)))
		//	errString += "商品规格异常:商品id:"+ strconv.Itoa(int(product.ID))
		//	return
		//}
		err = verifySkus(product)
		if err != nil {
			errString += err.Error()
			return
		}

		cloudPushGoods.GoodsName = product.Title
		cloudPushGoods.GoodsDes = cloudPushProductIds.GoodsDes
		cloudPushGoods.CategoryId = categoryId
		cloudPushGoods.Price = CloudGuidePrice(config, productPrice)
		cloudPushGoods.JsPrice = CloudJsPrice(config, productPrice)
		cloudPushGoods.ScPrice = CloudScPrice(config, productPrice)
		cloudPushGoods.ProducingArea = cloudPushProductIds.ProducingArea
		cloudPushGoods.DeliverArea = cloudPushProductIds.DeliverArea
		cloudPushGoods.FreightId = cloudPushProductIds.FreightId
		cloudPushGoods.AftersaleTime = cloudPushProductIds.AftersaleTime
		cloudPushGoods.DelayCompensate = cloudPushProductIds.DelayCompensate
		cloudPushGoods.GoodsBrand = brandName
		cloudPushGoods.BrandId = brandId
		cloudPushGoods.Unit = product.Unit
		cloudPushGoods.Stags = cloudPushProductIds.Stags
		cloudPushGoods.OutGoodsId = product.ID
		cloudPushGoods.Description = product.DetailImages
		cloudPushGoods.Imgs = PushCloudIms(product)
		cloudPushGoods.Specs = PushCloudSpecs(product)
		cloudPushGoods.SpecsGroup = PushCloudSpecsGroup(config, product)

		fmt.Println(cloudPushGoods)

		requestCloudPushGoods := make(map[string]interface{})
		requestCloudPushGoods["goods_name"] = cloudPushGoods.GoodsName
		requestCloudPushGoods["goods_des"] = cloudPushGoods.GoodsDes
		requestCloudPushGoods["category_id"] = cloudPushGoods.CategoryId
		requestCloudPushGoods["price"] = cloudPushGoods.Price
		requestCloudPushGoods["js_price"] = cloudPushGoods.JsPrice
		requestCloudPushGoods["sc_price"] = cloudPushGoods.ScPrice
		requestCloudPushGoods["producing_area"] = cloudPushGoods.ProducingArea
		requestCloudPushGoods["deliver_area"] = cloudPushGoods.DeliverArea
		requestCloudPushGoods["weight"] = cloudPushGoods.Weight
		requestCloudPushGoods["freight_id"] = cloudPushGoods.FreightId
		requestCloudPushGoods["aftersale_time"] = cloudPushGoods.AftersaleTime
		requestCloudPushGoods["delay_compensate"] = cloudPushGoods.DelayCompensate
		requestCloudPushGoods["goods_brand"] = cloudPushGoods.GoodsBrand
		requestCloudPushGoods["brand_id"] = cloudPushGoods.BrandId
		requestCloudPushGoods["stags"] = cloudPushGoods.Stags
		requestCloudPushGoods["out_goods_id"] = cloudPushGoods.OutGoodsId
		requestCloudPushGoods["description"] = cloudPushGoods.Description
		requestCloudPushGoods["web_url"] = cloudPushGoods.WebUrl
		requestCloudPushGoods["imgs"] = cloudPushGoods.Imgs
		requestCloudPushGoods["specs"] = cloudPushGoods.Specs
		requestCloudPushGoods["unit"] = cloudPushGoods.Unit
		requestCloudPushGoods["specs_group"] = cloudPushGoods.SpecsGroup

		res, err = common.RequestApi(string(common.CloudAddGoods), "POST", nil, requestCloudPushGoods, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res.Code != 1 {
			errString += "商品id：" + strconv.Itoa(int(product.ID)) + ":" + res.Message
			//err = errors.New("商品id："+ strconv.Itoa(int(product.ID)) +":"+res.Message)
			continue
		}

		data, _ := json.Marshal(res.Data)
		var addPushData AddPushData
		_ = json.Unmarshal(data, &addPushData)
		fmt.Printf("推送成功id：%i", addPushData.GoodsId)
		//添加记录表
		err = source.DB().Create(&model2.CloudGoods{
			ProductId:        product.ID,
			CloudProductId:   addPushData.GoodsId,
			GatherSuppliesId: cloudPushProductIds.GatherSuppliesId,
		}).Error
		if err != nil {
			errString += "商品id：" + strconv.Itoa(int(product.ID)) + ":添加CloudGoods表失败"
			continue
			//err = errors.New("添加CloudGoods表失败")
			//return
		}
		//如果推送成功自动上架 执行上架
		if config.Cloud.CloudIsPushAutoShelf == 1 {
			var thirdGoodsId []int
			thirdGoodsId = append(thirdGoodsId, int(addPushData.GoodsId))
			err = UpdateGoodsOnsale(config, request.GoodsOnsale{
				CloudCommon:  request.CloudCommon{GatherSuppliesId: cloudPushProductIds.GatherSuppliesId},
				ThirdGoodsId: thirdGoodsId,
				IsOnsale:     1,
			})
			if err != nil {
				errString += "商品id：" + strconv.Itoa(int(product.ID)) + "自动上架失败" + err.Error()
				continue
			}
		}

	}

	return
}

/*
*

	规格校验
*/
func verifySkus(product Product) (err error) {
	if len(product.Skus) == 0 {
		err = errors.New("商品规格异常(没有规格):商品id:" + strconv.Itoa(int(product.ID)))
		return
	}

	optionsLen := len(product.Skus[0].Options)
	if optionsLen == 0 {
		err = errors.New("商品规格异常(没有规格项):商品id:" + strconv.Itoa(int(product.ID)))
		return
	}
	//fmt.Printf("len%v",len(product.Skus[0].Options))
	//cloudSpecs1 := make(map[int]model2.Specs)

	for _, item := range product.Skus {
		if optionsLen != len(item.Options) {
			err = errors.New("商品规格异常(规格项数量不一致):商品id:" + strconv.Itoa(int(product.ID)))

			return
		}
	}
	return
}

// 添加云仓商品
func CloudPushGoods(config common.SupplySetting, cloudPushProductIds request.CloudPushProductIds) (err error, res *common.APIResult) {
	for _, v := range cloudPushProductIds.ProductIds {
		//log.Log().Info("推送云仓商品:循环推送", zap.Any("product_id", v))

		var cloudGoods model2.CloudGoods

		source.DB().Where("deleted_at is NULL").Where("product_id = ?", v).Where("gather_supplies_id = ?", cloudPushProductIds.GatherSuppliesId).First(&cloudGoods)
		//跳过已导入的
		if cloudGoods.ID != 0 {
			//如果需要更新 则更新
			if cloudGoods.IsUpdate == 1 {
				_, _ = CloudUpdateGoodsRun(config, cloudGoods, v)
			}
			continue
		}

		var product Product
		err = source.DB().Preload("Brand").Preload("Skus").Where("id = ?", v).First(&product).Error
		//如果商品没查询到就不执行 可能商品不存在
		if err != nil {
			err = errors.New("商品不存在id:" + strconv.Itoa(int(v)))
			return
		}
		var categoryId uint
		if cloudPushProductIds.CloudCategory3Id != 0 {
			categoryId = cloudPushProductIds.CloudCategory3Id
		} else if cloudPushProductIds.CloudCategory2Id != 0 {
			categoryId = cloudPushProductIds.CloudCategory2Id
		} else if cloudPushProductIds.CloudCategory1Id != 0 {
			categoryId = cloudPushProductIds.CloudCategory1Id
		} else {
			err = errors.New("请选择分类")
			return
		}
		err = verifySkus(product)
		if err != nil {
			return
		}
		//没有详情的时候赋值暂无详情
		if product.DetailImages == "" {
			product.DetailImages = "暂无详情"
		}
		var cloudPushGoods model2.CloudPushGoods
		//商品计算初始价格
		var productPrice ProductPrice
		productPrice.Price = product.Skus[0].Price
		productPrice.CostPrice = product.Skus[0].CostPrice
		productPrice.OriginPrice = product.Skus[0].OriginPrice
		productPrice.GuidePrice = product.Skus[0].GuidePrice
		var brandId uint = 0
		var brandName = "暂无品牌"
		//暂时云仓不需要品牌
		//if product.Brand.ID != 0 {
		//	brandId = uint(product.Brand.ID)
		//	brandName = product.Brand.Name
		//}
		//log.Log().Info("importCloudPushGoods", zap.Any("id", product.ID))

		//if utf8.RuneCountInString(product.Title)>50 {
		//	product.Title = string([]rune(product.Title)[:50]) //保持商品名称50个字符
		//}
		//if len(product.Skus) == 0 {
		//	err = errors.New("商品规格异常:商品id:"+ strconv.Itoa(int(product.ID)))
		//	return
		//}

		cloudPushGoods.GoodsName = product.Title
		cloudPushGoods.GoodsDes = cloudPushProductIds.GoodsDes
		cloudPushGoods.CategoryId = categoryId
		cloudPushGoods.Price = CloudGuidePrice(config, productPrice)
		cloudPushGoods.JsPrice = CloudJsPrice(config, productPrice)
		cloudPushGoods.ScPrice = CloudScPrice(config, productPrice)
		cloudPushGoods.ProducingArea = cloudPushProductIds.ProducingArea
		cloudPushGoods.DeliverArea = cloudPushProductIds.DeliverArea
		cloudPushGoods.FreightId = cloudPushProductIds.FreightId
		cloudPushGoods.AftersaleTime = cloudPushProductIds.AftersaleTime
		cloudPushGoods.DelayCompensate = cloudPushProductIds.DelayCompensate
		cloudPushGoods.GoodsBrand = brandName
		cloudPushGoods.BrandId = brandId
		cloudPushGoods.Unit = product.Unit
		cloudPushGoods.Stags = cloudPushProductIds.Stags
		cloudPushGoods.OutGoodsId = product.ID
		cloudPushGoods.Description = product.DetailImages
		cloudPushGoods.Imgs = PushCloudIms(product)
		cloudPushGoods.Specs = PushCloudSpecs(product)
		cloudPushGoods.SpecsGroup = PushCloudSpecsGroup(config, product)

		fmt.Println(cloudPushGoods)

		requestCloudPushGoods := make(map[string]interface{})
		requestCloudPushGoods["goods_name"] = cloudPushGoods.GoodsName
		requestCloudPushGoods["goods_des"] = cloudPushGoods.GoodsDes
		requestCloudPushGoods["category_id"] = cloudPushGoods.CategoryId
		requestCloudPushGoods["price"] = cloudPushGoods.Price
		requestCloudPushGoods["js_price"] = cloudPushGoods.JsPrice
		requestCloudPushGoods["sc_price"] = cloudPushGoods.ScPrice
		requestCloudPushGoods["producing_area"] = cloudPushGoods.ProducingArea
		requestCloudPushGoods["deliver_area"] = cloudPushGoods.DeliverArea
		requestCloudPushGoods["weight"] = cloudPushGoods.Weight
		requestCloudPushGoods["freight_id"] = cloudPushGoods.FreightId
		requestCloudPushGoods["aftersale_time"] = cloudPushGoods.AftersaleTime
		requestCloudPushGoods["delay_compensate"] = cloudPushGoods.DelayCompensate
		requestCloudPushGoods["goods_brand"] = cloudPushGoods.GoodsBrand
		requestCloudPushGoods["brand_id"] = cloudPushGoods.BrandId
		requestCloudPushGoods["stags"] = cloudPushGoods.Stags
		requestCloudPushGoods["out_goods_id"] = cloudPushGoods.OutGoodsId
		requestCloudPushGoods["description"] = cloudPushGoods.Description
		requestCloudPushGoods["web_url"] = cloudPushGoods.WebUrl
		requestCloudPushGoods["imgs"] = cloudPushGoods.Imgs
		requestCloudPushGoods["specs"] = cloudPushGoods.Specs
		requestCloudPushGoods["unit"] = cloudPushGoods.Unit
		requestCloudPushGoods["specs_group"] = cloudPushGoods.SpecsGroup

		res, err = common.RequestApi(string(common.CloudAddGoods), "POST", nil, requestCloudPushGoods, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res.Code != 1 {
			err = errors.New("商品id：" + strconv.Itoa(int(product.ID)) + ":" + res.Message)
			return
		}

		data, _ := json.Marshal(res.Data)
		var addPushData AddPushData
		_ = json.Unmarshal(data, &addPushData)
		//log.Log().Info("importCloudPushGoods:推送成功id", zap.Any("id", addPushData.GoodsId))
		//添加记录表
		err = source.DB().Create(&model2.CloudGoods{
			ProductId:        product.ID,
			CloudProductId:   addPushData.GoodsId,
			GatherSuppliesId: cloudPushProductIds.GatherSuppliesId,
		}).Error
		if err != nil {
			err = errors.New("添加CloudGoods表失败")
			return
		}
		//如果推送成功自动上架 执行上架
		if config.Cloud.CloudIsPushAutoShelf == 1 {
			var thirdGoodsId []int
			thirdGoodsId = append(thirdGoodsId, int(addPushData.GoodsId))
			err = UpdateGoodsOnsale(config, request.GoodsOnsale{
				CloudCommon:  request.CloudCommon{GatherSuppliesId: cloudPushProductIds.GatherSuppliesId},
				ThirdGoodsId: thirdGoodsId,
				IsOnsale:     1,
			})
			if err != nil {
				err = errors.New("自动上架失败" + err.Error())
				return
			}
		}

	}

	return
}

// 云仓更新时不更新的字段
type UpdateCloudGoods struct {
	GoodsDes        string              `json:"goods_des" gorm:"-"`        //商品关键字
	CategoryId      uint                `json:"category_id" gorm:"-"`      //云仓商品分类id
	ProducingArea   string              `json:"producing_area" gorm:"-"`   //产地
	DeliverArea     string              `json:"deliver_area" gorm:"-"`     //发货地
	FreightId       uint                `json:"freight_id" gorm:"-"`       //运费模板id
	AftersaleTime   uint                `json:"aftersale_time" gorm:"-"`   //售后时长 可选 7，15，30 天
	DelayCompensate uint                `json:"delay_compensate" gorm:"-"` //发货延期 时长 可选24，48，72，0 小时
	Stags           string              `json:"stags" gorm:"-"`            //服务标签 从服务标签列表获取 id 英文逗号连接
	IsOn            int                 `json:"is_on" gorm:"-"`            //是否删除 0删除 1正常
	SpecsGroup      []model2.SpecsGroup `json:"specs_group" gorm:"-"`      //
	GoodsId         uint                `json:"goods_id" gorm:"-"`         //是否删除 0删除 1正常

}

/*
*

	更新所有待更新商品
*/
func CloudUpdateAllGoods(config common.SupplySetting, cloudPushProductIds request.CloudUpdateCloudGoods) (err error, res *common.APIResult) {
	err = source.DB().Model(&model2.CloudGoods{}).Where("gather_supplies_id = ?", cloudPushProductIds.GatherSuppliesId).Where("is_update = 1").Pluck("product_id", &cloudPushProductIds.ProductIds).Error
	if err != nil {
		log.Log().Error("不存在待更新的商品")
		err = errors.New("不存在待更新的商品")
		return
	}
	CloudUpdateGoods(config, cloudPushProductIds)
	return
}

// 更新云仓商品
func CloudUpdateGoods(config common.SupplySetting, cloudPushProductIds request.CloudUpdateCloudGoods) (err error, res *common.APIResult) {
	var batch = "cloudUpdate" + strconv.FormatInt(time.Now().Unix(), 10)
	for _, v := range cloudPushProductIds.ProductIds {
		//没有第三方id直接跳过
		if v == 0 {
			continue
		}
		var cloudGoods model2.CloudGoods
		err = source.DB().Where("product_id = ?", v).Where("gather_supplies_id = ?", cloudPushProductIds.GatherSuppliesId).First(&cloudGoods).Error
		if err != nil {
			err = errors.New("更新失败，没有查询到中台云仓商品记录")
			return
		}
		err, res = CloudUpdateGoodsRun(config, cloudGoods, v)
		if err != nil {
			//如果是算法错误重新走一次
			if strings.Contains(err.Error(), "算法错误") {
				log.Log().Error("云仓更新商品:算法错误重新更新一次!")
				err, res = CloudUpdateGoodsRun(config, cloudGoods, v)
			}
			if err != nil {
				var cloudPushGoodsMessage model2.CloudPushGoodsMessage
				cloudPushGoodsMessage.ProductIds = strconv.Itoa(int(v))
				cloudPushGoodsMessage.Status = 2
				cloudPushGoodsMessage.Batch = batch
				cloudPushGoodsMessage.GatherSuppliesId = cloudPushProductIds.GatherSuppliesId
				cloudPushGoodsMessage.ErrorMsg = err.Error()
				source.DB().Create(&cloudPushGoodsMessage)
			}
		}
	}

	return
}

// 执行更新
func CloudUpdateGoodsRun(config common.SupplySetting, cloudGoods model2.CloudGoods, productId uint) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.CloudGetGoods), "POST", nil, g.Map{"goods_id": cloudGoods.CloudProductId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		log.Log().Error("云仓更新商品:获取商品详情失败!", zap.Any("err", res), zap.Any("goods_id", cloudGoods.CloudProductId))
		err = errors.New(res.Message)
		return
	}

	var cloudGoodsDetail model2.CloudPushGoods
	CloudGoodsData := res.Data.(map[string]interface{})
	CloudGoodsData1, _ := json.Marshal(CloudGoodsData)
	_ = json.Unmarshal(CloudGoodsData1, &cloudGoodsDetail)

	if cloudGoodsDetail.IsOn != 1 {
		err = errors.New("云仓商品已删除(这里会删除本地记录),云仓商品id" + strconv.Itoa(int(cloudGoods.CloudProductId)))
		source.DB().Where("id = ?", cloudGoods.ID).Delete(&model2.CloudGoods{})
		return
	}

	var product Product
	err = source.DB().Preload("Brand").Preload("Skus").Where("id = ?", productId).First(&product).Error
	if err != nil {
		return
	}
	//云仓暂时不需要品牌
	//if product.Brand.ID != 0 {
	//	brandId = uint(product.Brand.ID)
	//	brandName = product.Brand.Name
	//}
	var submit = 0
	if product.IsDisplay == 1 {
		submit = 1
	}
	//if utf8.RuneCountInString(product.Title)>50 {
	//	product.Title = string([]rune(product.Title)[:50]) //保持商品名称50个字符
	//}
	//没有详情的时候赋值暂无详情
	if product.DetailImages == "" {
		product.DetailImages = "暂无详情"
	}
	err = verifySkus(product)
	if err != nil {
		return
	}
	//相同的SKU数量 如果全部相同走库存修改 start
	var skuNum = 0     //相同的SKU数量 如果全部相同走库存修改
	var updateType = 1 //1更新商品信息 2.更新库存
	if cloudGoodsDetail.GoodsName == product.Title {
		for _, item := range cloudGoodsDetail.SpecsGroup {
			for _, sku := range product.Skus {
				if item.OutOptionId == sku.ID {
					//如果商品是DWD的并且规格项为数字就更新该商品
					if product.Source == 103 && IsNumber(item.SpecValues[0]) == true {
						continue
					}
					//商品计算初始价格
					var itemProductPrice ProductPrice
					var Price, JsPrice, ScPrice uint
					itemProductPrice.Price = sku.Price
					itemProductPrice.CostPrice = sku.CostPrice
					itemProductPrice.OriginPrice = sku.OriginPrice
					itemProductPrice.GuidePrice = sku.GuidePrice
					Price = CloudGuidePrice(config, itemProductPrice)
					JsPrice = CloudJsPrice(config, itemProductPrice)
					ScPrice = CloudScPrice(config, itemProductPrice)
					//价格有任何一个不相等 都走更新商品不走更新库存
					if Price == item.Price && JsPrice == item.JsPrice && ScPrice == item.ScPrice {
						skuNum++
					}
				}
			}
		}
	}
	if len(cloudGoodsDetail.SpecsGroup) == skuNum {
		updateType = 2
	}

	specsGroup := PushCloudSpecsGroup(config, product)
	if updateType == 2 {
		//校验规格名称是否一致
		var oldSpecsGroup []model2.SpecsGroup
		for _, item := range cloudGoodsDetail.SpecsGroup {
			item.Id = 0
			oldSpecsGroup = append(oldSpecsGroup, item)
		}
		newSpecsGroupJson, _ := json.Marshal(specsGroup)
		oldSpecsGroupJson, _ := json.Marshal(oldSpecsGroup)
		//不一致则更新
		if string(newSpecsGroupJson) != string(oldSpecsGroupJson) {
			updateType = 1
		}
	}
	//判断商品详情是否一致
	if updateType == 2 && cloudGoodsDetail.Description != product.DetailImages {
		updateType = 1
	}
	//如果是更新库存, 云仓是下架 中台是上架则变为更新商品信息
	if updateType == 2 && cloudGoodsDetail.IsOnsale == 0 && product.IsDisplay == 1 {
		updateType = 1
	}
	//只更新库存
	if updateType == 2 {
		err = UpdateCloudGoodsStockStep3(config, cloudGoodsDetail, product)
		if err != nil {
			return
		}

		////更新记录表
		cloudGoods.IsUpdate = 0
		err = source.DB().Where("id = ?", cloudGoods.ID).Save(&cloudGoods).Error
		if err != nil {
			err = errors.New("修改CloudGoods表失败" + err.Error())
			return
		}
		return
	}

	//相同的SKU数量 如果全部相同走库存修改 end
	//return
	var cloudPushGoods model2.CloudPushGoods
	//商品计算初始价格
	var productPrice ProductPrice
	productPrice.Price = product.Skus[0].Price
	productPrice.CostPrice = product.Skus[0].CostPrice
	productPrice.OriginPrice = product.Skus[0].OriginPrice
	productPrice.GuidePrice = product.Skus[0].GuidePrice
	var brandId uint = 0
	var brandName = "暂无品牌"

	cloudPushGoods.GoodsName = product.Title
	cloudPushGoods.GoodsDes = cloudGoodsDetail.GoodsDes
	cloudPushGoods.CategoryId = cloudGoodsDetail.CategoryId
	cloudPushGoods.Price = CloudGuidePrice(config, productPrice)
	cloudPushGoods.JsPrice = CloudJsPrice(config, productPrice)
	cloudPushGoods.ScPrice = CloudScPrice(config, productPrice)
	cloudPushGoods.ProducingArea = cloudGoodsDetail.ProducingArea
	cloudPushGoods.DeliverArea = cloudGoodsDetail.DeliverArea
	cloudPushGoods.FreightId = cloudGoodsDetail.FreightId
	cloudPushGoods.AftersaleTime = cloudGoodsDetail.AftersaleTime
	cloudPushGoods.DelayCompensate = cloudGoodsDetail.DelayCompensate
	cloudPushGoods.GoodsBrand = brandName
	cloudPushGoods.BrandId = brandId
	cloudPushGoods.Unit = product.Unit
	cloudPushGoods.Stags = cloudGoodsDetail.Stags
	cloudPushGoods.OutGoodsId = product.ID
	cloudPushGoods.Description = product.DetailImages
	cloudPushGoods.Imgs = PushCloudIms(product)
	cloudPushGoods.Specs = PushCloudSpecs(product)
	cloudPushGoods.SpecsGroup = specsGroup

	requestCloudPushGoods := make(map[string]interface{})
	requestCloudPushGoods["goods_id"] = cloudGoods.CloudProductId
	requestCloudPushGoods["goods_name"] = cloudPushGoods.GoodsName
	requestCloudPushGoods["goods_des"] = cloudPushGoods.GoodsDes
	requestCloudPushGoods["category_id"] = cloudPushGoods.CategoryId
	requestCloudPushGoods["price"] = cloudPushGoods.Price
	requestCloudPushGoods["js_price"] = cloudPushGoods.JsPrice
	requestCloudPushGoods["sc_price"] = cloudPushGoods.ScPrice
	requestCloudPushGoods["producing_area"] = cloudPushGoods.ProducingArea
	requestCloudPushGoods["deliver_area"] = cloudPushGoods.DeliverArea
	requestCloudPushGoods["weight"] = cloudPushGoods.Weight
	requestCloudPushGoods["freight_id"] = cloudPushGoods.FreightId
	requestCloudPushGoods["aftersale_time"] = cloudPushGoods.AftersaleTime
	requestCloudPushGoods["delay_compensate"] = cloudPushGoods.DelayCompensate
	requestCloudPushGoods["goods_brand"] = cloudPushGoods.GoodsBrand
	requestCloudPushGoods["brand_id"] = cloudPushGoods.BrandId
	requestCloudPushGoods["stags"] = cloudPushGoods.Stags
	//requestCloudPushGoods["out_goods_id"] = cloudPushGoods.OutGoodsId
	requestCloudPushGoods["description"] = cloudPushGoods.Description
	requestCloudPushGoods["web_url"] = cloudPushGoods.WebUrl
	requestCloudPushGoods["imgs"] = cloudPushGoods.Imgs
	requestCloudPushGoods["specs"] = cloudPushGoods.Specs
	requestCloudPushGoods["specs_group"] = cloudPushGoods.SpecsGroup
	requestCloudPushGoods["unit"] = cloudPushGoods.Unit
	requestCloudPushGoods["submit"] = submit

	res, err = common.RequestApi(string(common.CloudUpdateGoods), "POST", nil, requestCloudPushGoods, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		log.Log().Error("云仓更新商品:修改商品失败!", zap.Any("err", res), zap.Any("goods_id", cloudGoods.CloudProductId), zap.Any("requestCloudPushGoods", requestCloudPushGoods))

		err = errors.New(res.Message + "云仓商品id" + strconv.Itoa(int(cloudGoods.CloudProductId)))
		return
	}

	//更新记录表
	cloudGoods.IsUpdate = 0
	cloudGoods.Title = cloudPushGoods.GoodsName
	//cloudGoods.Imgs = cloudPushGoods.Imgs
	err = source.DB().Where("id = ?", cloudGoods.ID).Save(&cloudGoods).Error
	if err != nil {
		err = errors.New("修改CloudGoods表失败" + err.Error())
		return
	}
	return
}
func IsNumber(str string) bool {

	_, err := strconv.Atoi(str)

	return err == nil

}

// 拼接规格价格内容
func PushCloudSpecsGroup(config common.SupplySetting, product Product) (cloudSpecs []model2.SpecsGroup) {
	cloudSpecs = make([]model2.SpecsGroup, len(product.Skus))
	for key, item := range product.Skus {
		//商品计算初始价格
		var productPrice ProductPrice
		productPrice.Price = item.Price
		productPrice.CostPrice = item.CostPrice
		productPrice.OriginPrice = item.OriginPrice
		productPrice.GuidePrice = item.GuidePrice

		cloudSpecs[key].Weight = item.Weight
		cloudSpecs[key].OutOptionId = item.ID
		cloudSpecs[key].Stock = item.Stock
		cloudSpecs[key].Price = CloudGuidePrice(config, productPrice)
		cloudSpecs[key].JsPrice = CloudJsPrice(config, productPrice)
		cloudSpecs[key].ScPrice = CloudScPrice(config, productPrice)
		for _, itemOptions := range item.Options {
			//cloudSpecs[key].SpecValue[itemOptionsKey] = itemOptions.SpecItemName
			if product.Source == 103 {
				cloudSpecs[key].SpecValues = append(cloudSpecs[key].SpecValues, item.Title)
			} else {
				cloudSpecs[key].SpecValues = append(cloudSpecs[key].SpecValues, itemOptions.SpecItemName)

			}
		}
	}
	return
}

// 拼接规格内容
func PushCloudSpecs(product Product) (cloudSpecs []model2.Specs) {
	//旧版
	//cloudSpecs =  make([]model2.Specs,len(product.Skus[0].Options))
	//var specValue model2.SpecValue
	//var isCunZai = 0
	//for _,item := range product.Skus{
	//	for itemOptionsKey, itemOptions := range item.Options{
	//		isCunZai = 0
	//		cloudSpecs[itemOptionsKey].SpecName = itemOptions.SpecName
	//		specValue.Value = itemOptions.SpecItemName
	//		for _,value := range cloudSpecs[itemOptionsKey].SpecValue {
	//			if value.Value == itemOptions.SpecItemName {
	//				isCunZai = 1
	//				break
	//			}
	//		}
	//		if isCunZai == 0 {
	//			cloudSpecs[itemOptionsKey].SpecValue = append(cloudSpecs[itemOptionsKey].SpecValue,specValue)
	//		}
	//
	//	}
	//}
	//新版
	var isAdd = 1 //是否需要添加 1是 0否
	//1.把所有不同的规格名称添加到cloudSpecs中
	for _, item := range product.Skus {
		for _, itemOptions := range item.Options {
			isAdd = 1
			var specs model2.Specs
			for _, itemCloudSpecs := range cloudSpecs {
				//如果存在相同的就跳过
				if itemCloudSpecs.SpecName == itemOptions.SpecName {
					isAdd = 0
					break
				}
			}
			if isAdd == 1 {
				specs.SpecName = itemOptions.SpecName
				cloudSpecs = append(cloudSpecs, specs)
			}
		}
	}
	var SpecItemNames []string
	//2.把所有属于这个规格的属性名称放置到相应的位置
	var specValue model2.SpecValue
	for key, itemCloudSpecs := range cloudSpecs {
		for _, item := range product.Skus {
			for _, itemOptions := range item.Options {
				if itemCloudSpecs.SpecName == itemOptions.SpecName {
					isAdd = 1 //是否需要添加 1是 0否
					for _, itemSpecValue := range SpecItemNames {
						if itemSpecValue == itemOptions.SpecItemName {
							isAdd = 0
							break
						}
					}
					if isAdd == 1 {
						//DWD商品规格项使用规格标题
						if product.Source == 103 {
							specValue.Value = item.Title
						} else {
							specValue.Value = itemOptions.SpecItemName
						}
						itemCloudSpecs.SpecValue = append(itemCloudSpecs.SpecValue, specValue)
						SpecItemNames = append(SpecItemNames, itemOptions.SpecItemName)

					}
				}

			}
		}
		cloudSpecs[key] = itemCloudSpecs
	}
	return
}

// 推送到云仓的图片处理
func PushCloudIms(product Product) (imgs []string) {
	var maxLen = 7 //最大提交到云仓的图片数量
	imgs = append(imgs, product.ImageUrl)
	for _, v := range product.Gallery {
		if v.Type == 1 {
			imgs = append(imgs, v.Src)
		}
		if len(imgs) >= maxLen {
			break
		}
	}

	return
}

type CloudGoodsStock struct {
	GoodsData GoodsData `json:"goods_data"` //商品相关信息
	SkuData   []SkuData `json:"sku_data"`   //商品规格信息
}
type GoodsData struct {
	SpuId      uint `json:"spu_id"`      //商品id
	GoodsStock uint `json:"goods_stock"` //库存
}
type SkuData struct {
	SKuId    uint   `json:"sku_id"`    //商品id
	SkuName  string `json:"sku_name"`  //商品id
	SkuStock int    `json:"sku_stock"` //库存
}

// 一键更新库存
func UpdateCloudGoodsStock(config common.SupplySetting, cloudCommon request.CloudCommon) (err error, total int64) {
	err = source.DB().Where("gather_supplies_id = ?", cloudCommon.GatherSuppliesId).Model(&model2.CloudGoods{}).Where("gather_supplies_id = ?", cloudCommon.GatherSuppliesId).Count(&total).Error
	if err != nil {
		return
	}
	if total == 0 {
		err = errors.New("暂无需要更新库存的商品")
		return
	}
	go UpdateCloudGoodsStockStep1(config, cloudCommon, total)
	return
}
func UpdateCloudGoodsStockStep1(config common.SupplySetting, cloudCommon request.CloudCommon, total int64) {
	var pageSize = 100
	var loop = int(math.Ceil(float64(total) / float64(pageSize))) //循环次数
	//log.Log().Info("updataCloudGoodsStock:循环次数", zap.Any("loop", loop))

	var wg sync.WaitGroup
	for i := 1; i <= loop; i++ {
		wg.Add(1)
		var cloudGoods []model2.CloudGoods
		offset := pageSize * (i - 1)
		err := source.DB().Limit(pageSize).Offset(offset).Where("gather_supplies_id = ?", cloudCommon.GatherSuppliesId).Find(&cloudGoods).Error
		if err != nil {
			return
		}
		//开始更新库存
		go func(wg *sync.WaitGroup, i int, config common.SupplySetting, cloudCommon request.CloudCommon, cloudGoods []model2.CloudGoods) {
			//log.Log().Info("updataCloudGoodsStockstep2执行次数", zap.Any("i", i))
			defer wg.Done()
			for _, item := range cloudGoods {
				err = UpdateCloudGoodsStockStep2(config, item)
				if err != nil {
					log.Log().Info("updataCloudGoodsStock", zap.Any("err", err))
					return
				}
			}
		}(&wg, i, config, cloudCommon, cloudGoods)
	}

	return
}

// 更新云仓商品库存
func UpdateCloudGoodsStockStep2(config common.SupplySetting, cloudGood model2.CloudGoods) (err error) {
	var res *common.APIResult
	res, err = common.RequestApi(string(common.CloudGetGoods), "POST", nil, g.Map{"goods_id": cloudGood.CloudProductId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	var cloudGoodsDetail model2.CloudPushGoods
	CloudGoodsData := res.Data.(map[string]interface{})
	CloudGoodsData1, _ := json.Marshal(CloudGoodsData)
	_ = json.Unmarshal(CloudGoodsData1, &cloudGoodsDetail)

	var product Product
	err = source.DB().Preload("Skus").Where("id = ?", cloudGood.ProductId).First(&product).Error
	if err != nil {
		return
	}
	err = UpdateCloudGoodsStockStep3(config, cloudGoodsDetail, product)
	////拼接库存请求参数
	//var cloudGoodsStock CloudGoodsStock
	//cloudGoodsStock.GoodsData.GoodsStock = product.Stock
	//cloudGoodsStock.GoodsData.SpuId = cloudGood.CloudProductId
	//
	//for _, item := range cloudGoodsDetail.SpecsGroup {
	//	var skuData SkuData
	//	for _, itemProduct := range product.Skus {
	//		if item.OutOptionId == itemProduct.ID {
	//			skuData.SKuId = item.Id
	//			skuData.SkuName = strings.Replace(itemProduct.Title, "+", "+_", -1)
	//			skuData.SkuStock = itemProduct.Stock
	//			break
	//		}
	//	}
	//	if skuData.SKuId != 0 {
	//		cloudGoodsStock.SkuData = append(cloudGoodsStock.SkuData, skuData)
	//	}
	//}
	//res = common.RequestApi(string(common.CloudUpdateGoodsStock), "POST", nil, g.Map{"goods_data": cloudGoodsStock.GoodsData, "sku_data": cloudGoodsStock.SkuData}, config)
	//if res.Code != 1 {
	//	err = errors.New(res.Message)
	//	return
	//}
	return
}

// 更新云仓商品库存
func UpdateCloudGoodsStockStep3(config common.SupplySetting, cloudGoodsDetail model2.CloudPushGoods, product Product) (err error) {
	var res *common.APIResult

	//拼接库存请求参数
	var cloudGoodsStock CloudGoodsStock
	cloudGoodsStock.GoodsData.GoodsStock = product.Stock
	cloudGoodsStock.GoodsData.SpuId = cloudGoodsDetail.GoodsId

	for _, item := range cloudGoodsDetail.SpecsGroup {
		var skuData SkuData
		for _, itemProduct := range product.Skus {
			//id相等，库存不一致才去修改库存
			if item.OutOptionId == itemProduct.ID && ((item.Stock != itemProduct.Stock && itemProduct.Stock < 50) || (item.Stock == 0 && itemProduct.Stock > 0)) {
				skuData.SKuId = item.Id
				var SkuName = ""
				for _, specValue := range item.SpecValues {
					if SkuName == "" {
						SkuName = specValue
					} else {
						SkuName += "+_" + specValue
					}
				}
				//skuData.SkuName = strings.Replace(itemProduct.Title, "+", "+_", -1)
				skuData.SkuName = SkuName
				skuData.SkuStock = itemProduct.Stock
				break
			}
		}
		if skuData.SKuId != 0 {
			cloudGoodsStock.SkuData = append(cloudGoodsStock.SkuData, skuData)
		}
	}
	//没有不同的库存的规格无需修改
	if len(cloudGoodsStock.SkuData) == 0 {
		return
	}
	res, err = common.RequestApi(string(common.CloudUpdateGoodsStock), "POST", nil, g.Map{"goods_data": cloudGoodsStock.GoodsData, "sku_data": cloudGoodsStock.SkuData}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		log.Log().Error("云仓更新库存失败!", zap.Any("cloudGoodsStock", cloudGoodsStock), zap.Any("res", res))
		if res.Message == "不存在" {
			return nil
		}
		err = errors.New(res.Message)
		return
	}
	return nil
}

// ProductMessageType
// 监听商品删除修改事件
func ListenerCloudGoods(productId uint, messageType mq.ProductMessageType) (err error) {
	var cloudGoods model2.CloudGoods
	source.DB().Where("product_id", productId).First(&cloudGoods)
	if cloudGoods.ID == 0 {
		return nil
	}
	if messageType == mq.Delete {
		//调整为所有属于这个中台商品id的 都改变状态
		source.DB().Model(&model2.CloudGoods{}).Where("product_id", productId).UpdateColumns(model2.CloudGoods{
			IsDelete: 1,
		})
		//并且执行下架
		err = ListenerCloudGoodsAutoUndercarriage(cloudGoods, productId, 0)

	}
	if messageType == mq.Edit {
		//调整为所有属于这个中台商品id的 都改变状态
		err = source.DB().Model(&model2.CloudGoods{}).Where("product_id", productId).UpdateColumns(model2.CloudGoods{
			IsUpdate: 1,
		}).Error
		var product model.Product
		source.DB().Where("id = ?", productId).First(&product)
		//如果商品是下架 就去执行云仓下架
		if product.ID != 0 && product.IsDisplay == 0 {
			err = ListenerCloudGoodsAutoUndercarriage(cloudGoods, productId, 0)
		}
		//自动更新商品开始
		var cloudUpdateCloudGoods request.CloudUpdateCloudGoods
		cloudUpdateCloudGoods.GatherSuppliesId = cloudGoods.GatherSuppliesId
		cloudUpdateCloudGoods.ProductIds = append(cloudUpdateCloudGoods.ProductIds, cloudGoods.ProductId)
		var config common.SupplySetting
		err, config = common.InitCloudSetting(cloudGoods.GatherSuppliesId)

		if err != nil {
			log.Log().Error("获取设置失败!", zap.Any("err", err))
			return
		}
		//log.Log().Debug("云仓接收商品变更消息，更新商品!", zap.Any("cloudUpdateCloudGoods", cloudUpdateCloudGoods))
		CloudUpdateGoods(config, cloudUpdateCloudGoods) //更新商品
		//自动更新商品结束
	}
	//如果是下架执行下架
	if messageType == mq.Undercarriage {
		err = ListenerCloudGoodsAutoUndercarriage(cloudGoods, productId, 0)
	}
	//如果是上架执行上架
	if messageType == mq.OnSale {
		err = ListenerCloudGoodsAutoUndercarriage(cloudGoods, productId, 1)
	}
	return
}

// 下架云仓商品
func ListenerCloudGoodsAutoUndercarriage(cloudGoods model2.CloudGoods, productId uint, isOnsale int) (err error) {
	var config common.SupplySetting
	err, config = common.InitCloudSetting(cloudGoods.GatherSuppliesId)
	if err != nil {
		log.Log().Error("云仓自动下架失败,配置错误", zap.Any("err", err.Error()))
		return
	}
	if config.Cloud.CloudIsAutoUndercarriage == 1 {
		//log.Log().Info("云仓自动下架开始", zap.Any("productId", productId))
		var cloudGoodss []model2.CloudGoods
		source.DB().Where("product_id", productId).Find(&cloudGoodss)
		var goodsOnsale request.GoodsOnsale
		goodsOnsale.IsOnsale = isOnsale
		for _, item := range cloudGoodss {
			goodsOnsale.ThirdGoodsId = append(goodsOnsale.ThirdGoodsId, int(item.CloudProductId))
		}
		//log.Log().Info("云仓自动下架开始执行参数", zap.Any("goodsOnsale", goodsOnsale), zap.Any("productIds", goodsOnsale.ThirdGoodsId))
		err = UpdateGoodsOnsale(config, goodsOnsale)
		if err != nil {
			log.Log().Error("云仓自动下架失败", zap.Any("err", err.Error()))
		} else {
			//log.Log().Info("云仓自动下架成功", zap.Any("productId", goodsOnsale.ThirdGoodsId))
		}

	}
	return
}

type GatherSupply struct {
	source.Model
	Name       string `json:"name" gorm:"column:name"`
	Key        string `json:"key" gorm:"column:key"`
	CategoryID uint   `json:"category_id"`
}

// 获取供应链 去掉胜天半子
func GetGatherSupplies() (err error, gatherSupplys []GatherSupply) {
	err = source.DB().Where("category_id not in ?", common.ExcludeGatherSupplysCategoryIds()).Find(&gatherSupplys).Error
	return
}

// 云仓商品详情
func CloudGetGoods(config common.SupplySetting, searchData request.GetCloudGoodsDetail) (err error, cloudGoodsDetail model2.CloudPushGoods) {
	var res *common.APIResult
	res, err = common.RequestApi(string(common.CloudGetGoods), "POST", nil, g.Map{"goods_id": searchData.GoodsId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}

	CloudGoodsData := res.Data.(map[string]interface{})
	CloudGoodsData1, _ := json.Marshal(CloudGoodsData)
	_ = json.Unmarshal(CloudGoodsData1, &cloudGoodsDetail)

	return
}

// 云仓物流详情
func CloudGetDeliver(config common.SupplySetting, searchData request.CloudGetDeliver) (err error, res *common.APIResult) {
	res, err = common.RequestApi(string(common.CloudGetDeliver), "POST", nil, g.Map{"goods_order_sn": searchData.GoodsOrderSn}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	//
	//CloudGoodsData := res.Data.(map[string]interface{})
	//CloudGoodsData1,_ := json.Marshal(CloudGoodsData)
	//_ = json.Unmarshal(CloudGoodsData1,&cloudGoodsDetail)

	return
}

// 云仓指导价计算
func CloudGuidePrice(config common.SupplySetting, productPrice ProductPrice) (guidePrice uint) {
	switch config.Cloud.CloudGuide {
	case 0: //市场价 origin_price
		if config.Cloud.CloudMarketingMarketingCoefficient == "" {
			config.Cloud.CloudMarketingMarketingCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudMarketingMarketingCoefficient, 10, 32)
		guidePrice = productPrice.OriginPrice * uint(intX) / 100
		break
	case 1: //现价 price 供货价
		if config.Cloud.CloudPriceCoefficient == "" {
			config.Cloud.CloudPriceCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudPriceCoefficient, 10, 32)
		guidePrice = productPrice.Price * uint(intX) / 100
		break
	case 2: //成本价 cost_price
		if config.Cloud.CloudCostCoefficient == "" {
			config.Cloud.CloudCostCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudCostCoefficient, 10, 32)
		guidePrice = productPrice.CostPrice * uint(intX) / 100
		break
	case 3: //指导价 cost_price
		if config.Cloud.CloudCostCoefficient == "" {
			config.Cloud.CloudCostCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudGuideGuideCoefficient, 10, 32)
		guidePrice = productPrice.GuidePrice * uint(intX) / 100
		break
	default: //默认市场价
		if config.Cloud.CloudMarketingMarketingCoefficient == "" {
			config.Cloud.CloudMarketingMarketingCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudMarketingMarketingCoefficient, 10, 32)
		guidePrice = productPrice.OriginPrice * uint(intX) / 100
		break
	}
	return
}

// 云仓协议价计算
func CloudJsPrice(config common.SupplySetting, productPrice ProductPrice) (jsPrice uint) {
	switch config.Cloud.CloudSettlement {
	case 0: //市场价 origin_price
		if config.Cloud.CloudSettlementMarketingCoefficient == "" {
			config.Cloud.CloudSettlementMarketingCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudSettlementMarketingCoefficient, 10, 32)
		jsPrice = productPrice.OriginPrice * uint(intX) / 100
		break
	case 1: //现价 price 供货价
		if config.Cloud.CloudSettlementPriceCoefficient == "" {
			config.Cloud.CloudSettlementPriceCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudSettlementPriceCoefficient, 10, 32)
		jsPrice = productPrice.Price * uint(intX) / 100
		break
	case 2: //成本价 cost_price
		if config.Cloud.CloudSettlementCostCoefficient == "" {
			config.Cloud.CloudSettlementCostCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudSettlementCostCoefficient, 10, 32)
		jsPrice = productPrice.CostPrice * uint(intX) / 100
		break
	default: //默认市场价
		if config.Cloud.CloudSettlementMarketingCoefficient == "" {
			config.Cloud.CloudSettlementMarketingCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudSettlementMarketingCoefficient, 10, 32)
		jsPrice = productPrice.OriginPrice * uint(intX) / 100
		break
	}
	return
}

// 云仓市场价计算
func CloudScPrice(config common.SupplySetting, productPrice ProductPrice) (scPrice uint) {
	switch config.Cloud.CloudMarketing {
	case 0: //市场价 origin_price
		if config.Cloud.CloudMarketingMarketingCoefficient == "" {
			config.Cloud.CloudMarketingMarketingCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudMarketingMarketingCoefficient, 10, 32)
		scPrice = productPrice.OriginPrice * uint(intX) / 100
		break
	case 1: //现价 price 供货价
		if config.Cloud.CloudPriceCoefficient == "" {
			config.Cloud.CloudPriceCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudPriceCoefficient, 10, 32)
		scPrice = productPrice.Price * uint(intX) / 100
		break
	case 2: //成本价 cost_price
		if config.Cloud.CloudCostCoefficient == "" {
			config.Cloud.CloudCostCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudCostCoefficient, 10, 32)
		scPrice = productPrice.CostPrice * uint(intX) / 100
		break
	default: //默认市场价
		if config.Cloud.CloudMarketingMarketingCoefficient == "" {
			config.Cloud.CloudMarketingMarketingCoefficient = "100"
		}
		intX, _ := strconv.ParseUint(config.Cloud.CloudMarketingMarketingCoefficient, 10, 32)
		scPrice = productPrice.OriginPrice * uint(intX) / 100
		break
	}
	return
}
func GetCloudPushGoodsMessageList(info request.CloudPushGoodsMessage) (err error, list []model2.CloudPushGoodsMessage, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model2.CloudPushGoodsMessage{})
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Batch != "" {
		db.Where("batch", info.Batch)
	}
	if info.GatherSuppliesId != 0 {
		db.Where("gather_supplies_id", info.GatherSuppliesId)
	}
	if info.Status != nil {
		db.Where("status", info.Status)
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&list).Error
	return err, list, total
}

// 云仓订单详情返回数据
type CloudOrderDetail struct {
	Order            common.CloudOrderData `json:"order" form:"order"`                           //分页
	OrderGoodsDetail []OrderGoodsDetail    `json:"order_goods_detail" form:"order_goods_detail"` //分页
}
type OrderGoodsDetail struct {
	Id                 int                   `json:"id" form:"id"`                                     //分页
	GoodsOrderSn       string                `json:"goods_order_sn" form:"goods_order_sn"`             //子订单号
	OrderId            int                   `json:"order_id" form:"order_id"`                         //父订单id
	GoodsId            int                   `json:"goods_id" form:"goods_id"`                         //商品Id
	Total              uint                  `json:"total" form:"total"`                               //商品数量
	GoodsOptionId      uint                  `json:"goods_option_id" form:"goods_option_id"`           //sku
	Title              string                `json:"title" form:"title"`                               //商品名称
	GoodsOptionTitle   string                `json:"goods_option_title" form:"goods_option_title"`     //商品规格名称
	GoodsPrice         int                   `json:"goods_price" form:"goods_price"`                   //商品金额 单位为分
	GoodsStatus        int                   `json:"goods_status" form:"goods_status"`                 //发货状态 0:未发货,1:已发货,2:已收货,3:配货中,4：拒收
	PayStatus          int                   `json:"pay_status" form:"pay_status"`                     //订单状态 0:未付款,1:已付款,2申请退款,3,退款中,4已退款5退款申请失败
	CreatedTime        int                   `json:"created_time" form:"created_time"`                 //创建时间
	ExpressCompanyName string                `json:"express_company_name" form:"express_company_name"` //物流公司
	ExpressSn          string                `json:"express_sn" form:"express_sn"`                     //物流单号
	ExpressCode        string                `json:"express_code" form:"express_code"`                 //物流公司代码
	CloudGoods         CloudGoods            `json:"cloud_goods" form:"cloud_goods"`                   //
	CloudOrderItem     model2.CloudOrderItem `json:"cloud_order_item" form:"cloud_order_item"`         //

}

// 导出云仓订单 GetCloudOrderList
func ExportCloudOrder(config common.SupplySetting, searchData request.CloudOrderSearch) (err error, link string) {
	searchData.Page = 1
	searchData.PageSize = 100

	var body = make(map[string]interface{})
	body["page"] = searchData.Page
	body["limit"] = searchData.PageSize
	if searchData.OrderSn != "" {
		body["order_sn"] = searchData.OrderSn
	}
	if searchData.Status != nil {
		body["status"] = searchData.Status
	}
	if searchData.Mobile != 0 {
		body["mobile"] = searchData.Mobile
	}
	if searchData.RealName != "" {
		body["real_name"] = searchData.RealName
	}
	if searchData.CreatedStartTime != 0 {
		body["created_start_time"] = searchData.CreatedStartTime
	}
	if searchData.CreatedEndTime != 0 {
		body["created_end_time"] = searchData.CreatedEndTime
	}
	res, err := common.RequestApi(string(common.GetCloudOrderList), "POST", nil, body, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}

	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	if res.Data == "" {
		err = errors.New("暂无订单")
		return
	}

	datas := res.Data.(map[string]interface{})
	var cloudOrderList common.CloudOrderList
	json1, _ := json.Marshal(datas)
	_ = json.Unmarshal(json1, &cloudOrderList)

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "订单id")
	f.SetCellValue("Sheet1", "B1", "订单编号")
	f.SetCellValue("Sheet1", "C1", "订单运费")
	f.SetCellValue("Sheet1", "D1", "第三方订单号")
	f.SetCellValue("Sheet1", "E1", "订单总额")
	f.SetCellValue("Sheet1", "F1", "收货人姓名")
	f.SetCellValue("Sheet1", "G1", "联系电话")
	f.SetCellValue("Sheet1", "H1", "省")
	f.SetCellValue("Sheet1", "I1", "市")
	f.SetCellValue("Sheet1", "J1", "区")
	f.SetCellValue("Sheet1", "K1", "街道")
	f.SetCellValue("Sheet1", "L1", "详细地址")
	f.SetCellValue("Sheet1", "M1", "服务费")
	f.SetCellValue("Sheet1", "N1", "子订单号")
	f.SetCellValue("Sheet1", "O1", "云仓商品id")
	f.SetCellValue("Sheet1", "P1", "子订单商品数量")
	f.SetCellValue("Sheet1", "Q1", "子订单商品金额")
	f.SetCellValue("Sheet1", "R1", "子订单云仓商品规格id")
	f.SetCellValue("Sheet1", "S1", "商品名称")
	f.SetCellValue("Sheet1", "U1", "商品规格名称")
	f.SetCellValue("Sheet1", "V1", "子订单发货状态")
	f.SetCellValue("Sheet1", "W1", "子订单状态")
	f.SetCellValue("Sheet1", "X1", "订单备注")
	f.SetCellValue("Sheet1", "Y1", "商家备注")
	f.SetCellValue("Sheet1", "Z1", "下单时间")
	f.SetCellValue("Sheet1", "AA1", "取消时间")
	f.SetCellValue("Sheet1", "AB1", "完成时间")
	f.SetCellValue("Sheet1", "AC1", "发货时间")
	//f.SetCellValue("Sheet1", "AD1", "中台商品id")
	//f.SetCellValue("Sheet1", "AE1", "中台规格id")
	i := 2
	//var getCloudGoodsDetail request.GetCloudGoodsDetail
	//getCloudGoodsDetail.GatherSuppliesId = searchData.GatherSuppliesId
	for _, v := range cloudOrderList.List {
		for _, item := range v.OrderGoods {
			var sentAt string
			if v.Order.SendTime != 0 {
				tm := time.Unix(int64(v.Order.SendTime), 0)
				sentAt = tm.Format("2006-01-02 15:04:05")
			}

			var createAt string
			if v.Order.CreatedTime != 0 {
				tm := time.Unix(int64(v.Order.CreatedTime), 0)
				createAt = tm.Format("2006-01-02 15:04:05")
			}

			var finishTime string
			if v.Order.FinishTime != 0 {
				tm := time.Unix(int64(v.Order.FinishTime), 0)
				finishTime = tm.Format("2006-01-02 15:04:05")
			}
			var cancelTime string
			if v.Order.CancelTime != 0 {
				tm := time.Unix(int64(v.Order.CancelTime), 0)
				finishTime = tm.Format("2006-01-02 15:04:05")
			}
			//发货状态 0:未发货,1:已发货,2:已收货,3:配货中,4：拒收
			var goodsStatus = "未知状态"
			switch item.GoodsStatus {
			case 0:
				goodsStatus = "未发货"
				break
			case 1:
				goodsStatus = "已发货"
				break
			case 2:
				goodsStatus = "已收货"
				break
			case 3:
				goodsStatus = "配货中"
				break
			case 4:
				goodsStatus = "拒收"
				break
			default:
				goodsStatus = goodsStatus + ":" + strconv.Itoa(item.GoodsStatus)
				break
			}
			//订单状态 0:未付款,1:已付款,2申请退款,3,退款中,4已退款5退款申请失败
			var payStatus = "未知状态"
			switch item.PayStatus {
			case 0:
				payStatus = "未付款"
				break
			case 1:
				payStatus = "已付款"
				break
			case 2:
				payStatus = "申请退款"
				break
			case 3:
				payStatus = "退款中"
				break
			case 4:
				payStatus = "已退款"
				break
			case 5:
				payStatus = "退款申请失败"
				break
			default:
				payStatus = payStatus + ":" + strconv.Itoa(item.PayStatus)
				break
			}
			//增加请求第三方商品详情请求100条订单的时候 会超时 暂时去掉
			//getCloudGoodsDetail.GoodsId = item.GoodsId
			//err,cloudGoodsDetail := CloudGetGoods(config,getCloudGoodsDetail)
			//var productId uint = 0
			//var skuId uint = 0
			//if err != nil {
			//	productId = cloudGoodsDetail.OutGoodsId
			//
			//	for _,cloudGoodsDetailItem := range cloudGoodsDetail.SpecsGroup{
			//
			//		if cloudGoodsDetailItem.Id == item.GoodsOptionId {
			//			skuId = cloudGoodsDetailItem.OutOptionId
			//		}
			//
			//	}
			//}
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.Order.Id)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.Order.OrderSn)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), float64(v.Order.DispatchPrice)/100)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.Order.ThirdOrdersn)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), float64(v.Order.OrderTotalPrice)/100)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.Order.RealName)
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.Order.Mobile)
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.Order.Provice)
			f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), v.Order.City)
			f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), v.Order.District)
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), v.Order.Street)
			f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), v.Order.Address)
			f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.Order.ServiceFee)
			f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), item.GoodsOrderSn)
			f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), item.GoodsId)
			f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), item.Total)
			f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), float64(item.GoodsPrice)/100)
			f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), item.GoodsOptionId)
			f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), item.Title)
			f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), item.GoodsOptionTitle)
			f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), goodsStatus)
			f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), payStatus)
			f.SetCellValue("Sheet1", "X"+strconv.Itoa(i), v.Order.Remark)
			f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i), v.Order.SellerRemark)
			f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i), createAt)
			f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i), cancelTime)
			f.SetCellValue("Sheet1", "AB"+strconv.Itoa(i), finishTime)
			f.SetCellValue("Sheet1", "AC"+strconv.Itoa(i), sentAt)
			//f.SetCellValue("Sheet1", "AD"+strconv.Itoa(i), productId)
			//f.SetCellValue("Sheet1", "AE"+strconv.Itoa(i), skuId)
			i++
		}
	}

	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	times := time.Now().Format("20060102150405")

	path := YzGoConfig.Config().Local.Path + "/export_cloud_order"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + times + "云仓订单导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}
	return err, link
}

// 下单参数
type ConfirmOrder struct {
	OrderSn string     `json:"order_sn"`
	Sku     []CloudSku `json:"sku"`
	Address Address    `json:"address"`
}

type Address struct {
	Consignee   string `json:"consignee"`
	Phone       string `json:"phone"`
	Province    string `json:"province"`
	City        string `json:"city"`
	Area        string `json:"area"`
	Street      string `json:"street"`
	ProvinceId  int    `json:"province_id"`
	CityId      int    `json:"city_id"`
	AreaId      int    `json:"area_id"`
	StreetId    int    `json:"street_id"`
	Description string `json:"description"`
}

type CloudSku struct {
	SkuId              uint   `json:"skuId"`
	Number             uint   `json:"number"`
	ExpressCompanyName string `json:"express_company_name" form:"express_company_name"` //物流公司
	ExpressSn          string `json:"express_sn" form:"express_sn"`                     //物流单号
	ExpressCode        string `json:"express_code" form:"express_code"`                 //物流公司代码
	model2.CloudOrderItem
}

/*
*

	处理因系统错误下单失败的订单，加上标识 之后重新下单 -- 舍弃
*/
func CloudOrderCreateSystemError() {
	return

	var cloudOrders []model2.CloudOrder
	err := source.DB().Where("status = 0").Where("order_id = 0").Find(&cloudOrders).Error
	if err != nil {
		log.Log().Error("处理因系统错误下单失败的订单:暂无任何系统原因出错的订单")
	}
	for _, item := range cloudOrders {
		var orderDatas []model4.Order
		source.DB().Where("third_order_sn = ?", item.CloudOrderSn).Find(&orderDatas)
		//判断出错时是否已经下单了 如果下单了 是待付款直接取消，其他状态把订单id与订单号记录到云仓下单表并且标记
		for _, orderData := range orderDatas {
			if orderData.ID != 0 {
				if orderData.Status == 0 {
					orderData.Note = "系统原因出错,重新标记云仓订单号,等待自动下单重新下单"
					orderData.ThirdOrderSN += "_system_error"
					source.DB().Where("id = ?", orderData.ID).Updates(&orderData)
					_ = order.Close(orderData.ID)
				} else {
					item.OrderId = strconv.Itoa(int(orderData.ID))
					item.OrderSn = strconv.Itoa(int(orderData.OrderSN))
					item.Status = 1
					item.ErrorMsg = "系统原因出错,但是订单已支付,发货时需要自行发货"
					source.DB().Where("id = ?", item.ID).Updates(&item)
					continue
				}
			}
		}

		//标记 改变云仓订单号 让自动下单可以重新进行下单
		item.CloudOrderSn += "_system_error"
		item.Status = -3
		item.CloudOrderId = 0
		item.ErrorMsg = "系统原因出错,重新标记云仓订单号,等待自动下单重新下单"
		source.DB().Where("id = ?", item.ID).Save(&item)
	}
}

// (云仓商城：同步中台与云仓商品下架状态)
func CronCloudProductSoldOutStep1(config common.SupplySetting, page int, GatherSuppliesId uint) {

	var limit = 15
	var total int64
	offset := limit * (page - 1)
	time.Sleep(2 * time.Second)
	var goodsOnsale request.GoodsOnsale
	joinWhere := "INNER join products on products.id = cloud_goods.product_id and (products.is_display = 0 or products.deleted_at is not null)"
	db := source.DB().Model(&model2.CloudGoods{}).Where("gather_supplies_id = ?", GatherSuppliesId).Joins(joinWhere)
	err := db.Count(&total).Error
	if err != nil {
		log.Log().Error("获取总数失败!", zap.Any("err", err))
		return
	}
	err = db.Limit(limit).Offset(offset).Pluck("cloud_product_id", &goodsOnsale.ThirdGoodsId).Error
	if err != nil {
		log.Log().Error("没有商品!", zap.Any("err", err))
		return
	}
	if len(goodsOnsale.ThirdGoodsId) == 0 {
		log.Log().Error("没有需要下架的商品!", zap.Any("err", err))
		return
	}
	goodsOnsale.IsOnsale = 0
	goodsOnsale.GatherSuppliesId = GatherSuppliesId
	log.Log().Error("云仓下架的商品!", zap.Any("offset", total/int64(limit)), zap.Any("page", page), zap.Any("goodsOnsale", goodsOnsale), zap.Any("page", page))

	err = UpdateGoodsOnsale(config, goodsOnsale)
	if err != nil {
		log.Log().Error("下架失败", zap.Any("err", err))
		return
	}

	if page < int(total/int64(limit)) {
		page++
		CronCloudProductSoldOutStep1(config, page, GatherSuppliesId)
	}
}

// (云仓商城：同步中台与云仓商品下架状态) -- 中台删除product记录也没有发消息导致云仓没有下架 同步这部分下架状态
func CloudProductDeleteSoldOutStep1(config common.SupplySetting, page int, GatherSuppliesId uint) {

	var limit = 15
	var total int64
	offset := limit * (page - 1)
	time.Sleep(2 * time.Second)
	var goodsOnsale request.GoodsOnsale
	db := source.DB().Where(" NOT EXISTS (SELECT t2.id FROM    products t2   WHERE    cloud_goods.product_id = t2.id  )").Model(&model2.CloudGoods{}).Where("gather_supplies_id = ?", GatherSuppliesId).Where("cloud_goods.deleted_at is null").Where("cloud_goods.is_delete = 0")
	err := db.Count(&total).Error
	if err != nil {
		log.Log().Error("获取总数失败!", zap.Any("err", err))
		return
	}
	err = db.Limit(limit).Offset(offset).Pluck("cloud_product_id", &goodsOnsale.ThirdGoodsId).Error
	if err != nil {
		log.Log().Error("没有商品!", zap.Any("err", err))
		return
	}
	if len(goodsOnsale.ThirdGoodsId) == 0 {
		log.Log().Error("没有需要下架的商品!", zap.Any("err", err))
		return
	}
	goodsOnsale.IsOnsale = 0
	goodsOnsale.GatherSuppliesId = GatherSuppliesId
	log.Log().Error("云仓下架的商品!", zap.Any("offset", total/int64(limit)), zap.Any("page", page), zap.Any("goodsOnsale", goodsOnsale), zap.Any("page", page))

	err = UpdateGoodsOnsale(config, goodsOnsale)
	if err != nil {
		log.Log().Error("下架失败", zap.Any("err", err))
		return
	}

	if page < int(total/int64(limit)) {
		page++
		CloudProductDeleteSoldOutStep1(config, page, GatherSuppliesId)
	}
}

// (云仓商城：同步中台与云仓商品上架状态) -- 因意外导致 中台上架云仓没有上架的商品
func CloudProductOnStep1(config common.SupplySetting, page int, GatherSuppliesId uint, productSource int) {

	var limit = 15
	var total int64
	offset := limit * (page - 1)
	time.Sleep(2 * time.Second)
	var goodsOnsale request.GoodsOnsale
	db := source.DB().Model(&model2.CloudGoods{}).Joins("INNER join products on products.id = cloud_goods.product_id and products.is_display = 1").Where("cloud_goods.product_id != 0 and cloud_goods.cloud_product_id != 0 and cloud_goods.deleted_at is null and products.deleted_at is null").Where("products.source = ?", productSource)
	err := db.Count(&total).Error
	if err != nil {
		log.Log().Error("获取总数失败!", zap.Any("err", err))
		return
	}
	err = db.Limit(limit).Offset(offset).Pluck("cloud_product_id", &goodsOnsale.ThirdGoodsId).Error
	if err != nil {
		log.Log().Error("没有商品!", zap.Any("err", err))
		return
	}
	if len(goodsOnsale.ThirdGoodsId) == 0 {
		log.Log().Error("没有需要下架的商品!", zap.Any("err", err))
		return
	}
	goodsOnsale.IsOnsale = 1
	goodsOnsale.GatherSuppliesId = GatherSuppliesId
	log.Log().Error("云仓上架的商品!", zap.Any("offset", total/int64(limit)), zap.Any("page", page), zap.Any("goodsOnsale", goodsOnsale), zap.Any("page", page))

	err = UpdateGoodsOnsale(config, goodsOnsale)
	if err != nil {
		log.Log().Error("上架失败", zap.Any("err", err))
		return
	}

	if page < int(total/int64(limit)) {
		page++
		CloudProductOnStep1(config, page, GatherSuppliesId, productSource)
	}
}

// (云仓订单：自动下单步骤1  获取订单列表)
func CloudOrderCreateStep1(config common.SupplySetting, page int, GatherSuppliesId uint) {
	var body = make(map[string]interface{})
	var limit = 100
	body["page"] = page
	body["limit"] = limit
	t := time.Now()
	tm2 := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	var hour = t.Hour()
	if hour == 0 || hour == 24 {
		tm2 = time.Date(t.Year(), t.Month(), t.Day()-1, 0, 0, 0, 0, t.Location())
	}

	body["created_start_time"] = tm2.Unix() - 1 //今天开始的时间
	body["created_end_time"] = t.Unix()         //当前时间
	//body["order_sn"] = "SN20220628d573452afe7b_1"
	//log.Log().Error("获取云仓订单列表条件!", zap.Any("where", body))
	res, err := common.RequestApi(string(common.GetCloudOrderList), "POST", nil, body, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		log.Log().Error("获取云仓订单列表失败!", zap.Any("err", res.Message))
		return
	}
	if res.Data == "" {
		log.Log().Error("暂时云仓订单!", zap.Any("err", "暂无订单"))
		return
	}

	datas := res.Data.(map[string]interface{})
	var cloudOrderList common.CloudOrderList
	json1, _ := json.Marshal(datas)
	_ = json.Unmarshal(json1, &cloudOrderList)

	CloudOrderCreateStep2(config, cloudOrderList, GatherSuppliesId)

	if cloudOrderList.Count > page*limit {
		page++
		CloudOrderCreateStep1(config, page, GatherSuppliesId)
	}
}

// (云仓订单：自动下单步骤2  循环云仓订单列表)
func CloudOrderCreateStep2(config common.SupplySetting, cloudOrderList common.CloudOrderList, GatherSuppliesId uint) {

	for _, item := range cloudOrderList.List {
		var cloudOrderModel model2.CloudOrder //云仓记录表数据
		//如果中台订单存在并且已支付 直接跳过
		err := source.DB().Where("cloud_order_id = ?", item.Order.Id).First(&cloudOrderModel).Error
		//数据库出错跳过
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("云仓自动下单,数据库错误-直接跳过", zap.Any("err", err))
			err = nil
			continue
		}
		if cloudOrderModel.ID != 0 {
			var orderModel model4.Order //中台订单数据
			source.DB().Where("third_order_sn = ?", cloudOrderModel.CloudOrderSn).Where("status = ?", model4.WaitPay).First(&orderModel)
			if orderModel.ID == 0 {
				//log.Log().Error("云仓订单记录表有数据并且中台订单并非待支付直接跳过!")
				continue
			}
		}
		res, err := common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": item.Order.Id}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res.Code != 1 {
			log.Log().Error("获取云仓订单详情失败!", zap.Any("err", res.Message))
			return
		}

		var cloudOrderDetail CloudOrderDetail //云仓订单详情数据
		datas := res.Data.(map[string]interface{})
		json1, _ := json.Marshal(datas)
		_ = json.Unmarshal(json1, &cloudOrderDetail)

		err = CloudOrderCreateStep3(config, cloudOrderDetail, GatherSuppliesId)
		if err != nil {
			log.Log().Error("云仓下单:错误", zap.Any("err", err))
		}
	}
}

// (云仓订单：自动下单步骤3  判断是否已经下单过 如果待支付则支付，单独触发直接获取详情调用步骤3即可)
func CloudOrderCreateStep3(config common.SupplySetting, cloudOrderDetail CloudOrderDetail, GatherSuppliesId uint) (err error) {
	//测试数据
	//cloudOrderDetail.OrderGoodsDetail[0].GoodsId = 27654
	//cloudOrderDetail.OrderGoodsDetail[0].GoodsOptionId = 1932690
	//config.Cloud.CloudUserId = 35
	var afterCount = 0 //存在售后的数量
	for _, itemOrderGoods := range cloudOrderDetail.OrderGoodsDetail {
		var cloudGoods model2.CloudGoods //云仓推送商品记录表数据
		source.DB().Where("cloud_product_id = ?", itemOrderGoods.GoodsId).First(&cloudGoods)
		//如果不是中台推送的商品直接跳过
		if cloudGoods.ID == 0 {
			err = errors.New("云仓下单:不是中台推送的商品，云仓商品id" + strconv.Itoa(itemOrderGoods.GoodsId))
			return
		}
		if itemOrderGoods.PayStatus != 1 {
			var payStatusText = ""
			switch itemOrderGoods.PayStatus {
			case 0:
				payStatusText = "待付款"
				break
			case 2:
				payStatusText = "申请退款"
				break
			case 3:
				payStatusText = "退款中"
				break
			case 4:
				payStatusText = "已退款"
				break
			case 5:
				payStatusText = "退款申请失败"
				break
			}
			err = errors.New("云仓下单:云仓订单状态不满足下单条件:" + payStatusText)
			afterCount++
			continue
		}
	}
	if afterCount == len(cloudOrderDetail.OrderGoodsDetail) {
		return
	}
	var cloudOrderData model2.CloudOrder //云仓订单记录表数据
	source.DB().Where("cloud_order_sn = ?", cloudOrderDetail.Order.OrderSn).First(&cloudOrderData)
	//支付
	if cloudOrderData.Status == 0 && cloudOrderData.OrderId != "" {
		var confirmInfoItem []model4.Order
		//因为存在中台拆单 所以这里查询多个
		err = source.DB().Where("third_order_sn = ?", cloudOrderDetail.Order.OrderSn).Find(&confirmInfoItem).Error
		if err != nil {
			log.Log().Error("云仓下单:获取订单详情失败", zap.Any("err", err))
			err = errors.New("云仓下单:获取订单详情失败:" + err.Error())
			return
		}
		for _, confirmInfoItemV := range confirmInfoItem {
			if confirmInfoItemV.Status == model4.WaitPay {
				var payInfo orderPay.PayInfo
				err, payInfo = orderPay.GetPayInfo(config.Cloud.CloudUserId, []uint{confirmInfoItemV.ID})
				if err != nil {
					log.Log().Error("云仓下单:获取支付信息失败", zap.Any("err", err))
					err = errors.New("云仓下单:获取支付信息失败:" + err.Error())
					return
				}
				err, _ = payment.PayService(paymentModel.UpdateBalance{
					Uid:       confirmInfoItemV.UserID,
					Amount:    confirmInfoItemV.Amount,
					PayType:   2,
					PayInfoID: int(payInfo.ID),
				})
				if err != nil {
					log.Log().Error("云仓下单:支付失败", zap.Any("err", err))
					err = errors.New("云仓下单:支付失败:" + err.Error())
					return
				}
			}
			if confirmInfoItemV.Status == model4.Closed {
				err = errors.New("云仓下单:中台订单已关闭" + strconv.Itoa(int(confirmInfoItemV.OrderSN)))
				return
			}
		}
		cloudOrderData.Status = cloudOrderDetail.Order.Status
		source.DB().Where("id = ?", cloudOrderData.ID).Updates(&cloudOrderData)
		return
	}
	//已下单 或下单出错的订单直接跳过
	if cloudOrderData.ID > 0 {
		err = errors.New("云仓下单:已下单或下单出错的订单直接跳过，中台订单号：" + cloudOrderData.OrderSn + "云仓订单号" + cloudOrderData.CloudOrderSn)
		return
	}

	var cloudOrder model2.CloudOrder
	cloudOrder.Status = 0
	cloudOrder.CloudOrderId = cloudOrderDetail.Order.Id
	cloudOrder.CloudOrderSn = cloudOrderDetail.Order.OrderSn
	cloudOrder.CloudOrderTotalPrice = cloudOrderDetail.Order.OrderTotalPrice
	cloudOrder.GatherSuppliesId = GatherSuppliesId
	err = source.DB().Create(&cloudOrder).Error
	if err != nil {
		log.Log().Error("云仓下单:创建记录失败", zap.Any("err", err))
		return
	}
	err, code := CloudOrderCreateStep4(config, cloudOrderDetail, cloudOrder)
	if err != nil {
		log.Log().Error("云仓下单:下单支付发货错误", zap.Any("err", err))
		var cloudOrderUpdate model2.CloudOrder
		//支付错误 不变为-1 再次请求时直接执行支付
		if code == 1 {
			cloudOrderUpdate.Status = -1
			cloudOrderUpdate.ErrorMsg = err.Error()
			//cloudOrder.SynStatus = 1 //因为同步失败状态变为待同步
			source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrderUpdate)
		} else {
			cloudOrderUpdate.ErrorMsg = err.Error()
			source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrderUpdate)
		}

	}
	return
}

// (云仓订单：自动下单步骤4  下单支付)
// code 错误类型 1正常错误,2支付错误  用于返回判断是否改云仓订单记录表状态为-1 1改 2不改
func CloudOrderCreateStep4(config common.SupplySetting, cloudOrderDetail CloudOrderDetail, cloudOrder model2.CloudOrder) (err error, code int) {
	one := 1
	zero := 0
	code = 1
	//config.Cloud.CloudUserId =
	var confirmOrder ConfirmOrder

	for _, itemOrderGoods := range cloudOrderDetail.OrderGoodsDetail {
		//状态不对直接跳过ser
		if itemOrderGoods.PayStatus != 1 {
			continue
		}
		var sku CloudSku

		////测试数据
		//var specsGroup model2.SpecsGroup
		//specsGroup.Id = 1932690
		//specsGroup.OutOptionId = 18771
		//var cloudGoodsDetail model2.CloudPushGoods
		//cloudGoodsDetail.SpecsGroup = append(cloudGoodsDetail.SpecsGroup,specsGroup)

		var res *common.APIResult
		res, err = common.RequestApi(string(common.CloudGetGoods), "POST", nil, g.Map{"goods_id": itemOrderGoods.GoodsId}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		//详情查询失败直接退出
		if res.Code != 1 {
			err = errors.New("云仓下单:获取云仓商品详情失败!" + strconv.Itoa(itemOrderGoods.GoodsId))
			log.Log().Error("获取云仓商品详情失败!"+strconv.Itoa(itemOrderGoods.GoodsId), zap.Any("err", res.Message))
			return
		}

		var cloudGoodsDetail model2.CloudPushGoods
		CloudGoodsData := res.Data.(map[string]interface{})
		CloudGoodsData1, _ := json.Marshal(CloudGoodsData)
		_ = json.Unmarshal(CloudGoodsData1, &cloudGoodsDetail)

		for _, itemGoodsSpecsGroup := range cloudGoodsDetail.SpecsGroup {
			if itemGoodsSpecsGroup.OutOptionId == 0 {
				err = errors.New("云仓下单:云仓商品详情第三方规格是0,云仓商品id" + strconv.Itoa(itemOrderGoods.GoodsId))
				return
			}
			//测试用完就删除
			//if itemGoodsSpecsGroup.Id == 13306932 {
			//	itemGoodsSpecsGroup.Id = 13166733
			//}
			//if itemOrderGoods.GoodsId == 70619 {
			//	itemGoodsSpecsGroup.OutOptionId = 130981 //测试用完就删除
			//}
			////130981
			//if itemOrderGoods.GoodsId == 102830 {
			//	itemGoodsSpecsGroup.OutOptionId = 131023 //测试用完就删除
			//}
			//获取商品规格id参数
			if itemGoodsSpecsGroup.Id == itemOrderGoods.GoodsOptionId {

				sku.SkuId = itemGoodsSpecsGroup.OutOptionId
				sku.Number = itemOrderGoods.Total
				sku.CloudGoodsOptionTitle = itemOrderGoods.GoodsOptionTitle
				sku.CloudGoodsId = uint(itemOrderGoods.GoodsId)
				sku.CloudGoodsOptionId = itemOrderGoods.GoodsOptionId
				sku.CloudGoodsTitle = itemOrderGoods.Title
				sku.CloudGoodsOrderSn = itemOrderGoods.GoodsOrderSn

				sku.Status = itemOrderGoods.GoodsStatus
				sku.PayStatus = itemOrderGoods.PayStatus
				//用于支付成功之后 直接发货
				sku.ExpressCompanyName = itemOrderGoods.ExpressCompanyName
				sku.ExpressSn = itemOrderGoods.ExpressSn
				sku.ExpressCode = itemOrderGoods.ExpressCode
				confirmOrder.Sku = append(confirmOrder.Sku, sku)
			}
		}
		//如果没有匹配上
		if sku.SkuId == 0 {
			//此处应该插入记录表数据 说明这个商品的规格找不到
			err = errors.New("云仓下单:云仓订单商品规格与商品详情规格匹配不上,云仓商品id" + strconv.Itoa(itemOrderGoods.GoodsId))
			return
		}

	}
	if confirmOrder.Sku == nil {
		err = errors.New("没有匹配到商品，订单id" + strconv.Itoa(cloudOrderDetail.Order.Id))
		return
	}
	//地区匹配
	cloudOrderDetail = OrderAddress(cloudOrderDetail)
	//下单开始
	confirmOrder.Address.Consignee = cloudOrderDetail.Order.RealName
	confirmOrder.Address.Phone = cloudOrderDetail.Order.Mobile
	confirmOrder.Address.Province = cloudOrderDetail.Order.Provice
	confirmOrder.Address.City = cloudOrderDetail.Order.City
	confirmOrder.Address.Area = cloudOrderDetail.Order.District
	confirmOrder.Address.Street = cloudOrderDetail.Order.Street
	confirmOrder.Address.Description = cloudOrderDetail.Order.City + "," + cloudOrderDetail.Order.District + "," + cloudOrderDetail.Order.Street + "," + cloudOrderDetail.Order.Address

	//获取地址id
	err, addressId := service.GetOrSetAddress(request2.Address(confirmOrder.Address))

	if err != nil {
		log.Log().Error("云仓下单:云仓下单获取地址出错!", zap.Any("err", err))
		err = errors.New("云仓下单:云仓下单获取地址出错:" + err.Error())
		return
	}

	id, err := cache.GetID("api_buy")
	if err != nil {
		err = errors.New("云仓下单:buy_id生成失败:" + err.Error())
		log.Log().Error("云仓下单:buy_id生成失败!", zap.Any("err", err))
		return
	}
	buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
	//插入购物车记录
	for _, item := range confirmOrder.Sku {
		shoppingCart := shoppingCartModel.ShoppingCart{
			UserID:    config.Cloud.CloudUserId,
			SkuID:     item.SkuId,
			Qty:       item.Number,
			Status:    &zero,
			Checked:   &one,
			BuyID:     uint(buyID),
			BuyWay:    3,
			AddressID: addressId,
		}
		err = shoppingCartService.CreateShoppingCart(shoppingCart)
		if err != nil {
			log.Log().Error("云仓下单:购物车添加失败,SkuId:"+strconv.Itoa(int(item.SkuId)), zap.Any("err", err))
			err = errors.New("云仓下单:购物车添加失败,SkuId:" + strconv.Itoa(int(item.SkuId)) + "" + err.Error())
			return
		}
	}
	// 读取购物车记录
	err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: config.Cloud.CloudUserId, BuyID: uint(buyID)})
	if err != nil {
		log.Log().Error("云仓下单:获取购物车记录失败", zap.Any("err", err))
		err = errors.New("云仓下单:获取购物车记录失败:" + err.Error())
		return
	}

	if len(shoppingCarts) == 0 {
		log.Log().Error("云仓下单:没有购物车记录")
		err = errors.New("云仓下单:没有购物车记录")
		return
	}
	var shoppingCartIDs []uint
	for _, shoppingCart := range shoppingCarts {
		shoppingCartIDs = append(shoppingCartIDs, shoppingCart.ID)
	}
	err = shoppingCartService.CheckBeforeSale(shoppingCartIDs)
	if err != nil {
		log.Log().Error("云仓下单:购物车检验失败", zap.Any("err", err))
		err = errors.New("云仓下单:购物车检验失败:" + err.Error())
		return
	}
	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(config.Cloud.CloudUserId, shoppingCarts)
	if err != nil {
		log.Log().Error("云仓下单:结算信息获取失败", zap.Any("err", err))
		err = errors.New("云仓下单:结算信息获取失败:" + err.Error())
		return
	}

	// 下单
	checkoutInfo.ThirdOrderSn = cloudOrderDetail.Order.OrderSn
	//如果云仓运费不是0则买家备注增加运费备注
	if cloudOrderDetail.Order.DispatchPrice != 0 {
		dispatchPrice := Fen2Yuan(cloudOrderDetail.Order.DispatchPrice)

		var remark = "云仓运费：" + dispatchPrice + "元"

		for orderK, _ := range checkoutInfo.Orders {
			checkoutInfo.Orders[orderK].Remark = remark
		}
	}
	err, confirmInfo := confirm.ShoppingCartConfirm(checkoutInfo)

	if err != nil {
		log.Log().Error("云仓下单:下单失败", zap.Any("err", err))
		err = errors.New("云仓下单:下单失败:" + err.Error())
		return
	}
	err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: config.Cloud.CloudUserId, BuyID: uint(buyID), BuyWay: 3})
	if err != nil {
		log.Log().Error("云仓下单:清空购物车记录", zap.Any("err", err))
		err = errors.New("云仓下单:清空购物车记录:" + err.Error())
		return
	}
	//下单结束
	//优先存储云仓子订单记录表数据
	for _, confirmInfoItem := range confirmInfo.Orders {

		cloudOrder.OrderSn += strconv.Itoa(int(confirmInfoItem.OrderSN)) + ","
		cloudOrder.OrderId += strconv.Itoa(int(confirmInfoItem.ID)) + ","

		for _, confirmInfoOrderItem := range confirmInfoItem.OrderItems {
			//var cloudOrderData  model2.CloudOrder
			//source.DB().Where("order_id = ",confirmInfoItem.ID).First(&cloudOrderData)
			var cloudOrderItem model2.CloudOrderItem
			cloudOrderItem.CloudOrderId = cloudOrder.ID
			cloudOrderItem.OrderId = confirmInfoItem.ID
			cloudOrderItem.OrderSn = confirmInfoItem.OrderSN
			cloudOrderItem.OrderItemId = confirmInfoOrderItem.ID
			cloudOrderItem.Total = confirmInfoOrderItem.Qty
			cloudOrderItem.ProductId = confirmInfoOrderItem.ProductID
			cloudOrderItem.SkuId = confirmInfoOrderItem.SkuID
			//拼接云仓商品订单号 用于发货 其他字段作用待定
			for _, confirmOrderSku := range confirmOrder.Sku {
				if confirmOrderSku.SkuId == confirmInfoOrderItem.SkuID {
					cloudOrderItem.CloudGoodsOptionId = confirmOrderSku.CloudGoodsOptionId
					cloudOrderItem.CloudGoodsTitle = confirmOrderSku.CloudGoodsTitle
					cloudOrderItem.CloudGoodsOptionTitle = confirmOrderSku.CloudGoodsOptionTitle
					cloudOrderItem.CloudGoodsOrderSn = confirmOrderSku.CloudGoodsOrderSn
					cloudOrderItem.CloudGoodsId = confirmOrderSku.CloudGoodsId
					cloudOrderItem.Status = confirmOrderSku.Status
					cloudOrderItem.PayStatus = confirmOrderSku.PayStatus
					break
				}
			}
			err = source.DB().Create(&cloudOrderItem).Error
			if err != nil {
				//如果记录表创建失败 直接关闭订单 并标记出错原因
				log.Log().Error("云仓下单:云仓订单记录子表创建失败", zap.Any("err", err), zap.Any("cloudOrderSn", cloudOrderDetail.Order.OrderSn), zap.Any("出错的记录:cloudOrderItem", cloudOrderItem))

				confirmInfoItem.Status = -1
				confirmInfoItem.Note = "云仓下单订单号" + cloudOrderDetail.Order.OrderSn + ":云仓子订单记录表创建失败" + err.Error()
				source.DB().Where("id = ?", confirmInfoItem.ID).Updates(&confirmInfoItem)

				err = errors.New("云仓下单:订单号:" + cloudOrderDetail.Order.OrderSn + ":云仓订单子记录表创建失败" + err.Error())
				return
			}
		}
	}
	//var GatherSuppliesId = cloudOrder.GatherSuppliesId
	//支付开始 //存在拆单可能 所以判断一下 如果拆弹重新创建一个 云仓订单记录 (因记录拆单不可行如果订单第一个就报错后面的全部都无法被记录)
	var key = 1
	cloudOrder.Status = 0
	err = source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder).Error
	if err != nil {
		log.Log().Error("云仓下单:云仓订单记录表创建失败", zap.Any("err", err), zap.Any("cloudOrderSn", cloudOrderDetail.Order.OrderSn), zap.Any("出错的记录:cloudOrderItem", cloudOrder))
		err = errors.New("云仓下单:订单号" + cloudOrderDetail.Order.OrderSn + ":云仓订单记录表创建失败" + err.Error())
		return
	}
	for _, confirmInfoItem := range confirmInfo.Orders {
		//if key>1 {
		//	var cloudOrder model2.CloudOrder
		//	cloudOrder.Status = 0
		//	cloudOrder.CloudOrderId = cloudOrderDetail.Order.Id
		//	cloudOrder.CloudOrderSn = cloudOrderDetail.Order.OrderSn
		//	cloudOrder.CloudOrderTotalPrice = cloudOrderDetail.Order.OrderTotalPrice
		//	cloudOrder.GatherSuppliesId = GatherSuppliesId
		//	cloudOrder.OrderSn = confirmInfoItem.OrderSN
		//	cloudOrder.OrderId = confirmInfoItem.ID
		//	err = source.DB().Create(&cloudOrder).Error
		//}else{

		//}
		key++

		var payInfo orderPay.PayInfo
		err, payInfo = orderPay.GetPayInfo(config.Cloud.CloudUserId, []uint{confirmInfoItem.ID})
		if err != nil {
			log.Log().Error("云仓下单:获取支付信息失败", zap.Any("err", err))
			err = errors.New("云仓下单:获取支付信息失败:" + err.Error())
			code = 2
			return
		}
		err, _ = payment.PayService(paymentModel.UpdateBalance{
			Uid:       confirmInfoItem.UserID,
			Amount:    confirmInfoItem.Amount,
			PayType:   2,
			PayInfoID: int(payInfo.ID),
		})
		if err != nil {
			log.Log().Error("云仓下单:支付失败", zap.Any("err", err))
			err = errors.New("云仓下单:支付失败:" + err.Error())
			code = 2
			return
		}
		//支付成功改变云仓记录表状态变为云仓订单状态
		cloudOrder.Status = cloudOrderDetail.Order.Status
		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)

	}
	//err = CloudOrderCreateStep5(config, cloudOrderDetail) //如果云仓订单已发货,则直接发货
	code = 2 //无论发货结果是什么都不需要改变云仓订单记录表状态为错误
	return
}

// 云仓订单 选择下单到中台
func CloudOrderCreate(config common.SupplySetting, cloudOrderCreate request.CloudOrderCreate) (err error) {
	var cloudOrderModel model2.CloudOrder //云仓记录表数据
	var orderModel model4.Order           //中台订单数据
	var cloudOrderDetail CloudOrderDetail //云仓订单详情数据
	for _, item := range cloudOrderCreate.OrderId {
		//如果中台订单存在并且已支付 直接跳过
		err = source.DB().Where("cloud_order_id = ?", item).First(&cloudOrderModel).Error
		//数据库出错跳过
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("云仓自动下单,数据库错误-直接跳过", zap.Any("err", err))
			err = errors.New("云仓自动下单,数据库错误-直接跳过" + err.Error())
			return
		}
		if cloudOrderModel.ID != 0 {
			//.Where("status = ?", model4.WaitPay)
			//已存在直接跳过
			source.DB().Where("third_order_sn = ?", cloudOrderModel.CloudOrderSn).First(&orderModel)
			if orderModel.ID == 0 {
				//log.Log().Error("云仓订单记录表有数据并且中台订单并非待支付直接跳过!")
				continue
			}
		}
		var res *common.APIResult
		res, err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": item}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res.Code != 1 {
			err = errors.New("获取订单详情失败,订单id" + strconv.Itoa(item))
			return
		}
		if res.Data == "" {
			err = errors.New("获取订单详情数据失败,订单id" + strconv.Itoa(item))
			return
		}
		datas := res.Data.(map[string]interface{})
		json1, _ := json.Marshal(datas)
		_ = json.Unmarshal(json1, &cloudOrderDetail)
		err = CloudOrderCreateStep3(config, cloudOrderDetail, cloudOrderCreate.GatherSuppliesId)
		if err != nil {
			return
		}
	}
	return
}

// 中台发货参数
type MiddlegroundOrderSend struct {
	OrderID      uint                         `json:"order_id"`
	OrderItemIDs []request3.OrderItemSendInfo `json:"order_item_ids"`
	CompanyCode  string                       `json:"company_code"`
	ExpressNo    string                       `json:"express_no"`
}

/*
*
地区判断 -- 使用云仓订单详情
*/
func OrderAddress(cloudOrderDetail CloudOrderDetail) (resCloudOrderDetail CloudOrderDetail) {
	var err error
	//部分地区与中台直接对应 -- 自动匹配 匹配补上
	city := ImmobilizationCity(cloudOrderDetail.Order.City)
	if city != "" {
		cloudOrderDetail.Order.City = city
	}
	var cloudAreaName model2.CloudAreaName
	source.DB().Where("name = ?", cloudOrderDetail.Order.City).First(&cloudAreaName)
	//如果存在区的名字放到街道中,区的名字变为市的名字，原因是云仓那面把有些市的街道当区返回的
	if cloudAreaName.ID != 0 {
		cloudOrderDetail.Order.Street = cloudOrderDetail.Order.District
		cloudOrderDetail.Order.District = cloudOrderDetail.Order.City
	}
	//获取以前记录的是否有完全符合 这个省市区街道的
	err, cloudAreaMatching := CloudAreaMatching(cloudOrderDetail.Order.Provice, cloudOrderDetail.Order.City, cloudOrderDetail.Order.District, cloudOrderDetail.Order.Street)

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("云仓自动下单,数据库错误-直接跳过", zap.Any("err", err))
		err = errors.New("云仓自动下单,数据库错误-直接跳过" + err.Error())
		return
	}
	//如果有则使用这个
	if cloudAreaMatching.ID != 0 {
		cloudOrderDetail.Order.Provice = cloudAreaMatching.Province
		cloudOrderDetail.Order.City = cloudAreaMatching.City
		cloudOrderDetail.Order.District = cloudAreaMatching.Area
		cloudOrderDetail.Order.Street = cloudAreaMatching.Street
	}
	return cloudOrderDetail
}

/*
*

	订单列表直接使用
*/
func OrderAddressNew(cloudOrderDetail common.CloudOrder) (resCloudOrderDetail common.CloudOrder) {
	//部分地区与中台直接对应 -- 自动匹配 匹配补上
	city := ImmobilizationCity(cloudOrderDetail.Order.City)
	if city != "" {
		cloudOrderDetail.Order.City = city
	}

	var cloudAreaName model2.CloudAreaName
	source.DB().Where("name = ?", cloudOrderDetail.Order.City).First(&cloudAreaName)
	//如果存在区的名字放到街道中,区的名字变为市的名字，原因是云仓那面把有些市的街道当区返回的
	if cloudAreaName.ID != 0 {
		cloudOrderDetail.Order.Street = cloudOrderDetail.Order.District
		cloudOrderDetail.Order.District = cloudOrderDetail.Order.City
	}
	//获取以前记录的是否有完全符合 这个省市区街道的
	err, cloudAreaMatching := CloudAreaMatching(cloudOrderDetail.Order.Provice, cloudOrderDetail.Order.City, cloudOrderDetail.Order.District, cloudOrderDetail.Order.Street)

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error("云仓自动下单,数据库错误-直接跳过", zap.Any("err", err))
		err = errors.New("云仓自动下单,数据库错误-直接跳过" + err.Error())
		return
	}
	//如果有则使用这个
	if cloudAreaMatching.ID != 0 {
		cloudOrderDetail.Order.Provice = cloudAreaMatching.Province
		cloudOrderDetail.Order.City = cloudAreaMatching.City
		cloudOrderDetail.Order.District = cloudAreaMatching.Area
		cloudOrderDetail.Order.Street = cloudAreaMatching.Street
	}
	return cloudOrderDetail
}

/*
*

	固定对应的城市
*/
func ImmobilizationCity(city string) (resCity string) {
	switch city {
	case "楚雄州":
		resCity = "楚雄彝族自治州"
		break
	case "文山州":
		resCity = "文山壮族苗族自治州"
		break
	case "红河州":
		resCity = "红河哈尼族彝族自治州"
		break
	case "大理州":
		resCity = "大理白族自治州"
		break
	case "西双版纳州":
		resCity = "西双版纳傣族自治州"
		break
	case "德宏州":
		resCity = "德宏傣族景颇族自治州"
		break
	case "怒江州":
		resCity = "怒江傈僳族自治州"
		break
	case "迪庆州":
		resCity = "迪庆藏族自治州"
		break
	case "黔西南州":
		resCity = "黔西南布依族苗族自治州"
		break
	case "黔东南州":
		resCity = "黔东南苗族侗族自治州"
		break
	case "黔南州":
		resCity = "黔南布依族苗族自治州"
		break
	}
	return
}

/*
*

	城市匹配失败的 手动下单之后会记录失败前和手动下单选择的城市 下单直接使用手动下单时选择的城市
*/
func CloudAreaMatching(Provice string, City string, District string, Street string) (err error, cloudAreaMatching model2.CloudAreaMatching) {
	//获取以前记录的是否有完全符合 这个省市区街道的
	err = source.DB().Model(&model2.CloudAreaMatching{}).Where("cloud_province = ?", Provice).Where("cloud_city = ?", City).Where("cloud_area = ?", District).Where("cloud_street = ?", Street).First(&cloudAreaMatching).Error
	return
}

// (云仓订单：自动下单步骤5  如果云仓订单已发货,则直接发货中台的订单)
func CloudOrderCreateStep5(config common.SupplySetting, cloudOrderDetail CloudOrderDetail) (err error) {
	for _, itemOrderGoods := range cloudOrderDetail.OrderGoodsDetail {
		var middlegroundOrderSend MiddlegroundOrderSend
		if itemOrderGoods.GoodsStatus != 0 {
			var cloudOrderItem model2.CloudOrderItem
			var orderItemSend request3.OrderItemSendInfo
			var ExpressCode string
			source.DB().Where("cloud_goods_order_sn = ?", itemOrderGoods.GoodsOrderSn).First(&cloudOrderItem)
			if cloudOrderItem.ID == 0 {
				err = errors.New("云仓下单:子订单创建失败,发货失败:云仓子订单号" + itemOrderGoods.GoodsOrderSn)
				return
			}
			//修改子记录表状态
			cloudOrderItem.Status = itemOrderGoods.GoodsStatus
			cloudOrderItem.PayStatus = itemOrderGoods.PayStatus
			source.DB().Where("id = ?", cloudOrderItem.ID).Updates(cloudOrderItem)

			//中台订单发货开始
			var orderItem model4.OrderItem

			source.DB().Where("id = ?", cloudOrderItem.OrderItemId).First(&orderItem)
			if orderItem.ID == 0 {
				err = errors.New("云仓下单:中台子订单创建失败,发货失败:云仓子订单号" + itemOrderGoods.GoodsOrderSn)
				return
			}
			if orderItem.SendStatus != 0 {
				err = errors.New("云仓下单:中台子订单已发货,中台子订单号" + strconv.Itoa(int(cloudOrderItem.OrderItemId)))
				return
			}
			err, ExpressCode = ExpressList(itemOrderGoods.ExpressCompanyName)
			if err != nil {
				ExpressCode = itemOrderGoods.ExpressCode
			}
			middlegroundOrderSend.OrderID = cloudOrderItem.OrderId
			middlegroundOrderSend.ExpressNo = itemOrderGoods.ExpressSn
			middlegroundOrderSend.CompanyCode = ExpressCode

			orderItemSend.ID = cloudOrderItem.OrderItemId
			orderItemSend.Num = itemOrderGoods.Total

			middlegroundOrderSend.OrderItemIDs = append(middlegroundOrderSend.OrderItemIDs, orderItemSend)
			err = sendOrder(config, middlegroundOrderSend)
			if err != nil {
				return
			}
		}
	}
	return
}

// 获取快递code
func ExpressList(name string) (err error, code string) {

	for _, item := range express2.GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

type CloudOrderService struct {
	source.Model
	OrderId              uint         `json:"order_id" form:"order_id" gorm:"column:order_id;comment:中台订单id;"`                                                //中台订单id
	OrderSn              uint         `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:中台订单编号;"`                                              //中台订单编号
	CloudOrderSn         string       `json:"cloud_order_sn" form:"cloud_order_sn" gorm:"column:cloud_order_sn;comment:云仓订单号;"`                              //云仓订单号
	CloudOrderId         int          `json:"cloud_order_id"  form:"cloud_order_id" gorm:"column:cloud_order_id;comment:云仓订单id"`                              // 云仓订单id
	Status               int          `json:"status" form:"status" gorm:"column:status;comment:-1订单错误1待发货2已发货3完成;"`                                   //-1订单错误0待支付1待发货2完成//云仓订单发货之后就算完成
	ErrorMsg             string       `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;"`                                               //错误内容
	CloudOrderTotalPrice uint         `json:"cloud_order_total_price" form:"cloud_order_total_price" gorm:"column:cloud_order_total_price;comment:云仓订单金额;"` //云仓订单金额
	Order                model4.Order `json:"order" gorm:"foreignkey:order_id"`
}

func (receiver CloudOrderService) TableName() string {
	return "cloud_orders"
}

// 发货方法1 定时同步云仓发货的内容，让中台订单发货 步骤2发货
func CronCloudSendOrder(config common.SupplySetting, GatherSuppliesId uint) {
	var cloudOrderList []CloudOrderService
	//查询未发货的订单
	joinWhere := "INNER join orders on orders.id = cloud_orders.order_id and orders.status = 1"
	err := source.DB().Joins(joinWhere).Preload("Order").Where("cloud_orders.status != ?", -1).Where("gather_supplies_id = ?", GatherSuppliesId).Find(&cloudOrderList).Error
	if err != nil {
		log.Log().Error("暂无待发货的云仓下单到中台的订单")
		return
	}

	for _, item := range cloudOrderList {
		if item.Order.ID == 0 {
			log.Log().Error("没有查询到中台订单跳过")
			return
		}
		res, err := common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": item.CloudOrderId}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res.Code != 1 {
			log.Log().Error("获取云仓订单详情失败,订单id!"+strconv.Itoa(item.CloudOrderId), zap.Any("err", res.Message))
			return
		}
		if res.Data == "" {
			log.Log().Error("获取云仓订单详情失败,订单id!"+strconv.Itoa(item.CloudOrderId), zap.Any("err", res.Message))
			return
		}
		datas := res.Data.(map[string]interface{})
		var cloudOrder CloudOrderDetail
		json1, _ := json.Marshal(datas)
		_ = json.Unmarshal(json1, &cloudOrder)
		if cloudOrder.Order.Status == 1 {
			continue
		}
		source.DB().Model(&model2.CloudOrder{}).Where("id = ?", item.ID).Updates(g.Map{"status": cloudOrder.Order.Status})
		err = CloudOrderCreateStep5(config, cloudOrder) //调用发货步骤
		if err != nil {
			log.Log().Error("云仓下单，中台订单发货失败!", zap.Any("err", err))
		}
	}
}

type OrderExpress struct {
	ID          uint              `json:"id"`
	OrderID     uint              `json:"-"`
	ExpressNo   string            `json:"express_no"`
	CompanyCode string            `json:"company_code"`
	CompanyName string            `json:"company_name" gorm:"-"`
	CreatedAt   *source.LocalTime `json:"created_at"`

	OrderItems []OrderItem `json:"order_items" gorm:"many2many:item_expresses;references:ID;joinReferences:OrderItemID"`
}
type OrderItem struct {
	source.SoftDel
	ID        uint   `json:"id"`
	ImageUrl  string `json:"image_url"`
	ProductID uint   `json:"product_id"`
	Title     string `json:"title"`
	SkuTitle  string `json:"sku_title"`
	SkuID     uint   `json:"sku_id"`
	Qty       uint   `json:"qty"`
	SendNum   uint   `json:"send_num"`
}

func (oe *OrderExpress) AfterFind(tx *gorm.DB) (err error) {
	err, oe.CompanyName = express2.GetCompanyByCode(oe.CompanyCode)
	return
}

// 云仓物流
type Deliver struct {
	Code string `json:"code"`
	Name string `json:"name"`
}
type SendCloudOrder struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		FailList []struct {
			Address      string `json:"address"`
			CityName     string `json:"city_name"`
			CountyName   string `json:"county_name"`
			GoodsName    string `json:"goods_name"`
			GoodsOrderSn string `json:"goods_order_sn"`
			GoodsStatus  string `json:"goods_status"`
			Id           int    `json:"id"`
			OrderSn      string `json:"order_sn"`
			Phone        string `json:"phone"`
			ProvinceName string `json:"province_name"`
			Reason       string `json:"reason"`
			ShippingName string `json:"shipping_name"`
			SkuName      string `json:"sku_name"`
			TownName     string `json:"town_name"`
		} `json:"fail_list"`
	} `json:"data"`
}

type FailList struct {
	Address      string `json:"address"`
	CityName     string `json:"city_name"`
	CountyName   string `json:"county_name"`
	GoodsName    string `json:"goods_name"`
	GoodsOrderSn string `json:"goods_order_sn"`
	GoodsStatus  string `json:"goods_status"`
	Id           int    `json:"id"`
	OrderSn      string `json:"order_sn"`
	Phone        string `json:"phone"`
	ProvinceName string `json:"province_name"`
	Reason       string `json:"reason"`
	ShippingName string `json:"shipping_name"`
	SkuName      string `json:"sku_name"`
	TownName     string `json:"town_name"`
}

// 获取快递code
func CloudExpressList(name string, code string, delivers []Deliver) (err error, resName string) {

	for _, item := range delivers {
		if item.Name == name || item.Code == code {
			resName = item.Name
			fmt.Println(resName)
			return
		}
		//名称与code 比对不上就不发货了
		//else if strings.Contains(item.Name, name) {
		//	resName = item.Name
		//	fmt.Println(resName)
		//	return
		//} else if strings.Contains(name, item.Name) {
		//	resName = item.Name
		//	fmt.Println(resName)
		//	return
		//}
	}
	return
}

//监听中台订单发货 中台订单发货 云仓订单随之一起发货
/**
isUpdate 1是修改物流 2发货
*/
func LisOrderSend(orderId uint, isUpdate int) (err error) {
	var orderModel model4.Order
	err = source.DB().Where("id = ?", orderId).First(&orderModel).Error
	if err != nil {
		log.Log().Error("云仓监听订单发货:获取订单失败!", zap.Any("err", err), zap.Any("orderId", orderId))
		return
	}

	var cloudOrder model2.CloudOrder
	source.DB().Where("cloud_order_sn = ?", orderModel.ThirdOrderSN).First(&cloudOrder)
	if cloudOrder.ID == 0 {
		log.Log().Error("云仓监听订单发货:并非云仓订单不需要操作!订单id" + strconv.Itoa(int(orderId)))
		err = errors.New("云仓监听订单发货:并非云仓订单不需要操作!订单id" + strconv.Itoa(int(orderId)))
		return
	}
	log.Log().Debug("云仓监听订单发货：收到需要发货订单", zap.Any("orderId", orderId))
	err, config := common.InitCloudSetting(cloudOrder.GatherSuppliesId)

	if err != nil {
		log.Log().Error("云仓监听订单发货:获取设置失败!", zap.Any("err", err))
		cloudOrder.Status = -2
		cloudOrder.ErrorMsg = "云仓监听订单发货:获取设置失败：" + err.Error()
		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)
		return
	}
	var orderExpress []OrderExpress
	err = source.DB().Preload("OrderItems").Where("order_id = ?", orderId).Find(&orderExpress).Error

	if err != nil {
		return
	}
	var delivers []Deliver
	err, deliverList := GetDeliverList(config)
	if err != nil {
		log.Log().Error("云仓监听订单发货:获取云仓物流公司失败!", zap.Any("err", err.Error()))
		cloudOrder.Status = -2
		cloudOrder.ErrorMsg = "云仓监听订单发货:获取云仓物流公司失败：" + err.Error()
		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)
		return
	}

	deliverLists := deliverList.Data.(interface{})

	json1, _ := json.Marshal(deliverLists)
	_ = json.Unmarshal(json1, &delivers)

	var sendData []interface{}

	for _, item := range orderExpress {
		for _, orderItem := range item.OrderItems {
			var companyName string
			var middlegroundCloudExpressMatching model2.MiddlegroundCloudExpressMatching
			//因顺风中台存储的时候把手机号后四位拼接上了  这里需要去掉否则提交到云仓 云仓会不认这个单号
			if strings.Index(item.ExpressNo, ":") != -1 {
				item.ExpressNo = item.ExpressNo[0:strings.Index(item.ExpressNo, ":")]
			}
			source.DB().Where("name = ?", item.CompanyName).First(&middlegroundCloudExpressMatching)
			if middlegroundCloudExpressMatching.ID != 0 {
				companyName = middlegroundCloudExpressMatching.CloudName
			} else {
				err, companyName = CloudExpressList(item.CompanyName, item.CompanyCode, delivers)
			}
			//switch item.CompanyName {
			//	case "邮政国内小包":
			//		companyName ="邮政快递包裹"
			//		err = nil
			//		break
			//	case "中通":
			//		companyName ="中通快递"
			//		err = nil
			//		break
			//	case "圆通":
			//		companyName ="圆通速递"
			//		err = nil
			//		break
			//
			//	default:
			//		err,companyName = CloudExpressList(item.CompanyName,item.CompanyCode,delivers)
			//		break
			//
			//}
			if err != nil || companyName == "" {
				log.Log().Error("云仓监听订单发货:发货失败物流公司匹配不上!")
				cloudOrder.Status = -2
				cloudOrder.ErrorMsg = "中台订单发货，云仓订单自动发货失败：物流公司匹配不上!,中台物流公司名称：" + item.CompanyName
				source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)
				err = errors.New(cloudOrder.ErrorMsg)
				break
			}
			var orderGoods = make(map[string]interface{})
			item.ExpressNo = strings.TrimSpace(item.ExpressNo)
			orderGoods["express_name"] = companyName
			orderGoods["express_sn"] = item.ExpressNo
			var cloudOrderItem model2.CloudOrderItem
			source.DB().Where("order_item_id = ?", orderItem.ID).First(&cloudOrderItem)
			if cloudOrderItem.ID == 0 {
				log.Log().Error("云仓监听订单发货:云仓子订单记录获取失败!", zap.Any("order_item_id", orderItem.ID))
				continue
			}
			//大于0代表云仓这个子订单已发货 直接跳过
			if isUpdate != 1 {
				if cloudOrderItem.Status > 0 {
					log.Log().Error("云仓监听订单发货:云仓子订单记录已发货不重新发货!", zap.Any("cloudOrderItem", cloudOrderItem))
					continue
				}
			}

			//修改状态为发货
			//cloudOrderItem.Status = 1
			//source.DB().Where("id = ?",cloudOrderItem.ID).Updates(cloudOrderItem)

			orderGoods["goods_order_sn"] = cloudOrderItem.CloudGoodsOrderSn
			sendData = append(sendData, orderGoods)
		}
	}

	if len(sendData) == 0 {
		err = errors.New("云仓监听订单发货:暂无需要发货的云仓子订单")
		return
	}

	sendBodyData, _ := json.Marshal(sendData)
	//log.Log().Error("云仓监听订单发货:云仓发货参数!", zap.Any("sendData", sendData), zap.Any("GatherSuppliesId", cloudOrder.GatherSuppliesId))

	var url = common.CloudOrderSend
	if isUpdate == 1 {
		url = common.CloudOrderAgainSends
	}
	res, err := common.RequestApiJson(string(url), "POST", nil, sendBodyData, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	//log.Log().Error("云仓监听订单发货:发货/修改发货，返回内容", zap.Any("res", res))

	if isUpdate != 1 {
		if res.Code != 1 {
			log.Log().Error("云仓监听订单发货:发货错误，错误原因" + res.Message)
			cloudOrder.Status = -2
			cloudOrder.ErrorMsg = res.Message
			source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)
			err = errors.New(res.Message)
			return
		}
		var sendCloudOrder SendCloudOrder
		resJson, _ := json.Marshal(res)
		_ = json.Unmarshal(resJson, &sendCloudOrder)
		var CloudOrderSendApis []request.CloudOrderSendApi

		_ = json.Unmarshal(sendBodyData, &CloudOrderSendApis)

		for _, item := range sendCloudOrder.Data.FailList {
			var errorMsg string
			for _, itemCompanyName := range CloudOrderSendApis {
				if itemCompanyName.GoodsOrderSn == item.GoodsOrderSn {
					errorMsgSendData, _ := json.Marshal(itemCompanyName)
					source.DB().Model(&model2.CloudOrderItem{}).Where("cloud_goods_order_sn = ?", item.GoodsOrderSn).Updates(&model2.CloudOrderItem{ErrorMsg: item.Reason + string(errorMsgSendData)})
					var cloudOrderErrorMsg model2.CloudOrder
					source.DB().Model(&model2.CloudOrder{}).Where("cloud_order_sn = ?", item.OrderSn).First(&cloudOrderErrorMsg)
					errorMsg = cloudOrderErrorMsg.ErrorMsg
					errorMsg += item.Reason + string(errorMsgSendData)
					cloudOrderErrorMsg.Status = -2
					cloudOrderErrorMsg.ErrorMsg = errorMsg
					source.DB().Where("id = ?", cloudOrderErrorMsg.ID).Save(&cloudOrderErrorMsg)
				}
			}
		}
	}

	//发货成功之后同步云仓订单状态
	res, err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": cloudOrder.CloudOrderId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New("获取订单详情失败,订单id" + strconv.Itoa(cloudOrder.CloudOrderId))
		return
	}
	if res.Data == "" {
		err = errors.New("获取订单详情数据失败,订单id" + strconv.Itoa(cloudOrder.CloudOrderId))
		return
	}
	datas := res.Data.(map[string]interface{})
	var getCloudOrder CloudOrderDetail
	json1, _ = json.Marshal(datas)
	_ = json.Unmarshal(json1, &getCloudOrder)
	if getCloudOrder.Order.Status != 1 {
		cloudOrder.Status = getCloudOrder.Order.Status
		source.DB().Where("id = ?", cloudOrder.ID).Updates(cloudOrder)
	}
	for _, item := range getCloudOrder.OrderGoodsDetail {
		source.DB().Model(&model2.CloudOrderItem{}).Where("cloud_goods_order_sn = ?", item.GoodsOrderSn).Updates(g.Map{"status": item.GoodsStatus})
	}
	return
}

// 获取自动发货失败的订单重新发货
func GetSendErrorOrder() (err error) {
	//获取监听发货失败的云仓订单
	var cloudOrder []model2.CloudOrder
	err = source.DB().Where("status = -2").Find(&cloudOrder).Error
	if err != nil {
		log.Log().Error("没有自动发货失败的订单")
	} else {
		go func(cloudOrder []model2.CloudOrder) {
			for _, item := range cloudOrder {
				var orderDatas []model4.Order
				err = source.DB().Where("third_order_sn = ?", item.CloudOrderSn).Find(&orderDatas).Error
				if err != nil {
					continue
				}
				for _, orderData := range orderDatas {
					_ = LisOrderSend(orderData.ID, 2)
				}
			}
		}(cloudOrder)
	}
	//获取没有拿到监听发货失败的订单
	var cloudOrder1 []model2.CloudOrder
	err = source.DB().Where("cloud_orders.status = 1").Joins("INNER join orders on orders.third_order_sn = cloud_orders.cloud_order_sn and orders.status = 2").Select("cloud_orders.*").Find(&cloudOrder1).Error
	if err != nil {
		log.Log().Error("没有自动发货失败的订单")
	} else {
		go func(cloudOrder1 []model2.CloudOrder) {
			for _, item := range cloudOrder1 {
				var orderDatas []model4.Order
				err = source.DB().Where("third_order_sn = ?", item.CloudOrderSn).Find(&orderDatas).Error
				if err != nil {
					continue
				}
				for _, orderData := range orderDatas {
					_ = LisOrderSend(orderData.ID, 2)
				}
			}
		}(cloudOrder1)
	}
	return
}

//获取自动发货失败的订单重新发货
/**
companyCode 中台快递code
*/
func UpdateSendErrorOrder(companyCode string) (err error) {
	var cloudOrder []model2.CloudOrder

	joinWhere := "INNER join order_expresses on order_expresses.order_id = cloud_orders.order_id"
	var date = "2022-06-11 00:00:00"
	err = source.DB().Joins(joinWhere).Where("order_expresses.company_code = ?", companyCode).Where("order_expresses.created_at < ?", date).Select("cloud_orders.*").Find(&cloudOrder).Error

	if err != nil {
		log.Log().Error("没有自动发货失败的订单" + companyCode)
	} else {
		go func(cloudOrder []model2.CloudOrder) {
			for _, item := range cloudOrder {
				var orderDatas []model4.Order
				err = source.DB().Where("third_order_sn = ?", item.CloudOrderSn).Find(&orderDatas).Error
				if err != nil {
					continue
				}
				for _, orderData := range orderDatas {
					_ = LisOrderSend(orderData.ID, 1)
				}
			}
		}(cloudOrder)
	}
	return
}

// 中台订单发货（暂无接到参数直接发货的方法  接c.的不算）
func sendOrder(config common.SupplySetting, orderRequest MiddlegroundOrderSend) (err error) {
	// 记录快递信息
	orderExpress := model4.OrderExpress{
		OrderExpressModel: model4.OrderExpressModel{
			OrderID:     orderRequest.OrderID,
			CompanyCode: orderRequest.CompanyCode,
			ExpressNo:   orderRequest.ExpressNo,
		},
	}
	err, sendInfo := express.GetOrderSendInfo(orderRequest.OrderID)
	// 获取发货信息
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("云仓下单:获取发货信息失败")
		return
	}
	// 校验订单状态
	if sendInfo.Status != model4.WaitSend {
		log.Log().Error(sendInfo.StatusName + "的订单无法发货")
		err = errors.New("云仓下单:订单状态错误:" + sendInfo.StatusName + "的订单无法发货")
		return
	}
	// 校验订单商品发货状态
	for _, orderItem := range sendInfo.OrderItems {
		for _, orderItemID := range orderRequest.OrderItemIDs {
			if orderItem.ID == orderItemID.ID {
				if orderItem.SendStatus == 1 {
					log.Log().Error("商品[" + orderItem.Title + "|" + orderItem.SkuTitle + "]已发货，无法重复发货")
					err = errors.New("云仓下单:订单状态错误商品[" + orderItem.Title + "|" + orderItem.SkuTitle + "]已发货，无法重复发货")

					return
				}
			}
		}
	}
	// 记录快递信息对应商品
	err = express.SaveOrderExpress(&orderExpress, orderRequest.OrderItemIDs)
	if err != nil {
		log.Log().Error("云仓下单:记录发货商品失败!", zap.Any("err", err))
		err = errors.New("云仓下单:记录发货商品失败:" + err.Error())
		return
	}

	// 全部订单商品发货后，订单发货
	err, canSend := express.OrderCanSend(orderRequest.OrderID)
	if err != nil {
		log.Log().Error("云仓下单:发货失败!!", zap.Any("err", err))
		err = errors.New("云仓下单:发货失败:" + err.Error())
		return
	}
	if canSend {
		// 发货
		if err = order.Send(orderRequest.OrderID); err != nil {
			log.Log().Error("云仓下单:发货失败!", zap.Any("err", err))
			err = errors.New("云仓下单:发货失败:" + err.Error())
			return
		}
	} else {
		// 部分发货
		if err = order.Sending(orderRequest.OrderID); err != nil {
			log.Log().Error("云仓下单:部分发货失败!", zap.Any("err", err))
			err = errors.New("云仓下单:部分发货失败:" + err.Error())
			return
		}

	}
	service2.CreateOperationRecord(config.Cloud.CloudUserId, 3, "", "订单"+strconv.Itoa(int(orderRequest.OrderID))+"发货")
	return
}

type CloudOrderRecordList struct {
	model2.CloudOrder
	CloudOrderItem []model2.CloudOrderItem `json:"cloud_order_item" gorm:"foreignkey:cloud_order_id"` //

}

func (receiver CloudOrderRecordList) TableName() string {
	return "cloud_orders"
}

// 获取云仓记录表列表
func GetCloudOrderRecordList(info request.CloudOrderRecordSearch) (err error, list []CloudOrderRecordList, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&CloudOrderRecordList{}).Preload("CloudOrderItem")
	// 如果有条件搜索 下方会自动创建搜索语句
	//中台订单id
	if info.OrderId != "" {
		db.Where("order_id like ?", "%"+info.OrderId+"%")
	}
	//云仓订单id
	if info.CloudOrderId != 0 {
		db.Where("cloud_order_id", info.CloudOrderId)
	}
	if info.SynStatus != 0 {
		db.Where("syn_status", info.SynStatus)
	}
	if info.CloudOrderSn != "" {
		db.Where("cloud_order_sn", info.CloudOrderSn)
	}
	//订单状态
	if info.Status != nil {
		db.Where("status", info.Status)
	}
	if info.IsOffline != nil {
		db.Where("is_offline", info.IsOffline)

	}
	//供应链id
	if info.GatherSuppliesId != 0 {
		db.Where("gather_supplies_id", info.GatherSuppliesId)
	}
	if info.CreatedStartTime != "" {
		db.Where("created_at >= ?", info.CreatedStartTime)
	}
	if info.CreatedEndTime != "" {
		db.Where("created_at <= ?", info.CreatedEndTime)
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&list).Error
	return err, list, total
}

type CloudGoods struct {
	source.Model
	ProductId uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:中台商品id;"` //中台商品id
	SkuId     uint `json:"sku_id"`                                                                    //匹配到 的中台规格ID

	CloudProductId uint `json:"cloud_product_id" form:"cloud_product_id" gorm:"column:cloud_product_id;comment:云仓商品id;"` //云仓商品id
	IsUpdate       int  `json:"is_update"  form:"is_update" gorm:"column:is_update;comment:是否修改1是0否;"`                 // 是否修改1是0否
	IsDelete       int  `json:"is_delete"  form:"is_delete" gorm:"column:is_delete;comment:是否待删除1是0否;"`               // 是否待删除1是0否

	GatherSuppliesId uint `json:"gather_supplies_id" form:"gather_supplies_id" gorm:"column:gather_supplies_id;comment:供应链id;"` //供应链id
}

// 手动下单时返回的订单详情
func GetCloudOrderProduct(config common.SupplySetting, searchData request.CloudOrderDetatil) (err error, cloudOrderDetail CloudOrderDetail, cloudOrderModel model2.CloudOrder) {
	source.DB().Where("cloud_order_id = ?", searchData.OrderId).First(&cloudOrderModel)
	if cloudOrderModel.ID == 0 {
		err = errors.New("云仓订单记录不存在")
		return
	}
	if cloudOrderModel.Status != -1 && cloudOrderModel.SynStatus == 3 {
		err = errors.New("云仓记录状态，订单下单成功")
		return
	}
	var res *common.APIResult
	res, err = common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": searchData.OrderId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New(res.Message)
		return
	}
	datas := res.Data.(map[string]interface{})
	json1, _ := json.Marshal(datas)
	_ = json.Unmarshal(json1, &cloudOrderDetail)
	cloudOrderDetail = OrderAddress(cloudOrderDetail)
	var address Address
	//下单开始
	address.Consignee = cloudOrderDetail.Order.RealName
	address.Phone = cloudOrderDetail.Order.Mobile
	address.Province = cloudOrderDetail.Order.Provice
	address.City = cloudOrderDetail.Order.City
	address.Area = cloudOrderDetail.Order.District
	address.Street = cloudOrderDetail.Order.Street
	address.Description = cloudOrderDetail.Order.Address

	//err, province, city, county, town = MatchingRegion(addressRequest)
	//省市区县单独建立方法方便其他地方调用（例如修改收货地址的时）
	_, cloudOrderDetail.Order.ProviceModel, cloudOrderDetail.Order.CityModel, cloudOrderDetail.Order.CountyModel, cloudOrderDetail.Order.TownModel = service.MatchingRegion(request2.Address(address))

	for key, item := range cloudOrderDetail.OrderGoodsDetail {
		var cloudGoods CloudGoods
		var cloudOrderItem model2.CloudOrderItem

		source.DB().Where("gather_supplies_id=? and cloud_product_id=?", searchData.GatherSuppliesId, item.GoodsId).First(&cloudGoods)

		source.DB().Where("cloud_goods_order_sn=?", item.GoodsOrderSn).First(&cloudOrderItem)

		var res1 *common.APIResult
		res1, err = common.RequestApi(string(common.CloudGetGoods), "POST", nil, g.Map{"goods_id": item.GoodsId}, config)
		if err != nil {
			err = errors.New(err.Error())
			return
		}
		if res1.Code == 1 {
			var cloudGoodsDetail model2.CloudPushGoods
			CloudGoodsData := res1.Data.(map[string]interface{})
			CloudGoodsData1, _ := json.Marshal(CloudGoodsData)
			_ = json.Unmarshal(CloudGoodsData1, &cloudGoodsDetail)

			for _, itemGoodsSpecsGroup := range cloudGoodsDetail.SpecsGroup {
				//获取商品规格id参数
				if itemGoodsSpecsGroup.Id == item.GoodsOptionId {
					cloudGoods.SkuId = itemGoodsSpecsGroup.OutOptionId
				}
			}
		}
		cloudOrderDetail.OrderGoodsDetail[key].CloudGoods = cloudGoods
		cloudOrderDetail.OrderGoodsDetail[key].CloudOrderItem = cloudOrderItem

	}

	return
}

// 云仓订单 手动填写信息 下单到中台
func ManualCloudOrderCreate(config common.SupplySetting, cloudOrderCreate request.ManualCloudOrderCreate) (err error, code int) {
	var cloudOrderModel model2.CloudOrder //云仓记录表数据
	source.DB().Where("id = ?", cloudOrderCreate.CloudOrderId).First(&cloudOrderModel)
	if cloudOrderModel.ID == 0 {
		err = errors.New("云仓订单记录不存在")
		return
	}
	//调整为 不是错误状态 和 是全部同步则直接返回
	if cloudOrderModel.Status != -1 && cloudOrderModel.SynStatus == 3 {
		err = errors.New("云仓记录状态，订单下单成功")
		return
	}
	var cloudOrderDetail CloudOrderDetail //云仓订单详情数据
	res, err := common.RequestApi(string(common.GetOrder), "POST", nil, g.Map{"order_id": cloudOrderCreate.OrderId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New("获取订单详情失败,订单id" + strconv.Itoa(int(cloudOrderCreate.OrderId)))
		return
	}
	if res.Data == "" {
		err = errors.New("获取订单详情数据失败,订单id" + strconv.Itoa(int(cloudOrderCreate.OrderId)))
		return
	}
	datas := res.Data.(map[string]interface{})
	json1, _ := json.Marshal(datas)
	_ = json.Unmarshal(json1, &cloudOrderDetail)
	//if len(cloudOrderDetail.OrderGoodsDetail) != len(cloudOrderCreate.Goods) {
	//	err = errors.New("提交下单子订单数与云仓不符，云仓子订单数量:" + string(len(cloudOrderDetail.OrderGoodsDetail)))
	//	return
	//} || cloudOrderCreate.Address.Street == ""
	if cloudOrderCreate.Address.City == "" || cloudOrderCreate.Address.Province == "" || cloudOrderCreate.Address.Area == "" {
		err = errors.New("请选择省市区县")
		return
	} else {
		var province model3.Region
		var city model3.Region
		var county model3.Region
		var town model3.Region

		err = source.DB().Where("id = ?", cloudOrderCreate.Address.Province).Where("level = ?", 1).First(&province).Error
		if err != nil {
			err = errors.New("省不存在")
			return
		}

		err = source.DB().Where("id = ?", cloudOrderCreate.Address.City).Where("level = ?", 2).First(&city).Error
		if err != nil {
			err = errors.New("市不存在")
			return
		}

		err = source.DB().Where("id = ?", cloudOrderCreate.Address.Area).Where("level = ?", 3).First(&county).Error
		if err != nil {
			err = errors.New("区不存在")
			return
		}
		//因街道可能没有 这里变为有就判断 没有就不判断了
		if cloudOrderCreate.Address.Street != "" && cloudOrderCreate.Address.Street != "0" {
			err = source.DB().Where("id = ?", cloudOrderCreate.Address.Street).Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error
			if err != nil {
				err = errors.New("街道不存在或者不属于这个区" + err.Error())
				return
			}
		}
		cloudOrderCreate.Address.Province = province.Name
		cloudOrderCreate.Address.City = city.Name
		cloudOrderCreate.Address.Area = county.Name
		cloudOrderCreate.Address.Street = town.Name

	}
	//如果存在则是因为地址原因导致下单失败 这里需要保存一下地址下次直接对应这个地址
	if strings.Contains(cloudOrderModel.ErrorMsg, "云仓下单获取地址出错") == true {
		var cloudAreaMatching model2.CloudAreaMatching

		err, cloudAreaMatching = CloudAreaMatching(cloudOrderDetail.Order.Provice, cloudOrderDetail.Order.City, cloudOrderDetail.Order.District, cloudOrderDetail.Order.Street)

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("云仓自动下单,数据库错误-直接跳过", zap.Any("err", err))
			err = errors.New("云仓自动下单,数据库错误-直接跳过" + err.Error())
			return
		}
		//
		if cloudAreaMatching.ID == 0 {
			cloudAreaMatching.Province = cloudOrderCreate.Address.Province
			cloudAreaMatching.City = cloudOrderCreate.Address.City
			cloudAreaMatching.Area = cloudOrderCreate.Address.Area
			cloudAreaMatching.Street = cloudOrderCreate.Address.Street

			cloudAreaMatching.CloudProvince = cloudOrderDetail.Order.Provice
			cloudAreaMatching.CloudCity = cloudOrderDetail.Order.City
			cloudAreaMatching.CloudArea = cloudOrderDetail.Order.District
			cloudAreaMatching.CloudStreet = cloudOrderDetail.Order.Street
			err = source.DB().Model(&model2.CloudAreaMatching{}).Create(&cloudAreaMatching).Error
			if err != nil {
				err = errors.New("云仓下单:地址记录失败" + err.Error())
				log.Log().Error("云仓下单:地址记录失败", zap.Any("err", err))
				return
			}
		}
	}
	err, code = ManualCloudOrderCreate2(config, cloudOrderDetail, cloudOrderModel, cloudOrderCreate)
	if err != nil {
		var cloudOrderUpdate model2.CloudOrder
		log.Log().Error("云仓下单:下单支付发货错误", zap.Any("err", err))

		//支付错误 不变为-1 再次请求时直接执行支付
		//if code == 1 {
		//
		//} else {
		//
		//}
		switch code {
		case 1:
			cloudOrderUpdate.Status = -1
			cloudOrderUpdate.ErrorMsg = err.Error()
			source.DB().Where("id = ?", cloudOrderModel.ID).Updates(&cloudOrderUpdate)
			break
		case 3:
			cloudOrderUpdate.ErrorMsg = err.Error()

			source.DB().Where("id = ?", cloudOrderModel.ID).Updates(&cloudOrderUpdate)
			break
		default:
			//source.DB().Where("id = ?", cloudOrderModel.ID).First(&cloudOrderUpdate)
			var updateData = make(map[string]interface{})
			updateData["status"] = 0
			updateData["error_msg"] = err.Error()
			//code = 2 支付错误这里无需提示
			//cloudOrderUpdate.ErrorMsg = err.Error()
			//cloudOrderUpdate.Status = 0
			source.DB().Model(&model2.CloudOrder{}).Where("id = ?", cloudOrderModel.ID).Updates(&updateData)
			break
		}
	}
	return
}

// (云仓订单：自动下单步骤4  下单支付)
// code 错误类型 1正常错误,2支付错误  用于返回判断是否改云仓订单记录表状态为-1 1改 2不改
func ManualCloudOrderCreate2(config common.SupplySetting, cloudOrderDetail CloudOrderDetail, cloudOrder model2.CloudOrder, cloudOrderCreate request.ManualCloudOrderCreate) (err error, code int) {
	one := 1
	zero := 0
	code = 1
	//config.Cloud.CloudUserId =
	var confirmOrder ConfirmOrder

	var oldOrderData []model4.Order
	//因为存在中台拆单 所以这里查询多个
	source.DB().Where("third_order_sn = ?", cloudOrder.CloudOrderSn).Preload("OrderItems").Find(&oldOrderData)

	for _, goods := range cloudOrderCreate.Goods {
		var sku CloudSku
		for _, itemOrderGoods := range cloudOrderDetail.OrderGoodsDetail {

			if itemOrderGoods.PayStatus != 1 {
				var payStatusText = ""
				switch itemOrderGoods.PayStatus {
				case 0:
					payStatusText = "待付款"
					break
				case 2:
					payStatusText = "申请退款"
					break
				case 3:
					payStatusText = "退款中"
					break
				case 4:
					payStatusText = "已退款"
					break
				case 5:
					payStatusText = "退款申请失败"
					break
				}
				err = errors.New("云仓下单:云仓订单状态不满足下单条件:" + payStatusText)
				return
			}

			if goods.SkuId == 0 {
				err = errors.New("请选择规格")
				return
			}
			if goods.GoodsOrderSn == "" {
				err = errors.New("请提交子订单号参数")
				return
			}
			var cloudOrderItem model2.CloudOrderItem
			err = source.DB().Where("cloud_goods_order_sn = ?", goods.GoodsOrderSn).First(&cloudOrderItem).Error
			//如果出现错误不是查询不到 则直接返回报错
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Log().Error("手动下单,数据库错误-直接跳过", zap.Any("err", err))
				err = errors.New("手动下单,数据库错误-直接跳过" + err.Error())
				return
			}
			//存在记录代表已经同步过了 直接跳过
			if cloudOrderItem.ID != 0 {
				continue
			}
			//获取商品规格id参数
			if goods.GoodsOrderSn == itemOrderGoods.GoodsOrderSn {
				sku.SkuId = goods.SkuId
				sku.Number = itemOrderGoods.Total
				sku.CloudGoodsOptionTitle = itemOrderGoods.GoodsOptionTitle
				sku.CloudGoodsId = uint(itemOrderGoods.GoodsId)
				sku.CloudGoodsOptionId = itemOrderGoods.GoodsOptionId
				sku.CloudGoodsTitle = itemOrderGoods.Title
				sku.CloudGoodsOrderSn = itemOrderGoods.GoodsOrderSn

				sku.Status = itemOrderGoods.GoodsStatus
				sku.PayStatus = itemOrderGoods.PayStatus
				//用于支付成功之后 直接发货
				sku.ExpressCompanyName = itemOrderGoods.ExpressCompanyName
				sku.ExpressSn = itemOrderGoods.ExpressSn
				sku.ExpressCode = itemOrderGoods.ExpressCode
				var isCreate = 0 //如果需要下单，0需要1不需要
				//有可能出现返回报错但是有的商品下单成功这里过滤一下
				if len(oldOrderData) > 0 {
					for _, oldOrder := range oldOrderData {
						if isCreate == 1 {
							break
						}
						for _, oldOrderItem := range oldOrder.OrderItems {
							if oldOrderItem.SkuID == sku.SkuId {
								isCreate = 1
								if strings.Contains(cloudOrder.OrderSn, strconv.Itoa(int(oldOrder.OrderSN))+",") == false {
									cloudOrder.OrderSn += strconv.Itoa(int(oldOrder.OrderSN)) + ","
									cloudOrder.OrderId += strconv.Itoa(int(oldOrder.ID)) + ","
								}
								var cloudOrderItem1 model2.CloudOrderItem
								cloudOrderItem1.CloudOrderId = cloudOrder.ID
								cloudOrderItem1.OrderId = oldOrder.ID
								cloudOrderItem1.OrderSn = oldOrder.OrderSN
								cloudOrderItem1.OrderItemId = oldOrderItem.ID
								cloudOrderItem1.Total = oldOrderItem.Qty
								cloudOrderItem1.ProductId = oldOrderItem.ProductID
								cloudOrderItem1.SkuId = oldOrderItem.SkuID
								cloudOrderItem1.CloudGoodsOptionId = sku.CloudGoodsOptionId
								cloudOrderItem1.CloudGoodsTitle = sku.CloudGoodsTitle
								cloudOrderItem1.CloudGoodsOptionTitle = sku.CloudGoodsOptionTitle
								cloudOrderItem1.CloudGoodsOrderSn = sku.CloudGoodsOrderSn
								cloudOrderItem1.CloudGoodsId = sku.CloudGoodsId
								cloudOrderItem1.Status = sku.Status
								cloudOrderItem1.PayStatus = sku.PayStatus
								//如果未支付标记为支付
								if oldOrder.Status == model4.WaitPay {
									cloudOrderItem1.SupplyPayStatus = -1
								}
								err = source.DB().Create(&cloudOrderItem1).Error
								if err != nil {
									//如果记录表创建失败 直接关闭订单 并标记出错原因
									log.Log().Error("云仓下单:云仓订单记录子表创建失败", zap.Any("err", err), zap.Any("cloudOrderSn", cloudOrderDetail.Order.OrderSn), zap.Any("出错的记录:cloudOrderItem", cloudOrderItem))
								}
								break
							}
						}
					}
				}
				if isCreate == 0 {
					confirmOrder.Sku = append(confirmOrder.Sku, sku)
				}
			}
			//fmt.Println(sku)
			////如果没有匹配上
			//if sku.SkuId == 0 {
			//	err = errors.New("云仓下单:云仓订单商品规格与商品详情规格匹配不上,云仓商品id" + strconv.Itoa(itemOrderGoods.GoodsId))
			//	return
			//}
		}
	}
	if confirmOrder.Sku == nil {
		code = 3
		err = errors.New("没有匹配到商品或者选择的云仓子订单都已同步，订单id" + strconv.Itoa(cloudOrderDetail.Order.Id))
		return
	}
	//下单开始
	confirmOrder.Address.Consignee = cloudOrderDetail.Order.RealName
	confirmOrder.Address.Phone = cloudOrderDetail.Order.Mobile
	confirmOrder.Address.Province = cloudOrderCreate.Address.Province
	confirmOrder.Address.City = cloudOrderCreate.Address.City
	confirmOrder.Address.Area = cloudOrderCreate.Address.Area
	confirmOrder.Address.Street = cloudOrderCreate.Address.Street
	confirmOrder.Address.Description = cloudOrderDetail.Order.City + "," + cloudOrderDetail.Order.District + "," + cloudOrderDetail.Order.Street + "," + cloudOrderDetail.Order.Address

	//获取地址id
	err, addressId := service.GetOrSetAddress(request2.Address(confirmOrder.Address))

	if err != nil {
		log.Log().Error("云仓下单:云仓下单获取地址出错!", zap.Any("err", err))
		err = errors.New("云仓下单:云仓下单获取地址出错:" + err.Error())
		return
	}

	id, err := cache.GetID("api_buy")
	if err != nil {
		err = errors.New("云仓下单:buy_id生成失败:" + err.Error())
		log.Log().Error("云仓下单:buy_id生成失败!", zap.Any("err", err))
		return
	}
	buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
	//插入购物车记录
	for _, item := range confirmOrder.Sku {
		shoppingCart := shoppingCartModel.ShoppingCart{
			UserID:    config.Cloud.CloudUserId,
			SkuID:     item.SkuId,
			Qty:       item.Number,
			Status:    &zero,
			Checked:   &one,
			BuyID:     uint(buyID),
			BuyWay:    3,
			AddressID: addressId,
		}
		err = shoppingCartService.CreateShoppingCart(shoppingCart)
		if err != nil {
			log.Log().Error("云仓下单:购物车添加失败,SkuId:"+strconv.Itoa(int(item.SkuId)), zap.Any("err", err))
			err = errors.New("云仓下单:购物车添加失败,SkuId:" + strconv.Itoa(int(item.SkuId)) + "" + err.Error())
			return
		}
	}
	// 读取购物车记录
	err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: config.Cloud.CloudUserId, BuyID: uint(buyID)})
	if err != nil {
		log.Log().Error("云仓下单:获取购物车记录失败", zap.Any("err", err))
		err = errors.New("云仓下单:获取购物车记录失败:" + err.Error())
		return
	}

	if len(shoppingCarts) == 0 {
		log.Log().Error("云仓下单:没有购物车记录")
		err = errors.New("云仓下单:没有购物车记录")
		return
	}
	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(config.Cloud.CloudUserId, shoppingCarts)
	if err != nil {
		log.Log().Error("云仓下单:结算信息获取失败", zap.Any("err", err))
		err = errors.New("云仓下单:结算信息获取失败:" + err.Error())
		return
	}

	// 下单
	checkoutInfo.ThirdOrderSn = cloudOrderDetail.Order.OrderSn
	//如果云仓运费不是0则买家备注增加运费备注
	if cloudOrderDetail.Order.DispatchPrice != 0 {
		dispatchPrice := Fen2Yuan(cloudOrderDetail.Order.DispatchPrice)

		var remark = "云仓运费：" + dispatchPrice + "元"

		for orderK, _ := range checkoutInfo.Orders {
			checkoutInfo.Orders[orderK].Remark = remark
		}
	}
	err, confirmInfo := confirm.ShoppingCartConfirm(checkoutInfo)

	if err != nil {
		log.Log().Error("云仓下单:下单失败", zap.Any("err", err))
		err = errors.New("云仓下单:下单失败:" + err.Error())
		return
	}
	err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: config.Cloud.CloudUserId, BuyID: uint(buyID), BuyWay: 3})
	if err != nil {
		log.Log().Error("云仓下单:清空购物车记录", zap.Any("err", err))
		err = errors.New("云仓下单:清空购物车记录:" + err.Error())
		return
	}
	//下单结束
	//优先存储云仓子订单记录表数据
	for _, confirmInfoItem := range confirmInfo.Orders {
		cloudOrder.OrderSn += strconv.Itoa(int(confirmInfoItem.OrderSN)) + ","
		cloudOrder.OrderId += strconv.Itoa(int(confirmInfoItem.ID)) + ","
		for _, confirmInfoOrderItem := range confirmInfoItem.OrderItems {
			//var cloudOrderData  model2.CloudOrder
			//source.DB().Where("order_id = ",confirmInfoItem.ID).First(&cloudOrderData)
			var cloudOrderItem model2.CloudOrderItem
			cloudOrderItem.CloudOrderId = cloudOrder.ID
			cloudOrderItem.OrderId = confirmInfoItem.ID
			cloudOrderItem.OrderSn = confirmInfoItem.OrderSN
			cloudOrderItem.OrderItemId = confirmInfoOrderItem.ID
			cloudOrderItem.Total = confirmInfoOrderItem.Qty
			cloudOrderItem.ProductId = confirmInfoOrderItem.ProductID
			cloudOrderItem.SkuId = confirmInfoOrderItem.SkuID
			//拼接云仓商品订单号 用于发货 其他字段作用待定
			for _, confirmOrderSku := range confirmOrder.Sku {
				if confirmOrderSku.SkuId == confirmInfoOrderItem.SkuID {
					cloudOrderItem.CloudGoodsOptionId = confirmOrderSku.CloudGoodsOptionId
					cloudOrderItem.CloudGoodsTitle = confirmOrderSku.CloudGoodsTitle
					cloudOrderItem.CloudGoodsOptionTitle = confirmOrderSku.CloudGoodsOptionTitle
					cloudOrderItem.CloudGoodsOrderSn = confirmOrderSku.CloudGoodsOrderSn
					cloudOrderItem.CloudGoodsId = confirmOrderSku.CloudGoodsId
					cloudOrderItem.Status = confirmOrderSku.Status
					cloudOrderItem.PayStatus = confirmOrderSku.PayStatus
					break
				}
			}
			err = source.DB().Create(&cloudOrderItem).Error
			if err != nil {
				//如果记录表创建失败 直接关闭订单 并标记出错原因
				log.Log().Error("云仓下单:云仓订单记录子表创建失败", zap.Any("err", err), zap.Any("cloudOrderSn", cloudOrderDetail.Order.OrderSn), zap.Any("出错的记录:cloudOrderItem", cloudOrderItem))

				confirmInfoItem.Status = -1
				confirmInfoItem.Note = "云仓下单订单号" + cloudOrderDetail.Order.OrderSn + ":云仓子订单记录表创建失败" + err.Error()
				source.DB().Where("id = ?", confirmInfoItem.ID).Updates(&confirmInfoItem)

				err = errors.New("云仓下单:订单号:" + cloudOrderDetail.Order.OrderSn + ":云仓订单子记录表创建失败" + err.Error())
				return
			}
		}
	}
	//获取子订单总数
	var cloudOrderItemsCount int64
	source.DB().Model(&model2.CloudOrderItem{}).Where("cloud_order_id", cloudOrder.ID).Count(&cloudOrderItemsCount)
	if cloudOrderItemsCount >= int64(len(cloudOrderDetail.OrderGoodsDetail)) {
		cloudOrder.SynStatus = 3
	} else {
		cloudOrder.SynStatus = 2
	}
	//var GatherSuppliesId = cloudOrder.GatherSuppliesId
	//支付开始 //存在拆单可能 所以判断一下 如果拆弹重新创建一个 云仓订单记录表不在拆分
	//var key = 1
	cloudOrder.Status = 0

	err = source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder).Error
	//}
	//key++
	if err != nil {
		log.Log().Error("云仓下单:云仓订单记录表创建失败", zap.Any("err", err), zap.Any("cloudOrderSn", cloudOrderDetail.Order.OrderSn), zap.Any("出错的记录:cloudOrderItem", cloudOrder))
		err = errors.New("云仓下单:订单号" + cloudOrderDetail.Order.OrderSn + ":云仓订单记录表创建失败" + err.Error())
		return
	}
	for _, confirmInfoItem := range confirmInfo.Orders {
		//if key>1 {
		//	var cloudOrder model2.CloudOrder
		//	cloudOrder.Status = 0
		//	cloudOrder.CloudOrderId = cloudOrderDetail.Order.Id
		//	cloudOrder.CloudOrderSn = cloudOrderDetail.Order.OrderSn
		//	cloudOrder.CloudOrderTotalPrice = cloudOrderDetail.Order.OrderTotalPrice
		//	cloudOrder.GatherSuppliesId = GatherSuppliesId
		//	cloudOrder.OrderSn = confirmInfoItem.OrderSN
		//	cloudOrder.OrderId = confirmInfoItem.ID
		//	err = source.DB().Create(&cloudOrder).Error
		//}else{

		//var payInfo orderPay.PayInfo
		//err, payInfo = orderPay.GetPayInfo(config.Cloud.CloudUserId, []uint{confirmInfoItem.ID})
		//if err != nil {
		//	log.Log().Error("云仓下单:获取支付信息失败", zap.Any("err", err))
		//	err = errors.New("云仓下单:获取支付信息失败:" + err.Error())
		//	code = 2
		//	return
		//}
		//err, _ = payment.PayService(paymentModel.UpdateBalance{
		//	Uid:       confirmInfoItem.UserID,
		//	Amount:    confirmInfoItem.Amount,
		//	PayType:   2,
		//	PayInfoID: int(payInfo.ID),
		//})
		//if err != nil {
		//	log.Log().Error("云仓下单:支付失败", zap.Any("err", err))
		//	err = errors.New("云仓下单:支付失败:" + err.Error())
		//	code = 2
		//	return
		//}
		err = SynCloudOrderPay(config, confirmInfoItem)
		if err != nil {
			err1 := source.DB().Model(&model2.CloudOrderItem{}).Where("order_id = ?", confirmInfoItem.ID).Updates(&model2.CloudOrderItem{
				SupplyPayStatus: -1,
			}).Error
			if err1 != nil {
				log.Log().Error("云仓下单:支付失败保存失败状态失败", zap.Any("err", err1), zap.Any("order_id", confirmInfoItem.ID))
			}
			code = 2

			return
		}
		//支付成功改变云仓记录表状态变为云仓订单状态
		cloudOrder.Status = cloudOrderDetail.Order.Status
		source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrder)

	}

	//err = CloudOrderCreateStep5(config, cloudOrderDetail) //如果云仓订单已发货,则直接发货
	code = 2 //无论发货结果是什么都不需要改变云仓订单记录表状态为错误
	return
}

// 手动关联云仓与中台商品
func ProductRelevanceCloudGoods(config common.SupplySetting, searchData request.ProductRelevanceCloudGoods) (err error) {
	var product Product
	source.DB().Where("id = ?", searchData.ProductId).First(&product)
	if product.ID == 0 {
		err = errors.New("中台商品不存在")
		return
	}
	var res *common.APIResult
	res, err = common.RequestApi(string(common.CloudGetGoods), "POST", nil, g.Map{"goods_id": searchData.GoodsId}, config)
	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if res.Code != 1 {
		err = errors.New("云仓商品错误:" + res.Message)
		return
	}
	var cloudGoods1 model2.CloudGoods
	source.DB().Where("product_id = ? or cloud_product_id = ?", searchData.ProductId, searchData.GoodsId).First(&cloudGoods1)
	if cloudGoods1.ID != 0 {
		err = errors.New("中台商品或云仓商品已关联")
		return
	}
	var cloudGoods model2.CloudGoods
	cloudGoods.ProductId = searchData.ProductId
	cloudGoods.CloudProductId = searchData.GoodsId
	cloudGoods.GatherSuppliesId = searchData.GatherSuppliesId
	cloudGoods.IsUpdate = 1
	cloudGoods.IsDelete = 0
	err = source.DB().Create(&cloudGoods).Error
	return
}

type CountCloudGoods struct {
	Count     int  `json:"count"`
	ProductId uint `json:"product_id"`
}

// 删除中台推送重复的云仓商品
func DeleteCloudGoods(config common.SupplySetting, cloudCommon request.DeleteProductRelevanceCloudGoods) (errs string) {
	var countCloudGoods []CountCloudGoods
	var err error
	if cloudCommon.ProductId == 0 {
		err = source.DB().Model(&model2.CloudGoods{}).Select("count(*) as count,product_id").Where("gather_supplies_id", cloudCommon.GatherSuppliesId).Having("count > 1").Group("product_id").Find(&countCloudGoods).Error
	} else {
		err = source.DB().Model(&model2.CloudGoods{}).Select("count(*) as count,product_id").Where("product_id = ?", cloudCommon.ProductId).Where("gather_supplies_id", cloudCommon.GatherSuppliesId).Having("count > 1").Group("product_id").Find(&countCloudGoods).Error
	}
	if err != nil {
		errs = "不存在重复的商品"
		return
	}
	log.Log().Info("云仓批量删除执行参数", zap.Any("countCloudGoods", countCloudGoods))

	for _, item := range countCloudGoods {
		var cloudGoods []model2.CloudGoods
		err = source.DB().Model(&model2.CloudGoods{}).Where("product_id", item.ProductId).Where("gather_supplies_id", cloudCommon.GatherSuppliesId).Order("id asc").Find(&cloudGoods).Error
		if err != nil {
			continue
		}
		for k, item1 := range cloudGoods {
			//不删除第一个
			if k == 0 {
				continue
			}
			log.Log().Info("云仓批量删除执行参数", zap.Any("k", k), zap.Any("item1", item1))

			var res *common.APIResult

			var goodsOnsale request.GoodsOnsale
			goodsOnsale.IsOnsale = 0

			goodsOnsale.ThirdGoodsId = append(goodsOnsale.ThirdGoodsId, int(item1.CloudProductId))

			res, err = common.RequestApi(string(common.UpdateGoodsOnsale), "POST", nil, g.Map{"goods_id": goodsOnsale.ThirdGoodsId, "is_onsale": goodsOnsale.IsOnsale}, config)
			if err != nil {
				err = errors.New(err.Error())
				return
			}
			if res.Code != 1 {
				errs = "下架失败，原因:" + res.Message + "云仓商品id" + strconv.Itoa(int(item1.CloudProductId))
				continue
			}

			res, err = common.RequestApi(string(common.DeleteGoods), "POST", nil, g.Map{"goods_id": item1.CloudProductId}, config)
			if err != nil {
				err = errors.New(err.Error())
				return
			}
			if res.Code != 1 {
				errs = "删除失败，原因:" + res.Message + "云仓商品id" + strconv.Itoa(int(item1.CloudProductId))
				continue
			} else {
				//删除本地记录
				err = source.DB().Where("cloud_product_id = ?", item1.CloudProductId).Where("gather_supplies_id = ?", cloudCommon.GatherSuppliesId).Delete(&model2.CloudGoods{}).Error
			}

		}

	}
	return

}

func UpdateCloudOrderIsOffline(cloudCommon request.UpdateCloudOrderIsOffline) (err error) {
	var cloudOrder model2.CloudOrder
	err = source.DB().Where("id = ?", cloudCommon.CloudOrderId).First(&cloudOrder).Error
	if err != nil {
		err = errors.New("云仓订单记录不存在")
		return
	}
	cloudOrder.IsOffline = 1
	err = source.DB().Where("id = ?", cloudCommon.CloudOrderId).Updates(&cloudOrder).Error
	return
}

/*
*
修改推送/更新商品记录状态变为已处理
*/
func UpdateCloudPushGoodsMessageType(cloudCommon request.UpdateCloudPushGoodsMessageType) (err error) {
	var cloudOrder model2.CloudPushGoodsMessage
	err = source.DB().Where("id = ?", cloudCommon.CloudPushGoodsMessageId).First(&cloudOrder).Error
	if err != nil {
		err = errors.New("推送/更新商品记录不存在")
		return
	}
	cloudOrder.Type = 1
	err = source.DB().Where("id = ?", cloudCommon.CloudPushGoodsMessageId).Updates(&cloudOrder).Error
	return
}

// 清除一个月之前的商品同步记录日志
func ClearCloudPushGoodsMessages() {
	// 获取当前时间
	now := time.Now()
	var batchDeleteSize = 20000 //删除数量

	// 获取一个月之前的时间
	oneMonthAgo := now.AddDate(0, -1, 0) //删除一个月之前的数据

	DeleteCloudPushGoodsMessages(batchDeleteSize, oneMonthAgo)
}
func DeleteCloudPushGoodsMessages(batchDeleteSize int, oneMonthAgo time.Time) {
	res := source.DB().Where("created_at <= ?", oneMonthAgo).Limit(batchDeleteSize).Delete(&model2.CloudPushGoodsMessage{})
	if res.RowsAffected > 0 {
		DeleteCloudPushGoodsMessages(batchDeleteSize, oneMonthAgo)
	}
	return
}
