package cron

import (
	"go.uber.org/zap"
	"maiger-supply/component/goods"
	"maiger-supply/service"
	publicSupply "public-supply/model"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func UpdateMaiGerDatabaseCron() {
	// 获取所有京东VOP供应链
	var gatherList []publicSupply.GatherSupply
	if err := source.DB().Where("`category_id` = ?", 131).Find(&gatherList).Error; err != nil {
		return
	}

	// 循环添加京东VOP选品库自动同步任务
	for _, gather := range gatherList {
		addUpdateMaiGerDatabaseCron(gather.ID)
	}
}

func addUpdateMaiGerDatabaseCron(gatherId uint) {
	sysSetting, err := service.GetSetting(gatherId)
	if err != nil {
		log.Log().Error("迈戈选品库自动更新:查询配置失败", zap.Any("gatherId", gatherId), zap.Any("err", err))
		return
	}

	cronStr := "0 0 */2 * * *"
	if sysSetting.UpdateInfo.Cron != "" {
		cronStr = sysSetting.UpdateInfo.Cron
	}

	task := cron.Task{
		Key:  "maiGerDatabaseUpdate" + strconv.Itoa(int(gatherId)),
		Name: "迈戈选品库自动更新" + strconv.Itoa(int(gatherId)),
		Spec: cronStr,
		Handle: func(task cron.Task) {
			UpdateMaiGerDatabase(gatherId)
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func UpdateMaiGerDatabase(gatherId uint) {
	log.Log().Info("迈戈选品库自动更新", zap.Any("gatherId", gatherId))
	m := goods.MaiGerSupply{}
	if err := m.InitSetting(gatherId); err != nil {
		log.Log().Error("迈戈选品库自动更新:初始化配置失败", zap.Any("gatherId", gatherId), zap.Any("err", err))
		return
	}

	if err := m.InitGoods(); err != nil {
		log.Log().Error("迈戈选品库自动更新:执行更新失败", zap.Any("gatherId", gatherId), zap.Any("err", err))
		return
	}
}
