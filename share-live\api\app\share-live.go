package app

import (
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"share-live/common"
	"share-live/model"
	"share-live/request"
	"share-live/service"
	"share-live/setting"
	"strconv"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

// Send
// @Tags 共享直播
// @Summary 保存累计观看人数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "保存累计观看人数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/saveTotalNum [post]
func SaveTotalNum(c *gin.Context) {
	var saveTotalNumRequest request.SaveTotalNumRequest
	err := c.ShouldBind<PERSON>(&saveTotalNumRequest)
	if saveTotalNumRequest.Num == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交增加的观看人数", c)
		return
	}
	if saveTotalNumRequest.ShareLiveRoomId == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交增加人数的直播间", c)
		return
	}
	appID := utils.GetAppID(c)

	err = service.SaveTotalNum(appID, saveTotalNumRequest)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
	return
}

// Send
// @Tags 共享直播
// @Summary 增加点赞人数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "增加点赞人数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/addLikeNum [post]
func AddLikeNum(c *gin.Context) {
	var saveTotalNumRequest request.SaveTotalNumRequest
	err := c.ShouldBindJSON(&saveTotalNumRequest)
	if saveTotalNumRequest.Num == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交增加的点赞人数", c)
		return
	}
	if saveTotalNumRequest.ShareLiveRoomId == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交增加点赞人数的直播间", c)
		return
	}
	appID := utils.GetAppID(c)

	err = service.UpdateLikeNum(appID, saveTotalNumRequest, 1)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
	return
}

// Send
// @Tags 共享直播
// @Summary 增加点赞人数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "增加点赞人数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/reduceLikeNum [post]
func ReduceLikeNum(c *gin.Context) {
	var saveTotalNumRequest request.SaveTotalNumRequest
	err := c.ShouldBindJSON(&saveTotalNumRequest)
	if saveTotalNumRequest.Num == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交减少的点赞人数", c)
		return
	}
	if saveTotalNumRequest.ShareLiveRoomId == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交减少点赞人数的直播间", c)
		return
	}
	appID := utils.GetAppID(c)

	err = service.UpdateLikeNum(appID, saveTotalNumRequest, 2)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
	return
}

// Send
// @Tags 共享直播
// @Summary 增加导入记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "增加导入记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/createShareLiveRoomApplication [post]
func CreateShareLiveRoomApplication(c *gin.Context) {
	var shareLiveRoomApplication model.ShareLiveRoomApplication
	err := c.ShouldBindJSON(&shareLiveRoomApplication)
	if shareLiveRoomApplication.ShareLiveRoomId == 0 {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交直播间标识", c)
		return
	}

	appID := utils.GetAppID(c)
	shareLiveRoomApplication.ApplicationId = appID

	err = service.CreateShareLiveRoomApplication(shareLiveRoomApplication)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
	return
}

// Send
// @Tags 共享直播
// @Summary im用户登录密码UserSig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "增加导入记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/genSig [post]
func GenSig(c *gin.Context) {
	var genSigRequest request.GenSigRequest
	err := c.ShouldBindJSON(&genSigRequest)
	if genSigRequest.Uid == "" {
		yzResponse.FailWithMessage("请提交用户唯一身份标识（商城端提供的）", c)
		return
	}
	if genSigRequest.ShareLiveRoomId == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}

	appID := utils.GetAppID(c)

	err, shareLiveRoomApp := service.VerifyShareLiveRoomApplication(genSigRequest.ShareLiveRoomId, appID)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, im := common.Initial()
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	genSigRequest.Uid = im.Config.Value.SerialNumber + "_" + strconv.Itoa(int(shareLiveRoomApp.ShareLiveRoomId)) + "_" + strconv.Itoa(int(appID)) + "_" + genSigRequest.Uid
	err, result := im.GenSig(genSigRequest.Uid)

	if err != nil {
		log.Log().Error("im用户登录密码UserSig请求失败", zap.Any("err", err))
		yzResponse.FailWithMessage("im用户登录密码UserSig请求失败"+err.Error(), c)
		return
	}
	if result.Code != 200 {
		log.Log().Error("im用户登录密码UserSig请求失败", zap.Any("err", err))
		yzResponse.FailWithMessage("im用户登录密码UserSig请求失败"+result.Msg, c)
		return
	}

	//序号_直播间id_配置id_会员id
	yzResponse.OkWithData(gin.H{"data": result.Data, "user_id": genSigRequest.Uid}, c)
	return
}

// Send
// @Tags 共享直播
// @Summary 群组系统消息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "群组系统消息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/shareLive/genSig [post]
func SendGroupMsg(c *gin.Context) {
	var sendGroupMsgRequest request.SendGroupMsgRequest
	err := c.ShouldBindJSON(&sendGroupMsgRequest)

	if sendGroupMsgRequest.ShareLiveRoomId == 0 {
		yzResponse.FailWithMessage("请提交直播间id", c)
		return
	}

	appID := utils.GetAppID(c)

	err, shareLiveRoomApp := service.VerifyShareLiveRoomApplication(sendGroupMsgRequest.ShareLiveRoomId, appID)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, im := common.Initial()
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, result := im.SendGroupMsg(sendGroupMsgRequest, shareLiveRoomApp.ShareLiveRoom.GroupId)

	if err != nil {
		log.Log().Error("群组系统消息", zap.Any("err", err))
		yzResponse.FailWithMessage("群组系统消息"+err.Error(), c)
		return
	}
	if result.Code != 200 {
		log.Log().Error("群组系统消息", zap.Any("err", err))
		yzResponse.FailWithMessage("群组系统消息"+result.Msg, c)
		return
	}
	yzResponse.OkWithData(result.Data, c)
	//{"code":200,"msg":"","data":{"ActionStatus":"OK","ErrorCode":0,"ErrorInfo":"","MsgSeq":1,"MsgTime":1690421984}}
	//{"code":200,"msg":"","data":{"ActionStatus":"FAIL","ErrorCode":10004,"ErrorInfo":"invalid msg"}}
	return
}

// createShareLiveCategory
// @Tags 共享直播
// @Summary 通过直播间id获取回放
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "通过直播间id获取回放"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/getShareLiveRoomRecordFileByRoomId [get]
func GetShareLiveRoomRecordFileByRoomId(c *gin.Context) {
	var shareLiveRoomIdRequest model.ShareLiveRoomRecordFile
	err := c.ShouldBindQuery(&shareLiveRoomIdRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	appID := utils.GetAppID(c)
	//校验是否导入
	err, _ = service.VerifyShareLiveRoomApplication(shareLiveRoomIdRequest.ShareLiveRoomId, appID)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, room := service.GetShareLiveRoomById(shareLiveRoomIdRequest.ShareLiveRoomId)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if room.IsTranscribe == 0 {
		yzResponse.FailWithMessage("暂无录制", c)
		return
	}
	if room.IsPlayback != 1 {
		yzResponse.FailWithMessage("未开启回放", c)
		return
	}

	err, data := service.GetShareLiveRoomRecordFileByRoomId(shareLiveRoomIdRequest.ShareLiveRoomId)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}

// @Tags 共享直播
// @Summary 共享直播直播间列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "共享直播直播间列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /shareLive/GetShareLiveRoomList [get]
func GetShareLiveRoomList(c *gin.Context) {
	var pageInfo request.ShareLiveRoomSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	appID := utils.GetAppID(c)
	pageInfo.ApplicationId = appID
	var isOpen = 0
	pageInfo.IsOpen = &isOpen
	if err, list, total := service.GetShareLiveRoomList(pageInfo, 1); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 共享直播API
// @Summary 获取共享直播配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取共享直播配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /shareLive/getSysShareLiveSetting [get]
func GetSysShareLiveSetting(c *gin.Context) {
	var sys setting.SysSetting
	_ = c.ShouldBindJSON(&sys)
	err, shareLiveSetting := setting.GetSysShareLiveSetting()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(gin.H{"is_open": shareLiveSetting.Value.IsOpen, "sdkappid": shareLiveSetting.Value.AppId, "serial_number": shareLiveSetting.Value.SerialNumber}, c)

}
