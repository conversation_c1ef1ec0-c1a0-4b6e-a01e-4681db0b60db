package v1

import (
	"fmt"
	"gin-vue-admin/cmd/gva"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"math"
	"order/model"
	address2 "region/address"
	"shipping/address"
	"shipping/freight"
	"shipping/shipping"
	shoppingCartModel "shopping-cart/model"
	shoppingCartService "shopping-cart/service"
	"strconv"
	"time"
	"trade/app_trade"
	"trade/cashier"
	"trade/checkout"
	"trade/confirm"
	"trade/request"
	ufv1 "user/api/f/v1"
	"yz-go/cache"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

type ConfirmRequest struct {
	BuyID  uint           `json:"buy_id"`
	Orders []OrderRequest `json:"orders"` // 订单
}

type ShoppingCartCheckoutRequest struct {
	BuyID  uint           `json:"buy_id"`
	Batch  int            `json:"batch"`
	BuyWay int            `json:"buy_way"`
	Orders []OrderRequest `json:"orders"` // 订单
}
type OrderRequest struct {
	Key              string          `json:"key"`                // 标识
	ShippingMethodID int             `json:"shipping_method_id"` // 配送方式id
	AddressID        uint            `json:"address_id"`         // 收货地址
	OrderItems       []OrderItem     `json:"order_items"`        // 订单条目
	Remark           string          `json:"remark"`             // 买家备注
	OrderBill        model.OrderBill `json:"order_bill"`         // 发票信息
	ThirdOrderSN     string          `json:"third_order_sn"`     // 第三方订单号
}

type OrderItem struct {
	ShoppingCartID uint `json:"shopping_cart_id"` // 购物车id
}
type ProductCheckoutRequest struct {
	Items []Item `json:"items"` // 结算条目
}
type ProductFreightRequest struct {
	SkuID      uint   `json:"sku_id"` // sku id
	Qty        uint   `json:"qty"`    // 数量
	Realname   string `json:"realname"`
	Mobile     string `json:"mobile"`
	ProvinceId int    `json:"province_id"` // 省id
	CityId     int    `json:"city_id"`     // 市id
	CountyId   int    `json:"county_id"`   // 区id
	TownId     int    `json:"town_id"`     // 街id
	Province   string `json:"province" `   // 省
	City       string `json:"city"`        // 市
	County     string `json:"county"`      // 区
	Town       string `json:"town"`        // 街
	Detail     string `json:"detail"`      // 地址
}

type Item struct {
	SkuID     uint `json:"sku_id"`     // sku id
	Qty       uint `json:"qty"`        // 数量
	AddressID uint `json:"address_id"` // 收货地址
}

// GetProductFreight
// @Tags 交易
// @Summary 获取商品运费
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ProductFreightRequest true "获取商品运费"
// @Success 200 {object} interface
// @Router /api/trade/getProductFreight [post]
func GetProductFreight(c *gin.Context) {
	var productFreightRequest ProductFreightRequest
	err := c.ShouldBindJSON(&productFreightRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var sku checkout.Sku
	var product checkout.Product
	err = source.DB().Model(&sku).Where("id = ?", productFreightRequest.SkuID).First(&sku).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err = source.DB().Model(&product).Where("id = ?", sku.ProductID).First(&product).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 阿里精选需要收获人、手机号参数
	// 按韦平要求，查询商品运费时，无收货人、手机号参数时，默认默认收货地址（假数据）
	if productFreightRequest.Realname == "" {
		productFreightRequest.Realname = "韦平"
	}
	if productFreightRequest.Mobile == "" {
		productFreightRequest.Mobile = "15216771448"
	}

	var freightDetail freight.Freight
	shippingItems := freight.Items{freight.Item{
		Amount:            sku.Price * productFreightRequest.Qty,
		Qty:               productFreightRequest.Qty,
		Title:             product.Title,
		Weight:            sku.Weight,
		Long:              product.Long,
		Wide:              product.Wide,
		High:              product.High,
		Volume:            product.Volume,
		Freight:           product.Freight,
		FreightType:       product.FreightType,
		FreightTemplateID: product.FreightTemplateID,
		GatherSupplyID:    product.GatherSupplyID,
		OriginalSkuID:     sku.OriginalSkuID,
		ID:                sku.ID,
		Address: address.ShippingAddress{
			Address: address2.Address{
				Realname:   productFreightRequest.Realname,
				Mobile:     productFreightRequest.Mobile,
				ProvinceId: productFreightRequest.ProvinceId,
				CityId:     productFreightRequest.CityId,
				CountyId:   productFreightRequest.CountyId,
				TownId:     productFreightRequest.TownId,
				Province:   productFreightRequest.Province,
				City:       productFreightRequest.City,
				County:     productFreightRequest.County,
				Town:       productFreightRequest.Town,
				Detail:     productFreightRequest.Detail,
			},
		}},
	}

	err, freightDetail = freight.GetItemsFreightDetail(shippingItems)
	if err != nil {
		return
	}
	// 配送信息
	shippingInfo := shipping.ShippingInfo{ShippingMethodID: 1, Freight: freightDetail}
	var amount uint
	var desc string
	amount = shippingInfo.GetFreight()
	desc = shippingInfo.GetDesc()
	yzResponse.OkWithDetailed(map[string]string{"freight": strconv.Itoa(int(amount)), "desc": desc}, "获取成功", c)
	return
}

// ProductCheckout
// @Tags 交易
// @Summary 立刻购买
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ProductCheckoutRequest true "获取结算信息"
// @Success 200 {object} checkout.Checkout
// @Router /api/trade/buy [post]
func ProductCheckout(c *gin.Context) {
	var checkoutRequest ProductCheckoutRequest
	err := c.ShouldBindJSON(&checkoutRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	one := 1
	zero := 0
	id, err := cache.GetID("product_buy")
	if err != nil {
		log.Log().Error("buy_id生成失败", zap.Any("err", err))
		yzResponse.FailWithMessage("buy_id生成失败", c)
		return
	}
	// 7.15不再使用默认收货地址
	//var userDefaultAddress userModel.Address
	//err = source.DB().Where("user_id = ? AND is_default = ?", userID, 1).First(&userDefaultAddress).Error
	//if err == nil {
	//	defaultAddressID = userDefaultAddress.ID
	//}
	buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
	for _, item := range checkoutRequest.Items {
		shoppingCart := shoppingCartModel.ShoppingCart{
			UserID:           userID,
			SkuID:            item.SkuID,
			Qty:              item.Qty,
			Status:           &zero,
			Checked:          &one,
			BuyID:            uint(buyID),
			BuyWay:           1,
			AddressID:        item.AddressID,
			ShippingMethodID: 1,
		}
		err = shoppingCartService.CreateShoppingCart(shoppingCart)
		if err != nil {
			log.Log().Error("添加失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("添加失败", c)
			return
		}
	}
	// 立即购买的购物车记录
	err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID), BuyWay: 1})
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if len(shoppingCarts) == 0 {
		//  没有购物车记录
		log.Log().Error("请选择要结算的商品")
		yzResponse.FailWithMessage("请选择要结算的商品", c)
		return
	}
	var shoppingCartIDs []uint
	for _, shoppingCart := range shoppingCarts {
		shoppingCartIDs = append(shoppingCartIDs, shoppingCart.ID)
	}
	err = shoppingCartService.CheckBeforeSale(shoppingCartIDs)
	if err != nil {
		return
	}
	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(userID, shoppingCarts)
	if err != nil {
		for _, failedOrder := range checkoutInfo.FailedOrders {
			for _, shoppingCart := range failedOrder.ShoppingCarts {
				shoppingCartErr := source.DB().Model(&shoppingCartModel.ShoppingCart{}).Where("id = ?", shoppingCart.ID).Updates(map[string]interface{}{"is_expired": 1, "checked": 0, "expired_message": failedOrder.ErrorMessage}).Error
				if shoppingCartErr != nil {
					log.Log().Error("更新购物车失效失败", zap.Any("shoppingCart", shoppingCart), zap.Error(shoppingCartErr))
				}
			}
		}
		log.Log().Error("订单结算失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	checkoutInfo.BuyID = uint(buyID)
	yzResponse.OkWithDetailed(checkoutInfo, "获取成功", c)
	return
}

// ShoppingCartCheckout
// @Tags 交易
// @Summary 购物车结算
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ShoppingCartCheckoutRequest true "获取结算信息"
// @Success 200 {object} checkout.Checkout
// @Router /api/trade/checkout [post]
func ShoppingCartCheckout(c *gin.Context) {
	var checkoutRequest ShoppingCartCheckoutRequest
	err := c.ShouldBindJSON(&checkoutRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	// 修改购物车记录收货地址和配送方式
	for _, order := range checkoutRequest.Orders {
		for _, orderItem := range order.OrderItems {
			shoppingCart := shoppingCartModel.ShoppingCart{}
			shoppingCart.ID = orderItem.ShoppingCartID
			shoppingCart.AddressID = order.AddressID
			shoppingCart.ShippingMethodID = order.ShippingMethodID
			shoppingCart.BuyWay = 0
			err = shoppingCartService.UpdateShoppingCart(shoppingCart)
			if err != nil {
				log.Log().Error("获取失败", zap.Any("err", err))
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
		}
	}

	var checkoutShoppingCart checkout.ShoppingCart
	checkoutShoppingCart.UserID = userID
	checkoutShoppingCart.BuyID = checkoutRequest.BuyID
	checkoutShoppingCart.BuyWay = checkoutRequest.BuyWay
	// 批量下单参数,查询批量下单产生的购物车数据
	if checkoutRequest.Batch == 1 {
		checkoutShoppingCart.BuyWay = gva.BATCHORDERBUYWAY
	}
	// 选中的购物车记录
	err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkoutShoppingCart)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if len(shoppingCarts) == 0 {
		//  没有购物车记录
		log.Log().Error("请选择要结算的商品", zap.Any("err", err))
		yzResponse.FailWithMessage("请选择要结算的商品", c)
		return
	}
	log.Log().Info("购物车结算数据", zap.Any("info", shoppingCarts))
	var shoppingCartIDs []uint
	for _, shoppingCart := range shoppingCarts {
		shoppingCartIDs = append(shoppingCartIDs, shoppingCart.ID)
	}
	err = shoppingCartService.CheckBeforeSale(shoppingCartIDs)
	if err != nil {
		log.Log().Error("购物车结算失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(userID, shoppingCarts)
	checkoutInfo.BuyID = checkoutRequest.BuyID
	if err != nil {
		log.Log().Error("订单结算失败", zap.Any("err", err))
		yzResponse.FailWithDetailed(checkoutInfo.FailedShoppingCart, err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(checkoutInfo, "获取成功", c)
	return
}

// ShoppingCartConfirm
// @Tags 交易
// @Summary 购物车下单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} cashier.CashierInfo
// @Router /api/trade/confirm [post]
func ShoppingCartConfirm(c *gin.Context) {
	var err error
	var confirmRequest ConfirmRequest
	err = c.ShouldBindJSON(&confirmRequest)
	if err != nil {
		log.Log().Error("参数转义失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)
	// 选中的购物车记录
	err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: confirmRequest.BuyID})
	if err != nil {
		log.Log().Error("查询购物车失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if len(shoppingCarts) == 0 {
		//  没有购物车记录
		log.Log().Error("购物车数量为0", zap.Any("err", err))
		yzResponse.FailWithMessage("请选择要结算的商品", c)
		return
	}
	for _, item := range shoppingCarts {
		if item.AddressID == 0 {
			log.Log().Error(fmt.Sprintf("下单失败shopping_cart_id:%d为0", item.ID), zap.Any("err", err))
			yzResponse.FailWithMessage(fmt.Sprintf("商品(%s %s)未选择下单地址", item.Product.Title, item.Sku.Title), c)
			return
		}
	}
	//验证是否是这个API能购买的商品
	var tradeConfirmRequest request.ConfirmRequest
	for _, item := range shoppingCarts {
		var spu request.Spu
		spu.Sku = item.SkuID
		tradeConfirmRequest.Spu = append(tradeConfirmRequest.Spu, spu)
	}
	err = app_trade.IsApiBuy(tradeConfirmRequest)
	if err != nil {
		log.Log().Error("IsApiBuy", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	log.Log().Info("购物车下单数据", zap.Any("info", shoppingCarts))
	var shoppingCartIDs []uint
	for _, shoppingCart := range shoppingCarts {
		shoppingCartIDs = append(shoppingCartIDs, shoppingCart.ID)
	}
	err = shoppingCartService.CheckBeforeSale(shoppingCartIDs)
	if err != nil {
		log.Log().Error("CheckBeforeSale失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(userID, shoppingCarts)
	if err != nil {
		log.Log().Error("ShoppingCartCheckout失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if checkoutInfo.CanConfirm == false {
		log.Log().Error("下单false失败", zap.Any("err", checkoutInfo.Messages))
		yzResponse.FailWithDetailed(gin.H{"messages": checkoutInfo.Messages}, "下单失败", c)
		return
	}
	for i, order := range checkoutInfo.Orders {
		for _, orderRequest := range confirmRequest.Orders {
			if order.Key == orderRequest.Key {
				checkoutInfo.Orders[i].Remark = orderRequest.Remark
				checkoutInfo.Orders[i].OrderBill = orderRequest.OrderBill
				checkoutInfo.Orders[i].ThirdOrderSN = orderRequest.ThirdOrderSN
			}
		}
	}
	// 下单
	err, confirmInfo := confirm.ShoppingCartConfirm(checkoutInfo)
	if err != nil {
		log.Log().Error("ShoppingCartConfirm失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: confirmRequest.BuyID})
	if err != nil {
		log.Log().Error("ClearCheckedShoppingCarts失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 收银台信息
	var orderIDs []uint
	for _, o := range confirmInfo.Orders {
		orderIDs = append(orderIDs, o.ID)
	}
	yzResponse.OkWithDetailed(gin.H{"order_ids": orderIDs}, "操作成功", c)
	return
}

// Cashier
// @Tags 交易
// @Summary 收银台
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body CashierRequest true "收银台"
// @Success 200 {object} cashier.CashierInfo
// @Router /api/trade/cashier [post]
func Cashier(c *gin.Context) {
	var cashierRequest CashierRequest
	err := c.ShouldBindJSON(&cashierRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	err, cashierInfo := cashier.GetCashierInfo(userID, cashierRequest.OrderIDs)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(cashierInfo, "操作成功", c)
	return
}

type CashierRequest struct {
	OrderIDs []uint `json:"order_ids"`
}

func ShoppingCartConfirmByReorder(c *gin.Context) {
	var err error
	var confirmRequest ConfirmRequest
	err = c.ShouldBindJSON(&confirmRequest)
	if err != nil {
		log.Log().Error("参数转义失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)
	// 选中的购物车记录
	err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: confirmRequest.BuyID, BuyWay: 4})
	if err != nil {
		log.Log().Error("查询购物车失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if len(shoppingCarts) == 0 {
		//  没有购物车记录
		log.Log().Error("购物车数量为0", zap.Any("err", err))
		yzResponse.FailWithMessage("请选择要结算的商品", c)
		return
	}
	for _, item := range shoppingCarts {
		if item.AddressID == 0 {
			log.Log().Error(fmt.Sprintf("下单失败shopping_cart_id:%d为0", item.ID), zap.Any("err", err))
			yzResponse.FailWithMessage(fmt.Sprintf("商品(%s %s)未选择下单地址", item.Product.Title, item.Sku.Title), c)
			return
		}
	}
	//验证是否是这个API能购买的商品
	var tradeConfirmRequest request.ConfirmRequest
	for _, item := range shoppingCarts {
		var spu request.Spu
		spu.Sku = item.SkuID
		tradeConfirmRequest.Spu = append(tradeConfirmRequest.Spu, spu)
	}
	err = app_trade.IsApiBuy(tradeConfirmRequest)
	if err != nil {
		log.Log().Error("IsApiBuy", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	log.Log().Info("购物车下单数据", zap.Any("info", shoppingCarts))
	var shoppingCartIDs []uint
	for _, shoppingCart := range shoppingCarts {
		shoppingCartIDs = append(shoppingCartIDs, shoppingCart.ID)
	}
	err = shoppingCartService.CheckBeforeSale(shoppingCartIDs)
	if err != nil {
		log.Log().Error("CheckBeforeSale失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(userID, shoppingCarts)
	if err != nil {
		log.Log().Error("ShoppingCartCheckout失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if checkoutInfo.CanConfirm == false {
		log.Log().Error("下单false失败", zap.Any("err", checkoutInfo.Messages))
		yzResponse.FailWithDetailed(gin.H{"messages": checkoutInfo.Messages}, "下单失败", c)
		return
	}
	for i, order := range checkoutInfo.Orders {
		for _, orderRequest := range confirmRequest.Orders {
			if order.Key == orderRequest.Key {
				checkoutInfo.Orders[i].Remark = orderRequest.Remark
				checkoutInfo.Orders[i].OrderBill = orderRequest.OrderBill
				checkoutInfo.Orders[i].ThirdOrderSN = orderRequest.ThirdOrderSN
			}
		}
	}
	// 下单
	err, confirmInfo := confirm.ShoppingCartConfirm(checkoutInfo)
	if err != nil {
		log.Log().Error("ShoppingCartConfirm失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: confirmRequest.BuyID, BuyWay: 4})
	if err != nil {
		log.Log().Error("ClearCheckedShoppingCarts失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 收银台信息
	var orderIDs []uint
	for _, o := range confirmInfo.Orders {
		orderIDs = append(orderIDs, o.ID)
	}
	yzResponse.OkWithDetailed(gin.H{"order_ids": orderIDs}, "操作成功", c)
	return
}
