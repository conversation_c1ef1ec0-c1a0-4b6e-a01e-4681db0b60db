package service

import (
	"errors"
	live "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/live/v20180801"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"math"
	model3 "product/model"
	"product/other"
	productService "product/service"
	"share-live/common"
	"share-live/model"
	"share-live/request"
	"share-live/response"
	"share-live/setting"
	smallShopModel "small-shop/model"
	smallShopService "small-shop/service"
	"strconv"
	"time"
	"user/level"
	model2 "user/model"
	"yz-go/component/log"
	"yz-go/source"
)

// SmallShop 小商店
type SmallShop struct {
	source.Model
	Title           string `json:"title" form:"title" gorm:"column:title;comment:小商店名称;type:varchar(500);size:500;"`
	Uid             uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	UserLevelID     uint   `json:"user_level_id" form:"user_level_id" gorm:"column:user_level_id;comment:会员等级id;"`
	OrderPriceSum   uint   `json:"order_price_sum" gorm:"column:order_price_sum;comment:订单金额;"`
	OrderCount      int64  `json:"order_count" gorm:"column:order_count;comment:订单数量;"`
	AllSettleSum    uint   `json:"all_settle_sum" gorm:"column:all_settle_sum;comment:总收益;"`
	FinishSettleSum uint   `json:"finish_settle_sum" gorm:"column:finish_settle_sum;comment:已结算奖励;"`
	WaitSettleSum   uint   `json:"wait_settle_sum" gorm:"column:wait_settle_sum;comment:未结算奖励;"`
	Status          int    `json:"status" gorm:"column:status;comment:商店状态 0:关闭 1：开启;"`
}

/*
*

	共享直播分类列表
*/
func GetShareLiveCategoryList(info request.ShareLiveCategorySearch) (err error, list []model.ShareLiveCategory, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ShareLiveCategory{})
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Title != "" {
		db.Where("title like '%" + info.Title + "%'")
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Order("sort desc").Order("id desc").Offset(offset).Find(&list).Error
	return err, list, total
}

/*
*

	获取所有分类
*/
func GetShareLiveCategorys(info request.ShareLiveCategorySearch) (err error, list []model.ShareLiveCategory) {

	db := source.DB().Model(&model.ShareLiveCategory{})
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Title != "" {
		db.Where("title like '%" + info.Title + "%'")
	}
	err = db.Order("sort desc").Order("id desc").Find(&list).Error
	return err, list
}

/*
*

	获取小商店开启的的直播的分类
*/
func GetShareLiveCategorysBySmallShopId(info request.ShareLiveCategorysBySmallShopId) (err error, list []model.ShareLiveCategory) {
	if info.Sid == 0 {
		err = errors.New("请提交小商店id")
		return
	}

	var shareLiveRoomIds []uint
	err = source.DB().Model(&model.ShareLiveRoomSmallShop{}).Where("status = 1").Where("small_shop_id = ?", info.Sid).Pluck("share_live_room_id", &shareLiveRoomIds).Error
	if err != nil {
		return nil, list
	}
	if len(shareLiveRoomIds) == 0 {
		return nil, list
	}
	var shareLiveCategoryIds []uint
	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id in ?", shareLiveRoomIds).Group("share_live_category_id").Pluck("share_live_category_id", &shareLiveCategoryIds).Error
	if err != nil {
		return nil, list
	}
	if len(shareLiveCategoryIds) == 0 {
		return nil, list
	}
	// 创建db
	db := source.DB().Model(&model.ShareLiveCategory{}).Where("id in ?", shareLiveCategoryIds)

	err = db.Order("sort desc").Order("id desc").Find(&list).Error
	return err, list
}

// 删除分类
func DeleteShareLiveCategory(category model.ShareLiveCategory) (err error) {

	err = source.DB().Where("id = ?", category.ID).Delete(&category).Error

	return
}

// 创建分类
func CreateShareLiveCategory(category model.ShareLiveCategory) (err error) {
	if category.Title == "" {
		err = errors.New("请提交分类名称")
		return
	}
	err = source.DB().Create(&category).Error
	return
}

// 修改分类
func SaveShareLiveCategory(category model.ShareLiveCategory) (err error) {
	if category.Title == "" {
		err = errors.New("请提交分类名称")
		return
	}
	if category.ID == 0 {
		err = errors.New("请提交分类ID")
		return
	}
	err = source.DB().Where("id = ?", category.ID).Save(&category).Error
	return
}

// 获取分类信息
func GetShareLiveCategoryById(id uint) (err error, data model.ShareLiveCategory) {
	if id == 0 {
		err = errors.New("请提交分类ID")
		return
	}
	err = source.DB().Where("id = ?", id).First(&data).Error
	return
}

// 创建直播间
func CreateShareLiveRoom(roomRequest request.CreateShareLiveRoomRequest) (err error) {
	if roomRequest.ShareLiveRoom.Title == "" {
		err = errors.New("请输入直播间标题")
		return
	}
	if roomRequest.ShareLiveRoom.ShareLiveCategoryId == 0 {
		err = errors.New("请选择直播间分类")
		return
	}
	if roomRequest.ShareLiveRoom.UserId == 0 {
		err = errors.New("请选择主播会员")
		return
	}
	if roomRequest.ShareLiveRoom.ImageUrl == "" {
		err = errors.New("请提交直播封面")
		return
	}
	//检验是否配置了基础设置
	err, _ = common.VerifySysShareLiveSetting()
	if err != nil {
		return
	}
	//roomRequest.PlugFlowUrl =
	err = source.DB().Model(&model.ShareLiveRoom{}).Create(&roomRequest.ShareLiveRoom).Error
	if err != nil {
		err = errors.New("创建失败" + err.Error())
		return
	}
	if len(roomRequest.ProductIds) > 0 {
		var ProductRooms []model.ShareLiveRoomProduct
		for _, item := range roomRequest.ProductIds {
			var ProductRoom model.ShareLiveRoomProduct
			ProductRoom.ProductId = item
			ProductRoom.ShareLiveRoomId = roomRequest.ShareLiveRoom.ID
			ProductRooms = append(ProductRooms, ProductRoom)
		}
		err = source.DB().Model(&model.ShareLiveRoomProduct{}).Create(&ProductRooms).Error
		if err != nil {
			err = errors.New("商品关联失败" + err.Error())
			return
		}
	}
	//im
	err = CreateImGroup(roomRequest.ShareLiveRoom)
	if err != nil {
		err = errors.New("直播间创建成功:" + err.Error())
		return
	}
	//后续看情况是否变为协程方式
	msg := CreateOrSaveSynShareLiveRoomSmallShop(roomRequest.ShareLiveRoom.ID)
	if msg != "" {
		log.Log().Error("直播间创建成功:自动同步到小商店失败", zap.Any("err", msg))
		//err = errors.New("创建成功," + msg)
		return
	}
	return
}

// 创建修改时对设置一键同步的自动保存记录状态和提交商品到小商店
func CreateOrSaveSynShareLiveRoomSmallShop(ShareLiveRoomId uint) (msg string) {
	var shareLiveSmallShopSyns []model.ShareLiveSmallShopSyn
	var err error
	source.DB().Model(&model.ShareLiveSmallShopSyn{}).Where("is_syn = ?", 1).Find(&shareLiveSmallShopSyns)
	if len(shareLiveSmallShopSyns) != 0 {
		for _, item := range shareLiveSmallShopSyns {
			//状态变为开启并且推送商品到小商品
			err = SaveShareLiveRoomSmallShop(item.UserId, request.SaveShareLiveRoomSmallShop{
				Status:          1,
				ShareLiveRoomId: ShareLiveRoomId,
			})
			if err != nil {
				msg += "用户id自动提交商品到小商店失败：" + strconv.Itoa(int(item.UserId)) + "" + err.Error()
				err = nil
			}
		}
	}
	return
}

/*
*
加入im 创建群聊
*/
func CreateImGroup(room model.ShareLiveRoom) (err error) {
	err, im := common.Initial()
	if err != nil {
		return
	}
	var resultData *common.APIResult
	//如果未加入im，就加入im
	if room.IsAccountImport == 2 {
		room.Identifier = im.Config.Value.SerialNumber + "_" + strconv.Itoa(int(room.UserId))
		var user model2.User
		err = source.DB().Model(&model2.User{}).Where("id = ?", room.UserId).First(&user).Error
		if err != nil {
			err = errors.New("im加入失败,主播会员不存在:" + err.Error())
			return
		}
		var title string
		//如果存在昵称使用昵称，不存在则使用默认
		if user.NickName == "" {
			title = "直播间:" + room.Title
		} else {
			title = user.NickName
		}
		err, resultData = im.AccountImport(room.Identifier, title)
		if err != nil {
			err = errors.New("im加入失败" + err.Error())
			return
		}
		if resultData.Code != 200 {
			err = errors.New("im直播间加入失败" + resultData.Msg)
			return
		}
		err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", room.ID).Updates(&model.ShareLiveRoom{
			IsAccountImport: 1,
			Identifier:      room.Identifier,
		}).Error
		if err != nil {
			err = errors.New("im加入成功,保存记录失败" + err.Error())
			return
		}
	}
	//如果未创建群聊.就创建群聊
	if room.IsCreateGroup == 2 {
		room.GroupId = im.Config.Value.SerialNumber + "_" + strconv.Itoa(int(room.ID))

		err, resultData = im.CreateGroup(room.Identifier, room.GroupId, room.Title)
		if err != nil {
			err = errors.New("创建im群聊失败" + err.Error())
			return
		}
		if resultData.Code != 200 {
			err = errors.New("创建im群聊失败" + resultData.Msg)
			return
		}
		err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", room.ID).Updates(&model.ShareLiveRoom{
			IsCreateGroup: 1,
			GroupId:       room.GroupId,
		}).Error
		if err != nil {
			err = errors.New("创建im群聊成功,保存记录失败" + err.Error())
			return
		}
	}
	return
}

/*
*
修改状态
*/
func SaveShareLiveRoomIsPlayBack(id uint, status int) (err error) {
	if id == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	if status != 1 && status != 2 {
		err = errors.New("请提交修改状态")
		return
	}

	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", id).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在")
		return
	}
	source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", id).Updates(&model.ShareLiveRoom{
		IsPlayback: status,
	})
	return
}

/*
*
修改状态
*/
func SaveShareLiveRoomStatus(id uint, status int) (err error) {
	if id == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", id).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在")
		return
	}
	if shareLiveRoom.Status == 2 {
		err = errors.New("结束的直播无法更新信息")
		return
	}
	//&source.LocalTime{Time: time.Now()}
	if status == 1 {
		source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", id).Updates(&model.ShareLiveRoom{
			Status: status,
			//StartAt: &source.LocalTime{Time: time.Now()},
		})
	} else {
		source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", id).Updates(&model.ShareLiveRoom{
			Status: status,
			EndAt:  &source.LocalTime{Time: time.Now()},
		})
	}

	return
}

// 修改直播间
func SaveShareLiveRoom(roomRequest request.CreateShareLiveRoomRequest) (err error) {
	if roomRequest.ShareLiveRoom.ID == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	if roomRequest.ShareLiveRoom.Title == "" {
		err = errors.New("请输入直播间标题")
		return
	}
	if roomRequest.ShareLiveRoom.ShareLiveCategoryId == 0 {
		err = errors.New("请选择直播间分类")
		return
	}
	if roomRequest.ShareLiveRoom.UserId == 0 {
		err = errors.New("请选择主播会员")
		return
	}
	if roomRequest.ShareLiveRoom.ImageUrl == "" {
		err = errors.New("请提交直播封面")
		return
	}
	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", roomRequest.ShareLiveRoom.ID).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在")
		return
	}
	if shareLiveRoom.Status == 2 {
		err = errors.New("结束的直播无法更新信息")
		return
	}
	//检验是否配置了基础设置
	err, _ = common.VerifySysShareLiveSetting()
	if err != nil {
		return
	}
	shareLiveRoom.Title = roomRequest.ShareLiveRoom.Title
	shareLiveRoom.Sort = roomRequest.ShareLiveRoom.Sort
	shareLiveRoom.ShareLiveCategoryId = roomRequest.ShareLiveRoom.ShareLiveCategoryId
	shareLiveRoom.ImageUrl = roomRequest.ShareLiveRoom.ImageUrl
	shareLiveRoom.StartAt = roomRequest.ShareLiveRoom.StartAt
	shareLiveRoom.UserId = roomRequest.ShareLiveRoom.UserId
	shareLiveRoom.IsSpecifyApplication = roomRequest.ShareLiveRoom.IsSpecifyApplication
	shareLiveRoom.ApplicationIds = roomRequest.ShareLiveRoom.ApplicationIds
	//roomRequest.PlugFlowUrl =
	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", roomRequest.ShareLiveRoom.ID).Save(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("修改失败" + err.Error())
		return
	}
	//清空关联数据
	err = source.DB().Where("share_live_room_id = ?", roomRequest.ShareLiveRoom.ID).Delete(&model.ShareLiveRoomProduct{}).Error
	if err != nil {
		err = errors.New("商品关联失败" + err.Error())
		return
	}
	if len(roomRequest.ProductIds) > 0 {
		//var OldProductRooms []model.ShareLiveRoomProduct
		var ProductRooms []model.ShareLiveRoomProduct
		for _, item := range roomRequest.ProductIds {
			var ProductRoom model.ShareLiveRoomProduct
			ProductRoom.ProductId = item
			ProductRoom.ShareLiveRoomId = roomRequest.ShareLiveRoom.ID
			ProductRooms = append(ProductRooms, ProductRoom)
		}
		err = source.DB().Model(&model.ShareLiveRoomProduct{}).Create(&ProductRooms).Error
		if err != nil {
			err = errors.New("商品关联失败" + err.Error())
			return
		}
	}

	//im
	err = CreateImGroup(shareLiveRoom)
	if err != nil {
		err = errors.New("直播间修改成功:" + err.Error())
		return
	}
	//后续看情况是否变为协程方式
	msg := CreateOrSaveSynShareLiveRoomSmallShop(roomRequest.ShareLiveRoom.ID)
	if msg != "" {
		log.Log().Error("直播间修改成功:自动同步到小商店失败", zap.Any("err", msg))

		//err = errors.New("修改成功," + msg)
		return
	}
	return
}

// 验证这个直播间是否这个小商店启用了
func ShareLiveRoomBySmallShopId(shareLiveRoomIdRequest request.ShareLiveRoomIdRequest) (err error) {
	var shareLiveRoomSmallShop model.ShareLiveRoomSmallShop
	err = source.DB().Model(&model.ShareLiveRoomSmallShop{}).Where("share_live_room_id = ?", shareLiveRoomIdRequest.Id).Where("small_shop_id = ?", shareLiveRoomIdRequest.Sid).First(&shareLiveRoomSmallShop).Error
	if err != nil {
		err = errors.New("小商店未使用这个直播" + err.Error())
		return
	}
	if shareLiveRoomSmallShop.Status == 0 {
		err = errors.New("小商店未开启这个直播")
		return
	}
	return
}

/*
*

	通过id获取

isOpen  0 获取开启的 1获取全部
*/
func GetShareLiveRoomById(id uint) (err error, detail response.ShareLiveRoomDetail) {
	if id == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	err = source.DB().Model(&response.ShareLiveRoomDetail{}).Preload("User").Preload("ShareLiveCategory").Preload("ShareLiveRoomProducts").Preload("ShareLiveRoomProducts.Product").Where("id = ?", id).First(&detail).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}

	var sysSetting setting.SysSetting
	//检验是否配置了基础设置
	err, sysSetting = common.VerifySysShareLiveSetting()
	//如果没有配置则不生成 推流 播放地址
	if err == nil {
		detail.PushUrl = common.GetPushUrl(sysSetting, strconv.Itoa(int(detail.ID)))
		detail.PlayUrl = common.GetPlayUrl(sysSetting, strconv.Itoa(int(detail.ID)))
	}

	return
}

/*
*

	通过id获取
*/
func GetShareLiveRoomByIdSmallShop(id uint, sid uint) (err error, detail response.ShareLiveRoomDetailSmallShopProduct) {
	if id == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	err = source.DB().Model(&response.ShareLiveRoomDetailSmallShopProduct{}).Preload("User").Preload("ShareLiveCategory").Preload("ShareLiveRoomProducts").Preload("ShareLiveRoomProducts.Product").Preload("ShareLiveRoomProducts.Product.Skus").Where("id = ?", id).First(&detail).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}
	var sysSetting setting.SysSetting
	//检验是否配置了基础设置
	err, sysSetting = common.VerifySysShareLiveSetting()
	//如果没有配置则不生成 推流 播放地址
	if err == nil {
		detail.PushUrl = common.GetPushUrl(sysSetting, strconv.Itoa(int(detail.ID)))
		detail.PlayUrl = common.GetPlayUrl(sysSetting, strconv.Itoa(int(detail.ID)))
	}
	var shareLiveRoomProducts []response.ShareLiveRoomProductSmallShopProduct
	for _, item := range detail.ShareLiveRoomProducts {
		var sale smallShopService.SmallShopProductSale
		err, sale = smallShopService.FindSmallShopProductSale(item.ProductId, sid)
		if err != nil {
			err = nil
			continue
		}
		item.Product.ShopPrice = item.Product.Price
		if sale.PriceProportion != 0 {
			switch sale.PriceType {
			case smallShopModel.ORIGIN:
				if item.Product.OriginPrice != 0 {
					item.Product.ShopPrice = item.Product.OriginPrice * sale.PriceProportion / 10000
				}
			case smallShopModel.GUIDE:
				if item.Product.GuidePrice != 0 {
					item.Product.ShopPrice = item.Product.GuidePrice * sale.PriceProportion / 10000
				}
			case smallShopModel.PRICE:
				if item.Product.Price != 0 {
					item.Product.ShopPrice = item.Product.Price * sale.PriceProportion / 10000
				}
			case smallShopModel.ACTIVITY:
				if item.Product.ActivityPrice != 0 {
					item.Product.ShopPrice = item.Product.ActivityPrice * sale.PriceProportion / 10000
				}
			}
			for k, sku := range item.Product.Skus {
				item.Product.Skus[k].ShopPrice = sku.Price
				switch sale.PriceType {
				case smallShopModel.ORIGIN:
					if sku.OriginPrice != 0 {
						item.Product.Skus[k].ShopPrice = sku.OriginPrice * sale.PriceProportion / 10000
					}
				case smallShopModel.GUIDE:
					if sku.GuidePrice != 0 {
						item.Product.Skus[k].ShopPrice = sku.GuidePrice * sale.PriceProportion / 10000
					}
				case smallShopModel.PRICE:
					if sku.Price != 0 {
						item.Product.Skus[k].ShopPrice = sku.Price * sale.PriceProportion / 10000
					}
				case smallShopModel.ACTIVITY:
					if sku.ActivityPrice != 0 {
						item.Product.Skus[k].ShopPrice = sku.ActivityPrice * sale.PriceProportion / 10000
					}
				}

			}
		}

		shareLiveRoomProducts = append(shareLiveRoomProducts, item)
	}
	detail.ShareLiveRoomProducts = shareLiveRoomProducts
	return
}

/*
*

	通过id获取
*/
func GetShareLiveRoomByIds(ids []uint) (err error, detail []response.ShareLiveRoomDetail) {
	if len(ids) == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	db := source.DB().Model(&response.ShareLiveRoomDetail{}).Preload("User").Preload("ShareLiveCategory").Preload("ShareLiveRoomProducts").Preload("ShareLiveRoomProducts.Product")

	err = db.Where("id in ?", ids).Find(&detail).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}
	var sysSetting setting.SysSetting
	//检验是否配置了基础设置
	err, sysSetting = common.VerifySysShareLiveSetting()
	//如果没有配置则不生成 推流 播放地址
	if err == nil {
		for key, item := range detail {
			detail[key].PushUrl = common.GetPushUrl(sysSetting, strconv.Itoa(int(item.ID)))
			detail[key].PlayUrl = common.GetPlayUrl(sysSetting, strconv.Itoa(int(item.ID)))
			//detail.PushUrl = common.GetPushUrl(sysSetting, strconv.Itoa(int(detail.ID)))
			//detail.PlayUrl = common.GetPlayUrl(sysSetting, strconv.Itoa(int(detail.ID)))
		}

	}

	return
}

/*
*

	通过id获取
*/
func GetShareLiveRoomNumById(id uint) (err error, shareLiveRoom model.ShareLiveRoom) {
	if id == 0 {
		err = errors.New("请提交直播间id")
		return
	}

	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", id).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}
	return
}

/*
*

	定时任务--定时获取直播中的 峰值和流量
*/
func SynShareLive() (err error) {
	var sysSetting setting.SysSetting
	//检验是否配置了基础设置
	err, sysSetting = common.VerifySysShareLiveSetting()
	if err != nil {
		//err = errors.New("查询峰值流程定时任务：没有配置"+err.Error())
		return
	}
	var shareLiveRooms []model.ShareLiveRoom
	err = source.DB().Where("thrust_state = 1").Find(&shareLiveRooms).Error
	if err != nil {
		//err = errors.New("不存在推流中的直播间"+err.Error())
		return
	}

	for _, item := range shareLiveRooms {
		var describeStreamPlayInfoListResponse *live.DescribeStreamPlayInfoListResponse

		err, describeStreamPlayInfoListResponse = common.DescribeStreamPlayInfoList(item.BeginTime.Format("2006-01-02 15:04:05"), sysSetting.Value.PlayUrl, common.GetStreamName(strconv.Itoa(int(item.ID))))
		if err != nil {
			log.Log().Error(err.Error(), zap.Any("直播间查询流数据失败", item.ID), zap.Any("err", err))
			continue
		}

		for _, describeStreamPlayInfoItem := range describeStreamPlayInfoListResponse.Response.DataInfoList {
			item.PeakBandwidth = math.Max(item.PeakBandwidth, *describeStreamPlayInfoItem.Bandwidth) //带宽
			item.TotalFlow = math.Max(item.TotalFlow, *describeStreamPlayInfoItem.Flux)              //流量
		}
		err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", item.ID).Save(&item).Error
		if err != nil {
			log.Log().Error(err.Error(), zap.Any("直播间保存流量峰值失败", item.ID), zap.Any("err", err))
			continue
		}
	}
	return
}

/*
*

	共享直播间列表

isApp//是否是采购端请求 1是 0否
*/
func GetShareLiveRoomList(info request.ShareLiveRoomSearch, isApp int) (err error, list []response.ShareLiveRoomDetail, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&response.ShareLiveRoomDetail{}).Preload("ShareLiveCategory").Preload("ShareLiveRoomProducts").Preload("User")
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Title != "" {
		db.Where("title like '%" + info.Title + "%'")
	}
	if info.IsOpen != nil {
		db.Where("is_open = ?", info.IsOpen)
	}
	//通过小商店id获取共享直播间列表
	if info.Sid != 0 {
		var shareLiveRoomIds []uint
		err = source.DB().Model(&model.ShareLiveRoomSmallShop{}).Where("status = 1").Where("small_shop_id = ?", info.Sid).Pluck("share_live_room_id", &shareLiveRoomIds).Error
		if err != nil {
			return nil, list, total
		}
		if len(shareLiveRoomIds) == 0 {
			return nil, list, total
		}
		db.Where("id in ?", shareLiveRoomIds)

	}
	if info.Keyword != "" {
		var userIds []uint
		source.DB().Model(&model2.User{}).Where("nick_name like '%"+info.Keyword+"%' or username like '%"+info.Keyword+"%'").Pluck("id", &userIds)
		if len(userIds) > 0 {
			db.Where("user_id in ? or title like'%"+info.Keyword+"%'", userIds)
		} else {
			db.Where("title like'%" + info.Keyword + "%'")
		}

	}
	if isApp == 1 {
		// 构建子查询：不是指定采购端的 或者 指定采购端且采购端列表包含当前采购端
		//subQuery := "(is_specify_application = 0 OR (is_specify_application = 1 AND JSON_CONTAINS(CAST(application_ids AS CHAR CHARACTER SET utf8mb4), ?)))"
		//// 使用json.Marshal将applicationId转换为JSON格式
		//var applicationIds []uint
		//applicationIds = append(applicationIds, info.ApplicationId)
		//appIdJSON, err := json.Marshal(applicationIds)
		//if err != nil {
		//	log.Log().Error(err.Error(), zap.Any("转换applicationId为JSON失败", info.ApplicationId))
		//	return err, list, total
		//}
		//db.Where(subQuery, string(appIdJSON))
		subQuery := "(is_specify_application = 0 OR (is_specify_application = 1 AND JSON_CONTAINS(application_ids, CAST(? AS JSON))))"
		db.Where(subQuery, strconv.Itoa(int(info.ApplicationId)))
	}
	//如果是采购端请求 并且要查询已导入或者未导入的
	if isApp == 1 && info.IsImport != 0 {
		var shareLiveRoosIds []uint
		source.DB().Model(&model.ShareLiveRoomApplication{}).Where("application_id = ?", info.ApplicationId).Pluck("share_live_room_id", &shareLiveRoosIds)
		if info.IsImport == 1 {
			//如果查询已导入的并且没有已导入的直播间id,就直接返回
			if len(shareLiveRoosIds) == 0 {
				return
			}
			db.Where("id in ?", shareLiveRoosIds)
		}
		if info.IsImport == 2 {
			db.Where("id not in ?", shareLiveRoosIds)
		}
	}
	if info.ShareLiveCategoryId != 0 {
		db.Where("share_live_category_id = ?", info.ShareLiveCategoryId)
	}
	if info.Status != nil {
		db.Where("status = ?", info.Status)
	}
	//如果不是采购端API请求 并且有采购端id就检索采购端id导入的
	if info.ApplicationId != 0 && isApp == 0 {
		var shareLiveRoomIds []uint
		err = source.DB().Model(&model.ShareLiveRoomApplication{}).Where("application_id = ?", info.ApplicationId).Pluck("share_live_room_id", &shareLiveRoomIds).Error
		if err != nil {
			return nil, list, total
		}
		db.Where("id in ?", shareLiveRoomIds)
	}
	//通过商品id筛选
	if info.ProductId != 0 {
		var shareLiveRoomIds []uint
		err = source.DB().Model(&model.ShareLiveRoomProduct{}).Select("DISTINCT(share_live_room_id)").Where("product_id = ?", info.ProductId).Pluck("share_live_room_id", &shareLiveRoomIds).Error
		if err != nil {
			return nil, list, total
		}
		db.Where("id in ?", shareLiveRoomIds)
	}
	//通过商品名称筛选
	if info.ProductName != "" {
		//var ProductIds []uint
		var shareLiveRoomIds []uint
		err = source.DB().Model(&model3.Product{}).Select("DISTINCT(share_live_room_products.share_live_room_id)").Joins("INNER join share_live_room_products on share_live_room_products.product_id = products.id").Where("products.title like '%"+info.ProductName+"%'").Pluck("share_live_room_products.share_live_room_id", &shareLiveRoomIds).Error
		if err != nil {
			return nil, list, total
		}
		//
		//err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("product_id in ?",ProductIds).Pluck("share_live_room_id",&shareLiveRoomIds).Error
		//if err != nil {
		//	return  nil,list,total
		//}
		db.Where("id in ?", shareLiveRoomIds)
	}
	if info.IsTranscribe != nil {
		db.Where("is_transcribe = ?", info.IsTranscribe)
	}
	if info.IsPlayback != 0 {
		db.Where("is_playback = ?", info.IsPlayback)
	}
	if info.StartATSearch != "" {
		db.Where("start_at >= ?", info.StartATSearch)
	}
	if info.EndATSearch != "" {
		db.Where("start_at <= ?", info.EndATSearch)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Order("sort desc").Order("id desc").Offset(offset).Find(&list).Error
	if err != nil {
		return nil, list, total
	}
	var sysSetting setting.SysSetting
	//检验是否配置了基础设置
	var shareLiveErr error
	shareLiveErr, sysSetting = common.VerifySysShareLiveSetting()
	//如果没有配置则不生成 推流 播放地址
	for key, item := range list {
		if shareLiveErr == nil {
			list[key].PushUrl = common.GetPushUrl(sysSetting, strconv.Itoa(int(item.ID)))
			list[key].PlayUrl = common.GetPlayUrl(sysSetting, strconv.Itoa(int(item.ID)))
		}

		err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("share_live_room_id = ?", item.ID).Count(&list[key].ProductNum).Error
		if err != nil {
			err = nil
			list[key].ProductNum = 0
		}
		err = source.DB().Model(&model.ShareLiveRoomApplication{}).Where("share_live_room_id = ?", item.ID).Count(&list[key].AppNum).Error
		if err != nil {
			err = nil
			list[key].AppNum = 0
		}
	}
	return err, list, total
}

/*
*

	共享直播间列表  -- 前端
*/
func GetShareLiveRoomListFront(info request.ShareLiveRoomSearch) (err error, list []response.ShareLiveRoomFrontDetail, total int64) {
	if info.SmallShopUserId != 0 {
		var smallShop smallShopModel.SmallShop
		err = source.DB().Where("uid = ?", info.SmallShopUserId).First(&smallShop).Error
		if err != nil {
			err = errors.New("不是小商店店主" + err.Error())
			return
		}
	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&response.ShareLiveRoomFrontDetail{}).Preload("ShareLiveCategory").Preload("ShareLiveRoomProducts").Preload("User")
	db.Where("share_live_rooms.status in (0,1) or share_live_rooms.is_transcribe = 1") //查询直播状态为待直播 和正在直播的，以及有录制文件的（回放文件）
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Title != "" {
		db.Where("title like '%" + info.Title + "%'")
	}

	if info.IsOpen != nil {
		db.Where("is_open = ?", info.IsOpen)
	}

	if info.Keyword != "" {
		var userIds []uint
		err = source.DB().Model(&model2.User{}).Where("nick_name like '%"+info.Keyword+"%' or username like '%"+info.Keyword+"%'").Pluck("id", &userIds).Error
		if err != nil {
			return nil, list, total
		}
		db.Where("user_id in ?", userIds)
	}

	if info.ShareLiveCategoryId != 0 {
		db.Where("share_live_category_id = ?", info.ShareLiveCategoryId)
	}

	//通过商品名称筛选
	if info.ProductName != "" {
		//var ProductIds []uint
		var shareLiveRoomIds []uint
		err = source.DB().Model(&model3.Product{}).Select("DISTINCT(share_live_room_products.share_live_room_id)").Joins("INNER join share_live_room_products on share_live_room_products.product_id = products.id").Where("products.title like '%"+info.ProductName+"%'").Pluck("share_live_room_products.share_live_room_id", &shareLiveRoomIds).Error
		if err != nil {
			return nil, list, total
		}
		//
		//err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("product_id in ?",ProductIds).Pluck("share_live_room_id",&shareLiveRoomIds).Error
		//if err != nil {
		//	return  nil,list,total
		//}
		db.Where("id in ?", shareLiveRoomIds)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Order("sort desc").Order("id desc").Offset(offset).Find(&list).Error
	if err != nil {
		return nil, list, total
	}
	var sysSetting setting.SysSetting
	//检验是否配置了基础设置
	err, sysSetting = common.VerifySysShareLiveSetting()
	//如果没有配置则不生成 推流 播放地址
	for key, item := range list {
		if err == nil {
			list[key].PushUrl = common.GetPushUrl(sysSetting, strconv.Itoa(int(item.ID)))
			list[key].PlayUrl = common.GetPlayUrl(sysSetting, strconv.Itoa(int(item.ID)))
		}

		err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("share_live_room_id = ?", item.ID).Count(&list[key].ProductNum).Error
		if err != nil {
			err = nil
			list[key].ProductNum = 0
		}
		err = source.DB().Model(&model.ShareLiveRoomApplication{}).Where("share_live_room_id = ?", item.ID).Count(&list[key].AppNum).Error
		if err != nil {
			err = nil
			list[key].AppNum = 0
		}
		source.DB().Model(&model.ShareLiveRoomSmallShop{}).Where("share_live_room_id = ?", item.ID).Where("user_id = ?", info.SmallShopUserId).First(&list[key].ShareLiveRoomSmallShop)
	}
	return err, list, total
}

type Product struct {
	other.Product
	Brand          other.Brand    `json:"brand"  form:"brand"`
	Category1      other.Category `json:"category_1" form:"category_1"`
	Category2      other.Category `json:"category_2" form:"category_2"`
	Category3      other.Category `json:"category_3" form:"category_3"`
	AgreementPrice uint           `json:"agreement_price" gorm:"-"` //协议价
	MarketPrice    uint           `json:"market_price" gorm:"-"`    //市场价
	SalePrice      uint           `json:"sale_price" gorm:"-"`      //销售价
	PromotionRate  float64        `json:"origin_rate" gorm:"-"`     //常规利润率
	ActivityRate   float64        `json:"activity_rate" gorm:"-"`   //营销利润率
}

/*
*

	共享直播间商品列表
*/
func GetShareLiveRoomProductList(info request.GetShareLiveRoomProductListRequest, appID uint) (err error, list []Product, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	if info.ShareLiveRoomId == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	var productIds []uint
	err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("share_live_room_id = ?", info.ShareLiveRoomId).Pluck("product_id", &productIds).Error
	if err != nil {
		err = errors.New("直播间没有商品" + err.Error())
		return
	}
	if len(productIds) == 0 {
		return
	}
	var application productService.Application
	err = source.DB().Preload("ApplicationLevel").Preload("User.UserLevel").Where("id = ?", appID).First(&application).Error
	if err != nil {
		return
	}
	db := source.DB().Model(&Product{}).Preload(clause.Associations)
	db = db.Where("id in ?", productIds)

	db = db.Where("deleted_at is null")

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Order("sort desc").Order("id desc").Offset(offset).Find(&list).Error

	var productList []Product
	for _, product := range list {
		var percent, isDefault int
		err, percent, isDefault = level.GetLevelDiscountPercent(application.User.LevelID)
		if err != nil {
			return
		}
		if product.UserPriceSwitch == 1 {
			var calculateRes bool
			product.AgreementPrice, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Price, application.User.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, product.AgreementPrice = level.GetLevelDiscountAmount(product.Price, product.CostPrice, percent)
					if err != nil {
						return
					}
				} else {
					product.AgreementPrice = product.Price
				}
			}
		} else {
			if isDefault == 0 {
				err, product.AgreementPrice = level.GetLevelDiscountAmount(product.Price, product.CostPrice, percent)
				if err != nil {
					return
				}
			} else {
				product.AgreementPrice = product.Price
			}
		}

		product.AgreementPrice = uint(math.Floor(float64(product.AgreementPrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
		product.ActivityPrice = uint(math.Floor(float64(product.ActivityPrice)))
		product.MarketPrice = product.OriginPrice
		//if product.ActivityPrice > 0 {
		//	product.CostPrice = product.ActivityPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		//} else {
		product.CostPrice = product.AgreementPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		//}
		product.SalePrice = uint(math.Floor(float64(product.GuidePrice))) //建议销售价
		if product.GuidePrice > 0 {
			product.ProfitRate = productService.Decimal((float64(product.GuidePrice) - float64(product.AgreementPrice)) / float64(product.GuidePrice))
		} else {
			product.ProfitRate = -1
		}
		if product.AgreementPrice > 0 && product.GuidePrice > 0 && product.GuidePrice >= product.AgreementPrice {
			product.PromotionRate = productService.Decimal((float64(product.GuidePrice) - float64(product.AgreementPrice)) / float64(product.AgreementPrice))
		} else {
			if product.AgreementPrice <= 0 {
				product.PromotionRate = 1
			}
			if product.GuidePrice <= 0 {
				product.PromotionRate = 0
			}
			if product.GuidePrice < product.AgreementPrice {
				product.PromotionRate = 0
			}
		}
		if product.ActivityPrice > 0 {
			if product.ActivityPrice > product.AgreementPrice && product.ActivityPrice > 0 && product.AgreementPrice > 0 {
				product.ActivityRate = productService.Decimal((float64(product.ActivityPrice) - float64(product.AgreementPrice)) / float64(product.AgreementPrice))
			} else {
				product.ActivityRate = 0
			}
			product.CostPrice = product.ActivityPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		} else {
			product.CostPrice = product.AgreementPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		}
		for i, sku := range product.Skus {
			if product.UserPriceSwitch == 1 {
				var calculateRes bool
				product.Skus[i].Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, application.User.LevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					err, product.Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
					if err != nil {
						return
					}
				}
			} else {
				err, product.Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
				if err != nil {
					return
				}
			}

			product.Skus[i].Price = uint(math.Floor(float64(product.Skus[i].Price) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
			product.Skus[i].CostPrice = product.Skus[i].Price
		}

		productList = append(productList, product)
	}

	return err, productList, total
	//return err, list, total
}

/*
*

	获取回放文件
*/
func GetShareLiveRoomRecordFileByRoomId(roomId uint) (err error, files []model.ShareLiveRoomRecordFile) {
	err = source.DB().Model(&model.ShareLiveRoomRecordFile{}).Where("share_live_room_id = ?", roomId).Find(&files).Error
	if err != nil {
		err = errors.New("该直播间没有回放文件")
		return
	}
	return
}

/*
*

	共享直播间列表
*/
func GetShareLiveRoomApplicationList(info request.ShareLiveRoomApplicationSearch) (err error, list []response.ShareLiveRoomApplication, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&response.ShareLiveRoomApplication{}).Joins("INNER join share_live_rooms on share_live_rooms.id = share_live_room_applications.share_live_room_id").Preload("Application").Preload("ShareLiveRoom").Preload("ShareLiveRoom.ShareLiveCategory").Preload("ShareLiveRoom.User")
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Keyword != "" {
		var userIds []uint
		err = source.DB().Model(&model2.User{}).Where("nick_name like '%"+info.Keyword+"%' or username like '%"+info.Keyword+"%'").Pluck("id", &userIds).Error
		if err != nil {
			return nil, list, total
		}
		db.Where("share_live_rooms.user_id in ?", userIds)
	}
	if info.Title != "" {
		db.Where("share_live_rooms.title like '%" + info.Title + "%'")
	}
	if info.ShareLiveRoomId != 0 {
		db.Where("share_live_room_applications.share_live_room_id = ? ", info.ShareLiveRoomId)
	}
	if info.ShareLiveCategoryId != 0 {
		db.Where("share_live_rooms.share_live_category_id = ?", info.ShareLiveCategoryId)
	}
	if info.Status != nil {
		db.Where("share_live_rooms.status = ?", info.Status)
	}
	if info.ApplicationId != 0 {
		db.Where("share_live_room_applications.application_id = ?", info.ApplicationId)
	}
	//通过商品id筛选
	if info.ProductId != 0 {
		var shareLiveRoomIds []uint
		err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("product_id = ?", info.ProductId).Pluck("share_live_room_id", &shareLiveRoomIds).Error
		if err != nil {
			return nil, list, total
		}
		db.Where("share_live_rooms.id in ?", shareLiveRoomIds)
	}
	//通过商品名称筛选
	if info.ProductName != "" {
		var ProductIds []uint
		err = source.DB().Model(&model3.Product{}).Where("title like '%"+info.ProductName+"%'").Pluck("id", &ProductIds).Error
		if err != nil {
			return nil, list, total
		}
		var shareLiveRoomIds []uint
		err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("product_id in ?", ProductIds).Pluck("share_live_room_id", &shareLiveRoomIds).Error
		if err != nil {
			return nil, list, total
		}
		db.Where("share_live_rooms.id in ?", shareLiveRoomIds)
	}
	if info.StartATSearch != "" {
		db.Where("share_live_rooms.start_at >= ?", info.StartATSearch)
	}
	if info.EndATSearch != "" {
		db.Where("share_live_rooms.start_at =< ?", info.EndATSearch)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Select("share_live_room_applications.*").Order("share_live_room_applications.id desc").Offset(offset).Find(&list).Error
	if err != nil {
		return nil, list, total
	}

	//如果没有配置则不生成 推流 播放地址
	for key, item := range list {
		err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("share_live_room_id = ?", item.ShareLiveRoomId).Count(&list[key].ProductNum).Error
		if err != nil {
			err = nil
			list[key].ProductNum = 0
		}
	}
	return err, list, total
}

/*
*
获取所有直播间
*/
func GetShareLiveRooms() (err error, room []model.ShareLiveRoom) {
	err = source.DB().Model(&model.ShareLiveRoom{}).Find(&room).Error
	return
}

/*
*

	增加观看人数

appId 如果没有代表不是采购端的直接增加数量就可以
*/
func SaveTotalNum(appId uint, numRequest request.SaveTotalNumRequest) (err error) {
	var shareLiveRoomApplication model.ShareLiveRoomApplication
	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", numRequest.ShareLiveRoomId).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}
	if appId != 0 {
		err = source.DB().Where("share_live_room_id = ?", numRequest.ShareLiveRoomId).Where("application_id = ?", appId).First(&shareLiveRoomApplication).Error
		if err != nil {
			err = errors.New("未导入该直播间" + err.Error())
			return
		}
	}

	shareLiveRoom.TotalNum += numRequest.Num
	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", shareLiveRoom.ID).Save(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间保存观看人数失败" + err.Error())
		return
	}
	if shareLiveRoomApplication.ID > 0 {
		shareLiveRoomApplication.TotalNum += numRequest.Num
		err = source.DB().Model(&model.ShareLiveRoomApplication{}).Where("id = ?", shareLiveRoomApplication.ID).Save(&shareLiveRoomApplication).Error
		if err != nil {
			err = errors.New("直播间-采购端保存观看人数失败" + err.Error())
			return
		}
	}
	return
}

/*
*

	增加点赞人数 -- 采购端
	status  1增加 2减少
*/
func UpdateLikeNum(appId uint, numRequest request.SaveTotalNumRequest, status int) (err error) {
	var shareLiveRoomApplication model.ShareLiveRoomApplication
	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", numRequest.ShareLiveRoomId).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}
	err = source.DB().Where("share_live_room_id = ?", numRequest.ShareLiveRoomId).Where("application_id = ?", appId).First(&shareLiveRoomApplication).Error
	if err != nil {
		err = errors.New("未导入该直播间" + err.Error())
		return
	}
	if status == 2 {
		if numRequest.Num > shareLiveRoom.LikeNum {
			shareLiveRoom.LikeNum = 0
		} else {
			shareLiveRoom.LikeNum -= numRequest.Num
		}
	} else {
		shareLiveRoom.LikeNum += numRequest.Num
	}

	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", shareLiveRoom.ID).Save(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("保存失败" + err.Error())
		return
	}
	if status == 2 {
		if numRequest.Num > shareLiveRoomApplication.LikeNum {
			shareLiveRoomApplication.LikeNum = 0
		} else {
			shareLiveRoomApplication.LikeNum -= numRequest.Num
		}
	} else {
		shareLiveRoomApplication.LikeNum += numRequest.Num
	}

	err = source.DB().Model(&model.ShareLiveRoomApplication{}).Where("id = ?", shareLiveRoomApplication.ID).Save(&shareLiveRoomApplication).Error
	if err != nil {
		err = errors.New("操作失败-采购端保存观看人数失败" + err.Error())
		return
	}
	return
}

/*
*

	增加点赞人数
	status  1增加 2减少
*/
func SaveLikeNum(smallUserId uint, numRequest request.ShareLiveRoomId) (err error) {
	var num uint = 1
	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", numRequest.ShareLiveRoomId).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}

	var shareLiveRoomLike model.ShareLiveRoomLike
	err = source.DB().Where("share_live_room_id = ?", numRequest.ShareLiveRoomId).Where("small_user_id = ?", smallUserId).First(&shareLiveRoomLike).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询记录错误" + err.Error())
		return
	}
	if shareLiveRoomLike.ID == 0 {
		shareLiveRoomLike.ShareLiveRoomId = numRequest.ShareLiveRoomId
		shareLiveRoomLike.Status = 0
		shareLiveRoomLike.SmallUserId = smallUserId
		err = source.DB().Create(&shareLiveRoomLike).Error
		if err != nil {
			err = errors.New("创建记录失败" + err.Error())
			return
		}
	}
	//如果是1就取消喜欢 是0 就变为喜欢
	if shareLiveRoomLike.Status == 1 {
		if num > shareLiveRoom.LikeNum {
			shareLiveRoom.LikeNum = 0
		} else {
			shareLiveRoom.LikeNum -= num
		}
		shareLiveRoomLike.Status = 0
	} else {
		shareLiveRoom.LikeNum += num
		shareLiveRoomLike.Status = 1
	}

	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", shareLiveRoom.ID).Save(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("保存失败" + err.Error())
		return
	}

	err = source.DB().Model(&model.ShareLiveRoomLike{}).Where("id = ?", shareLiveRoomLike.ID).Save(&shareLiveRoomLike).Error
	if err != nil {
		log.Log().Error("记录修改失败", zap.Any("err", err))
		err = errors.New("记录修改失败" + err.Error())
		return
	}

	return
}

/*
*
获取是否点赞
*/
func GetLike(smallUserId uint, numRequest request.ShareLiveRoomId) (err error, shareLiveRoomLike model.ShareLiveRoomLike) {
	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", numRequest.ShareLiveRoomId).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}

	err = source.DB().Where("share_live_room_id = ?", numRequest.ShareLiveRoomId).Where("small_user_id = ?", smallUserId).First(&shareLiveRoomLike).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询记录错误" + err.Error())
		return
	}

	return
}
func CreateShareLiveRoomApplication(application model.ShareLiveRoomApplication) (err error) {
	//var shareLiveRoomApplication model.ShareLiveRoomApplication
	err, _ = VerifyShareLiveRoomApplication(application.ShareLiveRoomId, application.ApplicationId)
	//如果没有才保存
	if err != nil {
		err = source.DB().Save(&application).Error
		if err != nil {
			err = errors.New("保存导入记录失败" + err.Error())
			return
		}
	}
	return
}

// 共享直播间 采购端关联表
type ShareLiveRoomApplication struct {
	source.Model
	ShareLiveRoomId  uint                `json:"share_live_room_id" form:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id;"`       // 直播间id
	ApplicationId    uint                `json:"application_id" form:"application_id" gorm:"column:application_id;comment:采购端id;"`                   // 采购端id
	OrderAmountTotal uint                `json:"order_amount_total" form:"order_amount_total" gorm:"column:order_amount_total;comment:带货订单总金额;"` //带货订单总金额
	OrderTotal       uint                `json:"order_total" form:"order_total" gorm:"column:order_total;comment:带货订单总数;"`                        //带货订单总数
	TotalNum         uint                `json:"total_num" form:"total_num" gorm:"column:total_num;comment:累计观看人数;"`                              //累计观看人数
	ShareLiveRoom    model.ShareLiveRoom `json:"share_live_room"`
}

/*
*
校验是否导入了直播间
*/
func VerifyShareLiveRoomApplication(ShareLiveRoomId, application uint) (err error, shareLiveRoomApplication ShareLiveRoomApplication) {
	err = source.DB().Where("share_live_room_id = ?", ShareLiveRoomId).Preload("ShareLiveRoom").Where("application_id = ?", application).First(&shareLiveRoomApplication).Error
	if err != nil {
		err = errors.New("未导入直播间" + err.Error())
		return
	}
	return
}

/*
*
校验小商店是否使用这个直播
*/
func VerifyShareLiveRoomSmallShop(ShareLiveRoomId, sid uint) (err error, shareLiveRoom model.ShareLiveRoom) {
	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", ShareLiveRoomId).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在" + err.Error())
		return
	}
	var shareLiveRoomSmallShop model.ShareLiveRoomSmallShop
	err = source.DB().Model(&model.ShareLiveRoomSmallShop{}).Where("small_shop_id = ?", sid).First(&shareLiveRoomSmallShop).Error
	if err != nil {
		err = errors.New("未导入直播间" + err.Error())
		return
	}
	if shareLiveRoomSmallShop.Status == 0 {
		err = errors.New("小商品关闭了这个直播" + err.Error())
		return
	}

	return
}

/*
*

	删除直播间关联的商品 -- 监听商品删除
*/
func DeleteProductShareLiveRoom(productId uint) {
	var shareLiveRoomProduct model.ShareLiveRoomProduct
	err := source.DB().Model(&model.ShareLiveRoomProduct{}).Where("product_id = ?", productId).Delete(&shareLiveRoomProduct).Error
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("监听商品删除 删除直播间关联商品失败", productId), zap.Any("err", err))

	}
}

/*
*
//计算累计直播时长
ShareLiveRoomId  直播间
status   1 开始时间 2结束直播
*/
func ShareLiveRoomTime(ShareLiveRoom model.ShareLiveRoom, status int) {
	var shareLiveRoomTime model.ShareLiveRoomTime
	source.DB().Model(&model.ShareLiveRoomTime{}).Where("share_live_room_id = ?", ShareLiveRoom.ID).Last(&shareLiveRoomTime)
	if status == 1 {
		//如果不存在 或者最后一条记录已有结束时间 则创建新的记录
		if shareLiveRoomTime.ID == 0 || shareLiveRoomTime.EndTime != nil {
			source.DB().Model(&model.ShareLiveRoomTime{}).Create(&model.ShareLiveRoomTime{
				BeginTime:       &source.LocalTime{Time: time.Now()},
				ShareLiveRoomId: ShareLiveRoom.ID,
			})
		}
	} else {
		//没有记录则无效
		if shareLiveRoomTime.ID == 0 {
			return
		}
		endAt := &source.LocalTime{Time: time.Now()}

		totalTime := endAt.Sub(shareLiveRoomTime.BeginTime.Time) //播放间隔

		source.DB().Model(&model.ShareLiveRoomTime{}).Where("id = ?", shareLiveRoomTime.ID).Updates(&model.ShareLiveRoomTime{
			EndTime: endAt,
		})

		ShareLiveRoom.TotalTime += uint(totalTime.Seconds())
		err := source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", ShareLiveRoom.ID).Updates(&model.ShareLiveRoom{
			TotalTime: ShareLiveRoom.TotalTime,
		}).Error
		if err != nil {
			log.Log().Error("累计直播时间保存失败", zap.Any("totalTime", totalTime), zap.Any("ShareLiveRoom", ShareLiveRoom))
		}
	}
}

// 获取同步设置
func GetSysShareLiveSettingIsSyn(userId uint) (err error, shareLiveSmallShopSyn model.ShareLiveSmallShopSyn) {

	var smallShop smallShopModel.SmallShop
	err = source.DB().Where("uid = ?", userId).First(&smallShop).Error
	if err != nil {
		err = errors.New("不是小商店店主" + err.Error())
		return
	}

	err = source.DB().Where("small_shop_id = ?", smallShop.ID).Where("user_id = ?", userId).First(&shareLiveSmallShopSyn).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("错误" + err.Error())
		return
	}

	if shareLiveSmallShopSyn.ID == 0 {
		shareLiveSmallShopSyn.IsSyn = 0
		shareLiveSmallShopSyn.UserId = userId
		shareLiveSmallShopSyn.SmallShopID = smallShop.ID
		err = source.DB().Model(&model.ShareLiveSmallShopSyn{}).Create(&shareLiveSmallShopSyn).Error
		if err != nil {
			err = errors.New("创建记录错误" + err.Error())
			return
		}
	}

	return
}

// 修改同步设置
func SaveSysShareLiveSettingIsSyn(userId uint, isSyn int) (err error) {

	var smallShop smallShopModel.SmallShop
	err = source.DB().Where("uid = ?", userId).First(&smallShop).Error
	if err != nil {
		err = errors.New("不是小商店店主" + err.Error())
		return
	}
	var shareLiveSmallShopSyn model.ShareLiveSmallShopSyn
	err = source.DB().Where("small_shop_id = ?", smallShop.ID).Where("user_id = ?", userId).First(&shareLiveSmallShopSyn).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("错误" + err.Error())
		return
	}
	shareLiveSmallShopSyn.IsSyn = isSyn
	if shareLiveSmallShopSyn.ID == 0 {
		shareLiveSmallShopSyn.UserId = userId
		shareLiveSmallShopSyn.SmallShopID = smallShop.ID
		err = source.DB().Model(&model.ShareLiveSmallShopSyn{}).Create(&shareLiveSmallShopSyn).Error
		if err != nil {
			err = errors.New("创建记录错误" + err.Error())
			return
		}
	} else {
		err = source.DB().Model(&model.ShareLiveSmallShopSyn{}).Where("id = ?", shareLiveSmallShopSyn.ID).Save(&shareLiveSmallShopSyn).Error
	}

	if err != nil {
		err = errors.New("修改记录错误" + err.Error())
		return
	}
	return
}

// 修改同步设置
func SaveShareLiveRoomSmallShop(userId uint, saveShareLiveRoomSmallShop request.SaveShareLiveRoomSmallShop) (err error) {

	var smallShop smallShopModel.SmallShop
	err = source.DB().Where("uid = ?", userId).First(&smallShop).Error
	if err != nil {
		err = errors.New("不是小商店店主" + err.Error())
		return
	}
	err, setting := smallShopService.GetShopProductSetting(smallShop.ID)
	if err != nil {
		err = errors.New("未设置定价策略" + err.Error())
		return
	}
	if setting.Values.IsActivityPrice == 0 && setting.Values.IsAgreementPrice == 0 && setting.Values.IsGuidePrice == 0 && setting.Values.IsOriginPrice == 0 {
		err = errors.New("未设置定价策略")
		return
	}
	err = ShareLiveRoomSmallShopStart(setting, smallShop, userId, saveShareLiveRoomSmallShop)
	return
}
func ShareLiveRoomSmallShopStart(smallShopSetting smallShopModel.ShopProductSetting, smallShop smallShopModel.SmallShop, userId uint, saveShareLiveRoomSmallShop request.SaveShareLiveRoomSmallShop) (err error) {
	var shareLiveRoomSmallShop model.ShareLiveRoomSmallShop
	err = source.DB().Where("share_live_room_id = ?", saveShareLiveRoomSmallShop.ShareLiveRoomId).Where("user_id = ?", userId).First(&shareLiveRoomSmallShop).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("错误" + err.Error())
		return
	}
	shareLiveRoomSmallShop.Status = saveShareLiveRoomSmallShop.Status
	if shareLiveRoomSmallShop.ID == 0 {
		shareLiveRoomSmallShop.UserId = userId
		shareLiveRoomSmallShop.ShareLiveRoomId = saveShareLiveRoomSmallShop.ShareLiveRoomId
		shareLiveRoomSmallShop.SmallShopID = smallShop.ID
		err = source.DB().Model(&model.ShareLiveRoomSmallShop{}).Create(&shareLiveRoomSmallShop).Error
		if err != nil {
			err = errors.New("创建记录错误" + err.Error())
			return
		}
	} else {
		err = source.DB().Model(&model.ShareLiveRoomSmallShop{}).Where("id = ?", shareLiveRoomSmallShop.ID).Save(&shareLiveRoomSmallShop).Error
	}

	if err != nil {
		err = errors.New("修改记录错误" + err.Error())
		return
	}
	//推送商品到小商店
	if saveShareLiveRoomSmallShop.Status == 1 {
		err = PushSmallShop(smallShopSetting, smallShop, saveShareLiveRoomSmallShop)
	}
	return
}

// 提交商品到小商店
func PushSmallShop(smallShopSetting smallShopModel.ShopProductSetting, smallShop smallShopModel.SmallShop, saveShareLiveRoomSmallShop request.SaveShareLiveRoomSmallShop) (err error) {
	var priceType int
	var priceProportion int
	if smallShopSetting.Values.IsOriginPrice == 1 {
		priceType = 0
		priceProportion = smallShopSetting.Values.OriginPriceRatio

	} else if smallShopSetting.Values.IsGuidePrice == 1 {
		priceType = 1
		priceProportion = smallShopSetting.Values.GuidePriceRatio

	} else if smallShopSetting.Values.IsAgreementPrice == 1 {
		priceType = 2
		priceProportion = smallShopSetting.Values.AgreementPriceRatio

	} else if smallShopSetting.Values.IsActivityPrice == 1 {
		priceType = 3
		priceProportion = smallShopSetting.Values.ActivityPriceRatio
	}
	priceProportion = priceProportion * 100 //小商店选品时提交给后端的*100 这里要*100 否则价格会不对
	var ProductIDs []uint
	err = source.DB().Model(&model.ShareLiveRoomProduct{}).Where("share_live_room_id = ?", saveShareLiveRoomSmallShop.ShareLiveRoomId).Pluck("product_id", &ProductIDs).Error
	//没有商品直接返回成功
	if err != nil {
		return nil
	}
	//没有商品直接返回成功
	if len(ProductIDs) == 0 {
		return nil
	}
	err = smallShopService.AddProductSale(smallShop.ID, ProductIDs, uint(priceProportion), smallShopModel.PriceType(priceType))
	if err != nil {
		log.Log().Error("选品库商品提交失败", zap.Any("err", err))
		err = errors.New("选品库商品提交失败" + err.Error())
		return
	}
	return
}

// 一键同步所有筛选项的状态
func SaveShareLiveRoomSmallShopAll(userId uint, saveShareLiveRoomSmallShop request.SaveShareLiveRoomSmallShopAll) (err error) {

	var smallShop smallShopModel.SmallShop
	err = source.DB().Where("uid = ?", userId).First(&smallShop).Error
	if err != nil {
		err = errors.New("不是小商店店主" + err.Error())
		return
	}
	err, setting := smallShopService.GetShopProductSetting(smallShop.ID)
	if err != nil {
		err = errors.New("未设置定价策略" + err.Error())
		return
	}
	if setting.Values.IsActivityPrice == 0 && setting.Values.IsAgreementPrice == 0 && setting.Values.IsGuidePrice == 0 && setting.Values.IsOriginPrice == 0 {
		err = errors.New("未设置定价策略")
		return
	}
	var shareLiveRooms []model.ShareLiveRoom
	db := source.DB().Model(&model.ShareLiveRoom{}).Joins("left join share_live_room_small_shops as slrss on  slrss.share_live_room_id = share_live_rooms.id")
	db.Where("share_live_rooms.status in (0,1) or share_live_rooms.is_transcribe = 1") //查询直播状态为待直播 和正在直播的，以及有录制文件的（回放文件）

	if saveShareLiveRoomSmallShop.Status == 1 {
		//查询所有未开启的
		db.Where("slrss.status = 0 or slrss.id is null")
	} else {
		//查询所有已开启的
		db.Where("slrss.status = 1").Select("share_live_rooms.*")
	}
	if saveShareLiveRoomSmallShop.Title != "" {
		db.Where("title like '%" + saveShareLiveRoomSmallShop.Title + "%'")
	}
	if saveShareLiveRoomSmallShop.Keyword != "" {
		var userIds []uint
		err = source.DB().Model(&model2.User{}).Where("nick_name like '%"+saveShareLiveRoomSmallShop.Keyword+"%' or username like '%"+saveShareLiveRoomSmallShop.Keyword+"%'").Pluck("id", &userIds).Error
		if err != nil {
			err = errors.New("没有需要修改状态的直播间" + err.Error())
			return
		}
		db.Where("user_id in ?", userIds)
	}

	if saveShareLiveRoomSmallShop.ShareLiveCategoryId != 0 {
		db.Where("share_live_category_id = ?", saveShareLiveRoomSmallShop.ShareLiveCategoryId)
	}

	//通过商品名称筛选
	if saveShareLiveRoomSmallShop.ProductName != "" {
		var shareLiveRoomIds []uint
		err = source.DB().Model(&model3.Product{}).Select("DISTINCT(share_live_room_products.share_live_room_id)").Joins("INNER join share_live_room_products on share_live_room_products.product_id = products.id").Where("products.title like '%"+saveShareLiveRoomSmallShop.ProductName+"%'").Pluck("share_live_room_products.share_live_room_id", &shareLiveRoomIds).Error
		if err != nil {
			err = errors.New("没有需要修改状态的直播间" + err.Error())
			return
		}

		db.Where("share_live_rooms.id in ?", shareLiveRoomIds)
	}
	err = db.Select("share_live_rooms.*").Find(&shareLiveRooms).Error
	if err != nil {
		err = errors.New("没有需要修改状态的直播间" + err.Error())
		return
	}
	var msg = ""
	for _, item := range shareLiveRooms {
		err = ShareLiveRoomSmallShopStart(setting, smallShop, userId, request.SaveShareLiveRoomSmallShop{
			Status:          saveShareLiveRoomSmallShop.Status,
			ShareLiveRoomId: item.ID,
		})
		if err != nil {
			msg += err.Error()
			err = nil
			continue
		}
	}
	if msg != "" {
		err = errors.New(msg)
	}
	return
}

// 修改直播间开关状态
func SaveShareLiveRoomIsOpen(id uint, isOpen int) (err error) {
	if id == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	if isOpen != 0 && isOpen != 1 {
		err = errors.New("请提交有效状态值(0开1关)")
		return
	}

	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", id).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在")
		return
	}

	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", id).Update("is_open", isOpen).Error
	if err != nil {
		err = errors.New("修改失败" + err.Error())
		return
	}
	return
}

// 修改直播间采购端设置
func SaveShareLiveRoomApplicationSetting(req request.SaveShareLiveRoomApplicationSetting) (err error) {
	if req.ShareLiveRoomId == 0 {
		err = errors.New("请提交直播间id")
		return
	}

	// 验证直播间是否存在
	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", req.ShareLiveRoomId).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在")
		return
	}

	// 更新是否指定采购端
	shareLiveRoom.IsSpecifyApplication = req.IsSpecifyApplication

	// 更新采购端id列表
	if req.IsSpecifyApplication == 1 {
		// 如果指定采购端，则设置采购端id列表
		applicationIds := model.ApplicationIds{}
		for _, id := range req.ApplicationIds {
			applicationIds = append(applicationIds, model.ApplicationId(id))
		}
		shareLiveRoom.ApplicationIds = applicationIds
	} else {
		// 如果不指定采购端，则清空采购端id列表
		shareLiveRoom.ApplicationIds = model.ApplicationIds{}
	}

	// 保存修改
	err = source.DB().Model(&shareLiveRoom).Updates(map[string]interface{}{
		"is_specify_application": shareLiveRoom.IsSpecifyApplication,
		"application_ids":        shareLiveRoom.ApplicationIds,
	}).Error

	if err != nil {
		err = errors.New("修改采购端设置失败: " + err.Error())
		return
	}

	return nil
}

// 修改直播间横屏状态
func SaveShareLiveRoomIsLandscape(id uint, isLandscape int) (err error) {
	if id == 0 {
		err = errors.New("请提交直播间id")
		return
	}
	if isLandscape != 0 && isLandscape != 1 {
		err = errors.New("请提交有效状态值(0否1是)")
		return
	}

	var shareLiveRoom model.ShareLiveRoom
	err = source.DB().Where("id = ?", id).First(&shareLiveRoom).Error
	if err != nil {
		err = errors.New("直播间不存在")
		return
	}

	err = source.DB().Model(&model.ShareLiveRoom{}).Where("id = ?", id).Update("is_landscape", isLandscape).Error
	if err != nil {
		err = errors.New("修改失败" + err.Error())
		return
	}
	return
}
